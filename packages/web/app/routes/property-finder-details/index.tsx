import { AppBar } from "@/components/app-bar";
import { Container } from "@/components/container";
import { PropertyImageSwiper } from "@/features/property/property-image-swiper";
import { AppTheme, tabletMQ } from "@/theme";
import { useCallback, useMemo, useState } from "react";
import { useScreenSize } from "@/hooks/media-query-hooks";
import { Link, useLoaderData, useParams } from "@remix-run/react";
import { PropertyAttribute } from "@/features/property/property-attributes";
import { ListingName } from "@/features/property-finder/components/listing-name";
import { ListingPrice } from "@/features/property-finder/components/listing-price";
import { ListingHandoverDate } from "@/features/property-finder/components/listing-handover-date";
import { useMasterPlanLink } from "@/features/master-plan/open-with-default-unit";
import { useFeatureFlag, useFeatureFlagApi } from "@/features/feature-flags";
import { css } from "@emotion/react";
import { PropertyInformation } from "./listing-information";
import { PropertyFinderUnit } from "@/features/property-finder/types";
import { RDSButton } from "@roshn/ui-kit";
import { LocationIcon } from "@/components/icons";

import { LoaderFunctionArgs, json } from "@remix-run/node";
import { PropertyFinderService } from "@/features/property-finder/services";
import { createServerContainer } from "@/utils/create-server-container";

import { navigate404 } from "@/utils/navigate.server";
import PropertyFinderDetailsV4 from "./property-finder-details-v4";
import { ClientOnly } from "remix-utils/client-only";
const aspectRatio = {
	mobile: "1",
	tablet: "16/9",
};

const styles = {
	contentLayout: (theme: AppTheme) =>
		css({
			display: "grid",
			flex: 1,
			gridTemplateColumns: "1fr",
			gridTemplateRows: "auto 1fr",
			paddingBlockStart: theme.spaces.lg,
			[tabletMQ(theme)]: {
				gap: theme.spaces.xl3,
				gridTemplateColumns: "2fr 1fr",
				gridTemplateRows: "1fr",
				height: "80vh",
				overflow: "hidden",
				paddingInline: 0,
			},
		}),
	discoveryWrapper: (theme: AppTheme) =>
		css({
			alignContent: "flex-start",
			display: "flex",
			flexDirection: "column",
			height: "100%",
			overflowY: "auto",
			paddingInline: theme.spaces.md,
		}),
	link: css({
		display: "flex",
		textDecoration: "none",
	}),
	locationButton: (theme: AppTheme) =>
		css({
			[`& span`]: {
				color: theme.colors.common.black,
				paddingBlockStart: "0 !important",
			},
			height: theme.spaces.md,
			letterSpacing: "inherit",
			lineHeight: theme.spaces.lg,
			padding: 0,
			textDecorationColor: theme.colors.common.black,
			textTransform: "capitalize",
			textUnderlineOffset: theme.spaces.xs3,
		}),
	locationContainer: css({
		display: "flex",
	}),
	propertyAttribute: (theme: AppTheme) =>
		css({
			[tabletMQ(theme)]: {
				alignItems: "center",
			},
		}),
	propertyInfo: (theme: AppTheme) =>
		css({
			display: "flex",
			flexDirection: "column",
			[tabletMQ(theme)]: {
				display: "none",
			},
		}),
	propertyInfoWrapper: (theme: AppTheme) =>
		css({
			display: "flex",
			flexDirection: "column",
			gap: theme.spaces.md,
			marginBottom: theme.spaces.lg,
			paddingBlock: theme.spaces.lg,
			[tabletMQ(theme)]: {
				alignItems: "center",
				borderBlock: `1px solid ${theme.colors.grey[200]}`,
				justifyContent: "center",
			},
		}),
	swiperWrapper: (theme: AppTheme) =>
		css({
			aspectRatio: aspectRatio.mobile,
			overflow: "hidden",
			position: "relative",
			"> div": {
				maxWidth: "100%",
			},
			[tabletMQ(theme)]: {
				aspectRatio: aspectRatio.tablet,
				paddingInline: 0,
			},
		}),
};

export const loader = async ({ params }: LoaderFunctionArgs) => {
	const { locale = "", listingId = "" } = params;
	const container = createServerContainer(locale);
	const propertyService = container.get<PropertyFinderService>(
		PropertyFinderService,
	);
	if (!listingId) {
		return navigate404(locale!);
	}
	try {
		const propertyListing = await propertyService.findPropertyById(
			listingId,
			locale!,
		);
		if (!propertyListing) return navigate404(locale!);
		return json({
			propertyListing,
		});
	} catch {
		return navigate404(locale);
	}
};

export default function PropertyFinderDetails() {
	const { data } = useFeatureFlagApi();
	const { propertyListing } = useLoaderData<typeof loader>();
	return !data?.enableBrokerageFeature ? (
		<ClientOnly>
			{() => {
				return <PropertyFinderDetailsV3 propertyListing={propertyListing} />;
			}}
		</ClientOnly>
	) : (
		<ClientOnly>
			{() => {
				return <PropertyFinderDetailsV4 propertyListing={propertyListing} />;
			}}
		</ClientOnly>
	);
}

function PropertyFinderDetailsV3({
	propertyListing,
}: {
	propertyListing: PropertyFinderUnit;
}) {
	const [openFullscreenDialog, setOpenFullscreenDialog] = useState(false);
	const [latestHandoverDateFlag] = useFeatureFlag("latestHandoverDateFlag");
	const images = useMemo(
		() =>
			propertyListing.media?.map(({ MediaURL }) => ({ url: MediaURL })) ?? [],
		[propertyListing.media],
	);
	const { isTablet } = useScreenSize();
	const { locale }: any = useParams();
	const onOpenFullscreenDialog = useCallback(() => {
		if (images.length === 0) return;
		setOpenFullscreenDialog(true);
	}, [images.length === 0]);

	// const onCloseFullscreenDialog = useCallback(() => {
	// 	setOpenFullscreenDialog(false);
	// }, []);

	const masterPlanLink = useMasterPlanLink({
		communityName: propertyListing.communityName?.[locale].toLowerCase() ?? "",
		defaultUnitId: propertyListing.unitCode,
		projectName:
			propertyListing.projectName?.toLowerCase().replaceAll(/\s+/g, "_") ?? "",
		zoneId: propertyListing.neighborhood?.toLowerCase() ?? "",
	});

	const locationButton = (
		<RDSButton
			variant="tertiary"
			css={styles.locationButton}
			text={[
				propertyListing.city?.[locale],
				propertyListing.communityName?.[locale],
			]
				.map((value) => value?.trim() ?? "")
				.filter((value) => value)
				.join(" - ")}
		/>
	);
	return (
		<>
			<AppBar />
			<Container css={styles.contentLayout}>
				<div css={styles.swiperWrapper}>
					<PropertyImageSwiper
						aspectRatio={aspectRatio}
						data={images}
						effect={isTablet ? "fade" : "slide"}
						navigation={isTablet}
						onImageClick={onOpenFullscreenDialog}
					/>
				</div>
				<div css={styles.discoveryWrapper}>
					<div css={styles.propertyInfoWrapper}>
						<div css={styles.propertyInfo}>
							<ListingName data={propertyListing} variant="subtitleS" />
							<ListingPrice data={propertyListing} />
						</div>
						{latestHandoverDateFlag && (
							<ListingHandoverDate data={propertyListing} />
						)}
						<div
							css={styles.locationContainer}
							title={propertyListing.unparsedAddress}
						>
							<LocationIcon />
							{propertyListing.enableDigitalSelling ? (
								<Link to={masterPlanLink} css={styles.link}>
									{locationButton}
								</Link>
							) : (
								locationButton
							)}
						</div>
						<PropertyAttribute
							css={styles.propertyAttribute}
							attributes={{
								bathrooms: propertyListing.numberOfBathrooms ?? 0,
								bedrooms: propertyListing.numberOfBedrooms ?? 0,
								note: propertyListing.note ?? "",
								plotArea: propertyListing?.plotArea?.toString() ?? "0",
							}}
						/>
					</div>
					<PropertyInformation data={propertyListing} />
				</div>
			</Container>
		</>
	);
}
