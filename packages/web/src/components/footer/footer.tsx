import { useFooterQuery } from "@/features/footer";
import {
	FooterLink,
	FooterMainLink,
	FooterSocialLink,
	GetFooterResponse,
} from "@/services/footer";
import { useParams } from "@remix-run/react";

import { RDSFooter } from "@roshn/ui-kit";
import { useTranslation } from "react-i18next";

const Footer = () => {
	const { locale, communityName } = useParams();
	const { data } = useFooterQuery("HA-Footer", locale);
	const { t } = useTranslation();

	const footerData =
		data?.[0]?.attributes ?? ({} as GetFooterResponse["data"]["attributes"]);

	const allCertificationImages =
		footerData?.certificationsCompliance?.image || [];

	const shouldShowImage = (
		altText: string | null,
		community: string | undefined,
	) => {
		if (!altText) return false;

		const altTextArray = altText
			.split(",")
			.map((text) => text.trim().toLowerCase());

		const excludeMatch = altText.match(/exclude:\s*\[(.*?)\]/);
		const excludedCommunities = excludeMatch
			? excludeMatch[1].split(",").map((text) => text.trim().toLowerCase())
			: [];

		if (excludedCommunities.includes(community?.toLowerCase() || "")) {
			return false;
		}
		if (altTextArray.includes("global")) {
			return true;
		}
		return community ? altTextArray.includes(community.toLowerCase()) : false;
	};

	const filteredCertificationImages = allCertificationImages.filter(
		(image: any) => shouldShowImage(image?.altText, communityName),
	);
	const footerArgs = {
		showDivider: true,
		walletLabel: footerData?.downloadApps?.title || "Download ROSHN Super App",
		walletImages: (footerData?.downloadApps?.image || []).map((image: any) => ({
			appName: image?.title?.includes("apple")
				? "Apple App Store"
				: "Google Play Store",
			href: image?.title || "#",
			icon: {
				src: image?.src?.data?.attributes?.url || "",
				altText: image?.altText || "",
			},
		})),
		complianceLabel:
			footerData?.certificationsCompliance?.title ||
			"Certifications & Compliance",
		complianceImages: filteredCertificationImages.map((image: any) => ({
			href: image.title,
			src: image?.src?.data?.attributes?.url || "",
			altText: image?.altText || "Certification logo",
		})),
		roshnGroupLogo: {
			href: "https://roshn.sa/en/terms-condition",
			src: footerData?.content?.logo || "",
			altText: "Roshn Group Logo",
		},
		roshnFooterLinks: (footerData?.content?.legalData?.links || []).map(
			(link: FooterLink) => ({
				label: link?.text || "",
				href: link?.href || "#",
			}),
		),
		roshnSocialMediaLinks: {
			label: t("layout.footer.followUs"),
			icons: (footerData?.content?.socialLinks || []).map(
				(social: FooterSocialLink) => ({
					iconType: social?.link?.text?.toLowerCase() || "",
					href: social?.link?.href || "",
				}),
			),
		},
		roshnFooterBrand: {
			pifLogo: {
				src:
					footerData?.content?.footerImages?.[0]?.image?.src?.data?.attributes
						?.url || "",
				altText: "",
			},
			copyrightText: footerData?.content?.legalData?.copyrightText || "",
			commercialRegText:
				footerData?.content?.legalData?.crNumber || "CR No. 1010449563",
			unifiedNationalNo: t("layout.footer.unifiedNationalNo"),
			links: (footerData?.content?.mainLinks || []).map(
				(link: FooterMainLink) => ({
					label: link?.title || "",
					href: link?.link || "",
				}),
			),
		},
	};

	return <RDSFooter appLinkProps={footerArgs} />;
};

export default Footer;
