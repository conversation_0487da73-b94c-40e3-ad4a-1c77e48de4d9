export const STATIC_PAGE_SLUG = {
	DISCLAIMER: "disclaimer",
	PRIVACY_POLICY: "privacy-and-policy",
	TERM_AND_CONDITIONS: "terms-and-conditions",
	TERM_AND_CONDITIONS_PAYMENT: "terms-and-conditions-of-electronic-payment",
	RAFFLE_TERMS_AND_CONDITIONS: "raffle-terms-and-conditions",
};

export const PROPERTY_STATUS: Record<string, string> = {
	AVAILABLE: "AVAILABLE",
	LOW_AVAILABILITY: "LOW_AVAILABILITY",
	UNAVAILABLE: "UNAVAILABLE",
};

export const UNIT_STATUS: Record<string, string> = {
	AVAILABLE: "Available",
	HOLD: "Hold",
	NOT_AVAILABLE: "Not Available",
	RESERVED: "Reserved",
	SOLD: "Sold",
	UNRELEASED: "Unreleased",
};

export const MINIMUM_SALARY_FOR_RESERVATION = 9000;

export const HOME_FINANCE_CONFIG = {
	FLAT_RATE: 0.04,
	MAX_DOWN_PAYMENT_PERCENTAGE: 90,
	MAX_OBLIGATION: 1_000_000,
	MAX_SALARY: 1_000_000,
	MAX_YEARS: 30,
	MIN_DOWN_PAYMENT_PERCENTAGE_NO_SUPPORTED: 30,
	MIN_DOWN_PAYMENT_PERCENTAGE_WITH_SUPPORTED: 10,
	MIN_OBLIGATION: 0,
	MIN_SALARY: 3000,
	MIN_YEARS: 2,
	SALARY_THRESHOLD: 15_000,
};

export const COMMUNITY_NAMES = {
	ALAROUS: "alarous",
	ALMANAR: "almanar",
	ALFULWA: "alfulwa",
	ALDANAH: "aldanah",
	ROSHN: "roshn",
	SEDRA: "sedra",
	WAREFA: "warefa",
};

export const COMMUNITY_ID = {
	ALMANAR: "300000732129736",
};

export const PROJECT_NAMES = {
	ALAROUS_1A: "alarous_1a",
	ALMANAR_1: "almanar_1",
	SEDRA_3: "sedra_3",
	SEDRA_2A: "sedra_2a",
	SEDRA_4A: "sedra_4a",
	WAREFA_1: "warefa_1",
	ALDANAH_1: "aldanah_1",
	SEDRA_5: "sedra_5",
};

export const COMMUNITY_PROJECT_MAP = {
	[COMMUNITY_NAMES.ALAROUS]: [PROJECT_NAMES.ALAROUS_1A],
	[COMMUNITY_NAMES.SEDRA]: [
		PROJECT_NAMES.SEDRA_3,
		PROJECT_NAMES.SEDRA_4A,
		PROJECT_NAMES.SEDRA_2A,
		PROJECT_NAMES.SEDRA_5,
	],
	[COMMUNITY_NAMES.WAREFA]: [PROJECT_NAMES.WAREFA_1],
	[COMMUNITY_NAMES.ALMANAR]: [PROJECT_NAMES.ALMANAR_1],
};

export const CITY_NAMES = {
	JEDDAH: "jeddah",
	RIYADH: "riyadh",
	DAMMAM: "dammam",
};

export const HOME_PURCHASE_METHOD = {
	CASH: "CASH",
	HOME_FINANCE: "HOME_FINANCE",
};

export const ABOUT_US_URL = "https://www.roshn.sa/ar/about-us";
export const CONTACT_US_URL = (language: string) =>
	language === "ar"
		? "https://www.roshn.sa/ar/contact-us"
		: "https://www.roshn.sa/en/contact-us";

export const PAYMENT_STATUS = {
	PAID_FULLY: "PAID_FULLY",
	PAYMENT_FAILED: "PAYMENT_FAILED",
	UNPAID: "UNPAID",
};

export const PROPERTY_EXPLORE = {
	EXTERIOR_TAB: "ex-3d",
	INTERIOR_TAB: "in-3d",
};

export const SUPPORTED_LANGUAGES = ["en", "ar"];

export const RESERVATION_FEE = 5000; // TODO: dynamic get from unit info
export const FLOOR = {
	FF: "1F",
	GF: "GF",
	SF: "2F",
};

export const PROPERTY_TYPE = {
	APARTMENT: "apartment",
	DUPLEX: "duplex",
	TOWNHOUSE: "townhouse",
	VILLA: "villa",
	PREMIUM_VILLA: "PREMIUM VILLA",
};

export const CS_PHONE_NUMBER = "+920022288";

export const RESERVATION_PAYMENT_TIMEOUT = 1000 * 60 * 60; // 1H
export const RESERVATION_DOWNPAYMENT_TIMEOUT_CASH = 1000 * 60 * 60 * 24 * 7; // 7D
export const RESERVATION_DOWNPAYMENT_TIMEOUT_HOME_FINANCE =
	1000 * 60 * 60 * 24 * 15; // 15D

export const ZONE_STATUS = {
	RELEASED: "Available",
	SOLD_OUT: "Sold",
	UNRELEASED: "Unreleased",
};

// Represent a placeholder or default value ("any"). Localize this string for different languages if necessary.
export const DEFAULT_CHOICE = "any";
export const BROKERAGE = {
	RESO_PROPERTIES: "reso-properties",
	BROKERAGE_PROPERTIES: "brokerage-properties",
};

export const SA_IBAN_CHARACTERS = 24;
