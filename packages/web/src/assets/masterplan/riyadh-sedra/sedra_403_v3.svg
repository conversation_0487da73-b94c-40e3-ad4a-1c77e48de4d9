<svg width="560" height="538" viewBox="0 0 560 538" fill="none" xmlns="http://www.w3.org/2000/svg">
 <style>
    /* Scoped hover effect only for this SVG */
    #SEDRA\ 403\ \-\ W\ wadi\ \-\ UPDATED:hover path {
      opacity: 2 !important; /* doubles (0.8 → 1.6, clamped to 1) */
    }
  </style>
<g id="SEDRA 403 - W wadi - UPDATED" filter="url(#filter0_d_39_3816)">
<path id="Vector" opacity="0.8" d="M447.548 6.18164L465.006 6.18262H466.208L466.771 7.24414L483.666 39.0723L483.682 39.1016L483.695 39.1309L510.941 94.7744L543.947 157.134L544.869 158.876L543.141 159.823L298.534 293.843L303.388 308.141L356.376 279.793L358.216 278.81L359.121 280.688L375.984 315.678L376.807 317.384L375.146 318.298L16.4961 515.729L13.8516 512.893C17.4387 507.339 22.2252 495.089 27.4199 479.327C32.5842 463.658 38.0748 444.765 43.1055 426.122C48.1356 407.481 52.7 389.112 56.0127 374.496C57.6693 367.187 59.0103 360.828 59.9395 355.85C60.8805 350.807 61.3667 347.342 61.3799 345.716L61.3926 344.222L62.8281 343.81C96.3007 334.188 148.508 319.425 192.887 305.893C215.079 299.126 235.283 292.675 250.194 287.336C257.656 284.664 263.751 282.285 268.094 280.295C270.27 279.298 271.956 278.421 273.139 277.677C273.73 277.304 274.155 276.99 274.44 276.737C274.489 276.694 274.53 276.653 274.565 276.618V242.623L276.066 242.236L290.154 238.608L294.736 230.248L284.48 224.27L283.038 223.429L283.607 221.86L298.985 179.431L298.987 179.426L307.041 157.383L312.062 143.459L312.063 143.456L314.458 136.84L322.246 111.784L328.951 89.7646L329.378 88.3643L330.842 88.3477L364.699 87.9629C365.384 85.9414 367.251 84.5706 369.222 83.5381C371.876 82.1476 375.719 80.8505 380.073 79.2646C388.916 76.0436 400.832 71.314 413.313 61.3643L420.479 54.7891L420.482 54.7861L424.705 50.9268L424.723 50.9111L424.739 50.8955L426.89 49.0195C426.831 47.8539 426.981 46.5524 427.24 45.2217C427.583 43.4665 428.15 41.4846 428.865 39.3916C430.296 35.2031 432.365 30.4231 434.575 25.8623C436.787 21.2975 439.162 16.9075 441.22 13.4912C442.247 11.7852 443.21 10.2986 444.044 9.14648C444.46 8.57131 444.86 8.05896 445.231 7.63574C445.582 7.2366 445.99 6.82049 446.429 6.52441L446.936 6.18164H447.548Z" fill="#D8DCCE" stroke="#909B75" stroke-width="4"/>
<g id="WADI" filter="url(#filter1_n_39_3816)">
<path d="M496.035 69.6156L482.753 41.4121L475.691 50.6234L471.085 53.0899L464.025 59.0095C458.376 60.5881 456.446 66.7379 456.187 69.6156L449.818 72.822L429.978 86.3878L419 93.0479L414.029 100.557H420.581C418.397 102.226 415.555 104.562 421.663 100.557C427.77 96.5521 440.509 95.2229 446.115 95.0589C451.539 94.1192 463.289 81.1909 468.486 74.8443C475.004 66.6526 474.817 72.7964 473.909 76.8922L471.085 83.7186L458.995 97.8414L447.081 106.928L443.404 114.298L431.654 119.898L409.109 131.362L401.578 138.314L409.109 135.154L411.622 141.158L415.7 135.154L421.663 131.362H426.37L437.981 124.726L441.118 128.518L437.981 135.154L441.118 138.314L447.081 128.518V121.882V114.298L451.743 108.044H460.339L471.085 95.0589L475.691 97.8414L482.753 89.4939C487.666 80.3424 493.273 68.3795 496.035 69.6156Z" fill="#B7BEA6"/>
</g>
<g id="WADI_2" filter="url(#filter2_n_39_3816)">
<path d="M336.327 107.778L327.214 101.738L330.869 90.3507L368.787 89.6581L342.678 96.3449L338.091 101.738L339.502 107.778H336.327Z" fill="#B7BEA6"/>
</g>
</g>
<defs>
<filter id="filter0_d_39_3816" x="-8" y="0" width="572" height="557" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="6"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_39_3816"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_39_3816" result="shape"/>
</filter>
<filter id="filter1_n_39_3816" x="401.578" y="41.4121" width="94.457" height="99.7461" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feTurbulence type="fractalNoise" baseFrequency="0.25 0.25" stitchTiles="stitch" numOctaves="3" result="noise" seed="2628" />
<feColorMatrix in="noise" type="luminanceToAlpha" result="alphaNoise" />
<feComponentTransfer in="alphaNoise" result="coloredNoise1">
<feFuncA type="discrete" tableValues="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 "/>
</feComponentTransfer>
<feComposite operator="in" in2="shape" in="coloredNoise1" result="noise1Clipped" />
<feFlood flood-color="rgba(0, 0, 0, 0.25)" result="color1Flood" />
<feComposite operator="in" in2="noise1Clipped" in="color1Flood" result="color1" />
<feMerge result="effect1_noise_39_3816">
<feMergeNode in="shape" />
<feMergeNode in="color1" />
</feMerge>
</filter>
<filter id="filter2_n_39_3816" x="145.033" y="25.7021" width="269.621" height="211.411" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feTurbulence type="fractalNoise" baseFrequency="2 2" stitchTiles="stitch" numOctaves="3" result="noise" seed="7619" />
<feColorMatrix in="noise" type="luminanceToAlpha" result="alphaNoise" />
<feComponentTransfer in="alphaNoise" result="coloredNoise1">
<feFuncA type="discrete" tableValues="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 1 1 1 1 1 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 "/>
</feComponentTransfer>
<feComposite operator="in" in2="shape" in="coloredNoise1" result="noise1Clipped" />
<feFlood flood-color="rgba(0, 0, 0, 0.25)" result="color1Flood" />
<feComposite operator="in" in2="noise1Clipped" in="color1Flood" result="color1" />
<feMerge result="effect1_noise_39_3816">
<feMergeNode in="shape" />
<feMergeNode in="color1" />
</feMerge>
</filter>
</defs>
</svg>
