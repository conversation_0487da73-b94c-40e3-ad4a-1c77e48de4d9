import { GenericMap, HoldStatus } from "./types";

/* eslint-disable sort-keys-fix/sort-keys-fix */
export const FILTER_CHOICES = {
	customerType: ["all", "leadsOnly", "customersOnly"],
	leadRank: ["all", "cool", "hot", "warm"],
	paymentMethod: ["all", "cash", "homeFinance"],
	propertyStatus: [
		"all",
		"draft",
		"sold",
		"booked",
		"executed",
		"reservationCancelled",
		"contractCanceled",
		"reservationCancellationInProgress",
		"contractCancellationInProgress",
	],
} as const;

export const FILTER_VALUES_CRM_MAP: GenericMap = {
	cash: "SELF_FUNDED",
	homeFinance: "HOME_FINANCE",
	assignToHomeFinance: "Assign to Home Finance",
	followup: "Follow Up",
	finalisePayment: "Finalize Payment",
	updateInformation: "Update Information",
	scheduleMeeting: "Schedule Meeting",
	cancelForm: "Send Cancellation Form",
	alarous: "AL_AROUS",
};

export const SALES_METHOD: GenericMap = {
	SELF_FUNDED: "cash",
	HOME_FINANCE: "homeFinance",
};
export const STATE_TO_CRM_MAPPING: GenericMap = {
	interestedCommunities: "Leads[].CommunityOfInterest",
	paymentMethod: "Leads[].SalesMethod",
	leadRank: "Leads[].Rank",
	nextAction: "Leads[].NextAction",
	unitPreferences: "Leads[].Typology",
	unitType: "Leads[].PreferredUnitType", //looks for nested array object 1 level deep
	leadStatus: "Leads[].StatusCode",
};
export const FOLLOW_UP_FILTER_CHOICES = {
	paymentMethod: ["all", "cash", "homeFinance"],
	unitType: ["all", "villa", "duplex", "townhouse"],
	interestedCommunities: [
		"all",
		"alarous",
		"warefa",
		"sedra",
		"almanar",
		"aldanah",
		// "marafy",
	],
	// TODO : uncomment this once readiness is done
	leadRank: ["all", "hot", "cool", "warm"],
	unitPreferences: [
		"all",
		"C10",
		"C20",
		"DP1",
		"DP2",
		"DP4",
		"TH1",
		"TH3",
		"VL1",
		"VL2",
		"VL3",
	],
	// TODO : uncomment this once nextAction is done
	nextAction: [
		"all",
		"call",
		"followup",
		"assignToHomeFinance",
		"finalisePayment",
		"updateInformation",
		// "writeEmail",
		"scheduleMeeting",
		"cancelForm",
	],
	leadStatus: ["all", "qualified", "unqualified", "converted", "retired"],
} as const;

export enum HoldStatusFilter {
	"expired" = "expired",
	"inProgress" = "inProgress",
	"srnCreated" = "srnCreated",
}

export const HOLD_STATUS_FILTER_CHOICES = {
	holdStatus: [
		HoldStatusFilter.expired,
		HoldStatusFilter.inProgress,
		HoldStatusFilter.srnCreated,
	],
};

export const defaultFollowUpFilter = {
	interestedCommunities: ["all"],
	nextAction: ["all"],
	paymentMethod: "all",
	leadRank: ["all"],
	unitPreferences: ["all"],
	unitType: "all",
};

export enum Sort {
	Asc = "asc",
	Desc = "desc",
}

export enum EditLeadFields {
	CustomerBudget = "customer-budget",
	FollowUpList = "follow-up-list",
	InterestedCommunities = "interested-communities",
	LeadRank = "lead-rank",
	LeadStatus = "lead-status",
	NoOfBathRooms = "no-of-bathrooms",
	NoOfBedRooms = "no-of-bedrooms",
	PaymentMethod = "payment-method",
	PropertyPreferences = "property-preferences",
	Readiness = "readiness",
	RetireComments = "retirement-detail",
	RetireLead = "retire-lead",
	RetirementReason = "retirement-reason",
	SakaniBeneficiary = "sakani-beneficiary",
	SalaryRange = "salary-range",
	UnitPreferences = "unit-preferences",
}

export enum EditFollowUpListFields {
	LeadCategory = "lead-category",
	LeadRank = "lead-rank",
	LeadStage = "lead-stage",
	NextAction = "next-action",
	Notes = "notes",
	Time = "time",
}

export const mutationKeyMap = {
	Budget: "BUDGET",
	CommunityOfInterest: "COMMUNITY_OF_INTEREST",
	NumberOfBedrooms: "NUMBER_OF_BEDROOMS",
	Typology: "PROJECT_TYPOLOGY",
	Rank: "MKL_LEAD_RANK",
	SalaryRange: "SALARYRANGE",
	SalesMethod: "SALES_REGISTER_SALES_METHOD",
	StatusCode: "MKL_LEAD_STATUS",
	PreferredUnitType: "UNITTYPE",
	RetireReason: "MKL_RETIRE_REASON_CD_SETID",
	LeadCategory: "LEADCATEGORY",
	//NumberOfBathRooms: "-",
	//NextAction: "-",
	//Readiness:"-"
	//UseSakani:"-"
	//FollowupList:"-"
};

export const LeadStatuses = {
	CONVERTED: "CONVERTED",
	RETIRED: "RETIRED",
	QUALIFIED: "QUALIFIED",
	UNQUALIFIED: "UNQUALIFIED",
};

export const SalesMethodMap = {
	homeFinance: "Home Finance",
	cash: "Cash (Self-Funded)",
};

export const LeadCategoryMap = {
	RENT_READY_UNIT: "Brokerage - Tenant",
	BUY_READY_UNIT: "Brokerage - Buyer",
	PROPERTY_PURCHASE: "Property Purchase",
	LEASE_UNIT: "Brokerage - Home Owner (Lease)",
	SELL_UNIT: "Brokerage - Home Owner (Resell)",
};

export const typologyOptions = [
	{ label: "C10", value: "C10" },
	{ label: "DP1", value: "DP1" },
	{ label: "DP2", value: "DP2" },
	{ label: "DP4", value: "DP4" },
	{ label: "TH1", value: "TH1" },
	{ label: "TH3", value: "TH3" },
	{ label: "VL1", value: "VL1" },
	{ label: "VL2", value: "VL2" },
	{ label: "VL3", value: "VL3" },
];

export const PROJECT_MAP = {
	"SEDRA 4": "sedra4",
	"SEDRA 2": "sedra_2a",
	"SEDRA 3": "sedra_3",
	"SEDRA 4A": "sedra_4a",
	"WAREFA 1": "warefa_1",
	"ALAROUS 1A": "alarous_1a",
	"ALMANAR 1": "almanar_1",
	"SEDRA 5": "sedra_5",
};

export enum LeadStatus {
	Converted = "CONVERTED",
	Qualified = "QUALIFIED",
	Retired = "RETIRED",
	Unqualified = "UNQUALIFIED",
}

export const holdStatusMap = {
	[HoldStatusFilter.expired]: HoldStatus.EXPIRED,
	[HoldStatusFilter.inProgress]: HoldStatus.IN_PROGRESS,
	[HoldStatusFilter.srnCreated]: HoldStatus.SRN_CREATED,
};

export const propertyModifierFields = ["driverRoom", "maidRoom", "laundryRoom"];

export const SA_REGISTRATION_QUERY_STRING = "sa";
