import type { StrapiMedia, StrapiMediaFormat, StrapiSEO } from "@roshn/shared";

export type HomeProperty = {
	bathrooms: number;
	bedrooms: number;
	squareSpace: number;
};

export type PropertyInfo = {
	additionalRooms?: number;
	bathroom?: number;
	bedroom?: number;
	balcony?: string;
	builtUpArea?: string;
	driverRoom?: number;
	facade?: string;
	grossFloorArea?: string;
	landArea?: string;
	maidRoom?: number;
	note?: string;
	parking?: number;
	plotArea?: string;
	roofTerrace?: boolean;
	typologyGroup: string;
};

export type Amenity = {
	icon: string;
	name: string;
	plainTitle?: string;
};

export type PaymentPlanRecord = {
	milestone: string;
	percent: number;
};

export type PropertyImage = {
	imageSize?: {
		[format: string]: StrapiMediaFormat;
	};
	label?: {
		[key: string]: string;
	};
	url: string;
};

export type InstallmentPlan = {
	firstPaymentAmount: number;
	plan: {
		amount: number;
		constructionProgress: number;
		mileStone: number;
		percentage: number;
	}[];
};

export type PropertyFacade = {
	displayName: string;
	exteriorPreview?: StrapiMedia;
	imageSetFor3D: StrapiMedia[];
	name: string;
};

export type Kitchen = {
	data: StrapiMedia[];
};

// Type for the `formats` of the images
export type ImageFormats = {
	[key: string]: ImageFormat;
};

// Type for each format of the images
export type ImageFormat = {
	ext: string;
	hash: string;
	height: number;
	mime: string;
	name: string;
	path: null;
	size: number;
	url: string;
	width: number;
};

export type Property = {
	community?: {
		amenities?: Amenity[];
		city?: string;
		handoverDate?: string;
		name?: string;
	};
	communityName?: string;
	communityPhases?: {
		data: {
			id: number;
			attributes: {
				phaseName: string;
				createdAt: string;
				updatedAt: string;
				publishedAt: string;
			};
		}[];
	};
	description?: string;
	// array of image for exteriorPreview
	// each image is for separate facade
	exteriorPreview?: PropertyImage[];
	facades?: PropertyFacade[];

	floorPlanLink?: string;
	id: string;
	// combination of exteriorPreview + topViewImages + genericImage
	images: PropertyImage[];
	information: PropertyInfo;
	isLowAvailability?: boolean;
	keywords?: string[];
	kitchenColors?: Kitchen;
	paymentPlan?: InstallmentPlan;
	position?: {
		label: string;
		link: string;
	};
	price: number;
	project: string;
	seo?: StrapiSEO;
	status: string;
	title?: string;
	// array of image for topViewImages
	// each image is for a separete floor
	topViewImages?: PropertyImage[];
	type: string;
	// fields for full property detail
	typology?: string;
	typologyGroup?: string;
	updatedAt?: string;
	virtualTourUrl?: string;
};

export type UnitPrice = {
	amountRETT: number;
	price: number;
	priceIncludingRETT: number;
};

export type ReservationPrice = {
	amountRETT: number;
	price: number;
	priceIncludingRETT?: number;
};

export type UnitBankInfo = {
	bankAccountName: string;
	bankAccountNumber: string;
	bankName: string;
};

export type PropertyUnit = {
	// Price at the reservation time
	bankInfo: UnitBankInfo;
	community: string;
	currentProgress: number;
	estDeliveryDate?: string;
	exteriorPreview?: PropertyImage;
	id: string;
	neighborhood: string;
	project: string;
	propertyName: string;
	// Current unit price
	reservationPrice?: ReservationPrice;
	subPhase?: string;
	//PROPERTY_STATUS
	// CMS's Property Group Slug
	typology: string;
	unitCategory: string;
	unitCode: string;
	unitInfo: PropertyInfo;
	unitName: string;

	unitNumber: string;
	unitPrice: UnitPrice;
	unitStatus: string;

	unitType: string;
	updateAt: string;
	orientation: string;
	unitCodeId: string;
};
