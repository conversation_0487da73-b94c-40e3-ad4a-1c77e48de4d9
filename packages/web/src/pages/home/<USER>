import { useTheme, css, Global } from "@emotion/react";
import { useTranslation } from "react-i18next";
import { AppPaths, useAppPath, useAppPathGenerator } from "@/routes";
import "react-multi-carousel/lib/styles.css";
import { useEffect, useState, lazy, Suspense, useCallback } from "react";
import {
	generatePath,
	useNavigate,
	useParams,
	useSearchParams,
} from "@remix-run/react";
import HomeStaticBanner from "@roshn/ui-kit/components/legacy/organisms/HomeCommunityBanner/HomeStaticBanner.js";

// Lazy load below-the-fold components
const CommunityMap = lazy(
	() =>
		import(
			"@roshn/ui-kit/components/legacy/organisms/CommunityMap/CommunityMap.js"
		),
);
const HeroBrokerageBanner = lazy(
	() =>
		import(
			"@roshn/ui-kit/components/legacy/organisms/HeroBrokerageBanner/HeroBrokerageBanner.js"
		),
);
const CardCarouselComponent = lazy(() =>
	import("./card-carousel").then((module) => ({
		default: module.CardCarouselComponent,
	})),
);
import { COMMUNITY_NAMES } from "@/constants";
import { authModalSlice } from "@/store/global-modal-slices";
import { RegisterInterestDialogWrapper } from "@/features/register-interest/register-interest";
import { useAppDispatch } from "@/store";
import { AuthService, useCurrentUser, useInjection } from "@roshn/shared";
import { useFeatureFlagApi } from "@/features/feature-flags";
import { usePrevious } from "@/hooks/use-previous-hook";
import PendingActionModal from "@/features/thank-u-modal/thank-u-modal";

import { useSignatureInput } from "@/features/signature-input/context/signature-input-context";
import { useNavigateRedirect } from "@/hooks/use-navigate-redirect";
import { useSelector } from "react-redux";
import { AppState } from "@/store";
import { VirtualAppointment } from "@/features/virtual-appointment/virtual-appointment";
import { appointmentTypeProps } from "@/features/virtual-appointment/constant";
import {
	FullScreenLoader,
	NationalDayVideo,
	shouldShowNationalDayVideo,
} from "@/features/loading";
import { useScreenSize } from "@/hooks/media-query-hooks";
const DigitalExperienceSection = lazy(
	() => import("./digital-experience-section"),
);

// Lazy load footer
const Footer = lazy(() => import("@/components/footer/footer"));

const styles = {
	bannerOverride: (isTablet: boolean) =>
		css({
			"& > div": {
				"@media (max-width: 1023px)": {
					minHeight: "auto !important",
				},
			},
			"& > div > img": {
				background: "transparent",
				top: "5%",
				position: "relative",
				objectFit: "contain",
				width: "80%",
				left: "10%",
			},
			background:
				"linear-gradient(137deg, rgba(0, 0, 0, 0.08) 36.32%, rgba(0, 0, 0, 0.32) 47.69%), #01343A",
		}),
	bannerWrapper: (marginTop: string) =>
		css({
			marginTop: marginTop,
			p: {
				margin: 0,
			},
		}),
	communitiesSection: css({
		scrollMarginTop: "3.125rem",
	}),
};

export const HomepageV4 = () => {
	const theme = useTheme();
	const { t } = useTranslation();
	const isMobile = theme?.rds?.device === "mobile";
	const isDesktop = useScreenSize().isDesktop;
	const visitSaleCenter = useAppPath(AppPaths.visitSaleCenter);
	const { locale = "" } = useParams();
	const navigateRedirect = useNavigateRedirect();
	const isRebrandedFlag = import.meta.env.VITE_FF_REBRANDED === "TRUE";
	const navigate = useNavigate();
	const dispatch = useAppDispatch();
	const isLoggedIn = useInjection<AuthService>(AuthService).useStore().signedIn;
	const [isModalOpen, setIsModalOpen] = useState(false);
	const [checkState, setCheckState] = useState(false);
	const [community, setCommunity] = useState("");
	const userData = useCurrentUser();
	// @ts-ignore - Ignoring type error for prevUserData
	const prevUserData = usePrevious(userData);
	const [searchParams] = useSearchParams();
	const resigningDocs = searchParams.get("resigningDocs");
	const actionAppointment = searchParams.get("actionAppointment");
	const { data: featureFlagAPI, isLoading } = useFeatureFlagApi();
	const resignDocumentsFeatureFlag = featureFlagAPI?.resignDocuments;
	const enableAlmanarCommunity = featureFlagAPI?.enableAlmanarCommunity;
	const enableAldanah = featureFlagAPI?.enableAldanah;
	const enableNewCommunitiesPage = featureFlagAPI?.enableNewCommunitiesPage;
	const enableNewHomePage = featureFlagAPI?.enableNewHomePage;
	const enableNationalDayContent = featureFlagAPI?.enableNationalDayContent;

	const [openPendingActionModal, setOpenPendingActionModal] = useState(false);
	const enableVirtualAppointment = featureFlagAPI?.enableVirtualAppointment;
	const topHeight = useSelector((state: AppState) => state.topHeader.topHeight);
	const marginTop =
		topHeight === "128px" ? "128px" : topHeight === "72px" ? "74px" : "0px";
	const showDocumentModal = useSignatureInput().showDocumentModal;
	const [
		shouldOpenVirtualAppointmentAfterLogin,
		setShouldOpenVirtualAppointmentAfterLogin,
	] = useState(false);
	const [openVirtual, setOpenVirtual] = useState(false);
	const [appointmentType, setAppointmentType] = useState("");
	const [showNationalDayVideo, setShowNationalDayVideo] = useState(false);
	const [isLoaderComplete, setIsLoaderComplete] = useState(false);
	const generateAppPath = useAppPathGenerator();
	const goToSelectedCommunity = useCallback(
		(communityName: string) => {
			navigate(
				generatePath(AppPaths.masterPlanCity, { communityName, locale }),
			);
		},
		[locale, navigate, generatePath],
	);
	const handleOpenModal = () => {
		if (isLoggedIn) {
			setIsModalOpen(true);
		} else {
			dispatch(authModalSlice.actions.openModal());
		}
		setCheckState(true);
	};
	const handleSaleCenter = () => {
		navigateRedirect(generatePath(AppPaths.visitSaleCenter, { locale }));
	};
	const handleOpenVirtualAppointment = (appointmentTypeProps: string) => {
		// Prevent double-click by checking if already opening
		if (openVirtual && appointmentType === appointmentTypeProps) {
			return;
		}

		if (isLoggedIn) {
			setOpenVirtual(true);
			setAppointmentType(appointmentTypeProps);
		} else {
			dispatch(authModalSlice.actions.openModal());
			setShouldOpenVirtualAppointmentAfterLogin(true);
			setAppointmentType(appointmentTypeProps);
			setOpenVirtual(false);
		}
	};
	const handleCloseVirtualAppointment = () => {
		setOpenVirtual(false);
		setCommunity("");
		setAppointmentType("");
	};

	const handleVisitSalesCenter = (communityName: string) => {
		if (isLoggedIn) {
			setOpenVirtual(true);
			setAppointmentType(appointmentTypeProps.PHYSICAL);
			setCommunity(communityName);
		} else {
			dispatch(authModalSlice.actions.openModal());
			setShouldOpenVirtualAppointmentAfterLogin(true);
			setAppointmentType(appointmentTypeProps.PHYSICAL);
			setCommunity(communityName);
			setOpenVirtual(false);
		}
	};

	useEffect(() => {
		if (
			shouldOpenVirtualAppointmentAfterLogin &&
			isLoggedIn &&
			prevUserData?.crmLeadCreated
		) {
			setOpenVirtual(true);
			setCheckState(false);
			setShouldOpenVirtualAppointmentAfterLogin(false);
		}
	}, [
		isLoggedIn,
		shouldOpenVirtualAppointmentAfterLogin,
		dispatch,
		prevUserData,
	]);

	useEffect(() => {
		if (!isLoggedIn && resigningDocs && resignDocumentsFeatureFlag) {
			setOpenPendingActionModal(true);
		}
		if (isLoggedIn && prevUserData?.crmLeadCreated) {
			setIsModalOpen(true);
		} else if (isLoggedIn && prevUserData?.crmLeadCreated === false) {
			setIsModalOpen(false);
		}
		if (
			isLoggedIn &&
			prevUserData?.crmLeadCreated &&
			resigningDocs &&
			resignDocumentsFeatureFlag
		) {
			setOpenPendingActionModal(false);
			navigateRedirect(generatePath(AppPaths.myProperties, { locale }));
		}
	}, [
		isLoggedIn,
		dispatch,
		prevUserData,
		showDocumentModal,
		resigningDocs,
		resignDocumentsFeatureFlag,
	]);

	// Handle actionAppointment search parameter
	useEffect(() => {
		if (actionAppointment && enableVirtualAppointment) {
			const uppercaseActionAppointment = actionAppointment?.toUpperCase();
			if (
				uppercaseActionAppointment === appointmentTypeProps.PHYSICAL ||
				uppercaseActionAppointment === appointmentTypeProps.VIRTUAL
			) {
				if (isLoggedIn) {
					setOpenVirtual(true);
					setAppointmentType(uppercaseActionAppointment);
				} else {
					dispatch(authModalSlice.actions.openModal());
					setShouldOpenVirtualAppointmentAfterLogin(true);
					setAppointmentType(uppercaseActionAppointment);
					setOpenVirtual(false);
				}
			}
		}
	}, [actionAppointment, enableVirtualAppointment, isLoggedIn, dispatch]);

	useEffect(() => {
		if (!isLoading && !isLoaderComplete) {
			setIsLoaderComplete(true);

			if (enableNationalDayContent && shouldShowNationalDayVideo()) {
				setShowNationalDayVideo(true);
			}
		}
	}, [isLoading, isLoaderComplete, enableNationalDayContent]);

	const handleNationalDayVideoEnd = () => {
		setShowNationalDayVideo(false);
	};

	const handlePendingActionModal = () => {
		if (isLoggedIn && resigningDocs) {
			setOpenPendingActionModal(false);
		} else {
			setOpenPendingActionModal(false);
			dispatch(authModalSlice.actions.openModal());
		}
	};
	const handleOnClose = () => {
		setOpenVirtual(false);
		navigateRedirect(generatePath(AppPaths.myAppointments, { locale }));
	};
	const bannerArgs = {
		altText: t("homePagev4.bannerArgs.altText"),
		caption: {
			color: "white",
			text: t("homePagev4.bannerArgs.captionText"),
		},
		cardsBg: {
			src: isRebrandedFlag
				? "https://alb-home.roshn.sa/new_gradient_602d8e6d2c/new_gradient_602d8e6d2c.jpeg"
				: "https://alb-home.roshn.sa/banner_card_light_bg_f882abab94/banner_card_light_bg_f882abab94.png",
		},
		chevronIconButton: { href: visitSaleCenter },
		cta: {
			href: "#communities",
			text: t("features.exploreCommunities.title"),
			type: "primary",
		},
		direction: theme.direction,
		imageSrc: enableNationalDayContent
			? isMobile
				? "https://alb-home.roshn.sa/Mob_Pattern_f1430d8729/Mob_Pattern_f1430d8729.svg"
				: isDesktop
					? "https://alb-home.roshn.sa/desktop_V2_9486809264/desktop_V2_9486809264.svg"
					: "https://alb-home.roshn.sa/i_Pad_Pro_11_v3_3a446f4b87/i_Pad_Pro_11_v3_3a446f4b87.svg"
			: "https://alb-home.roshn.sa/Hero_Banner_13de8f0d09/Hero_Banner_13de8f0d09.webp",
		smallBannerOne: {
			chevronIconButton: {
				onClick: enableVirtualAppointment
					? () => handleOpenVirtualAppointment(appointmentTypeProps.VIRTUAL)
					: handleSaleCenter,
			},
			id: "smallBannerOne",
			smallBannerText: {
				color: "white",
				text: enableVirtualAppointment
					? t("features.bookings.virtualAppointment.homeBanner.overlay2")
					: t("homePagev4.bannerArgs.smallBannerOne"),
			},
			onClick: enableVirtualAppointment
				? () => handleOpenVirtualAppointment(appointmentTypeProps.VIRTUAL)
				: handleSaleCenter,
		},
		smallBannerTwo: {
			chevronIconButton: {
				onClick: enableVirtualAppointment
					? () => handleOpenVirtualAppointment(appointmentTypeProps.PHYSICAL)
					: handleOpenModal,
			},
			id: "smallBannerTwo",
			smallBannerText: {
				color: "white",
				text: enableVirtualAppointment
					? t("features.bookings.virtualAppointment.homeBanner.overlay1")
					: t("homePagev4.bannerArgs.smallBannerTwo"),
			},
			onClick: enableVirtualAppointment
				? () => handleOpenVirtualAppointment(appointmentTypeProps.PHYSICAL)
				: handleOpenModal,
		},
		title: {
			color: "white",
			text: t("homePagev4.bannerArgs.titleText"),
		},
		isNewHomePage: enableNewHomePage ? true : false,
	};
	const brokerageBannerArgs = {
		image: {
			altText: "sedra",
			src: "https://alb-home.roshn.sa/pvw_i1_61741a58cc/pvw_i1_61741a58cc.png",
		},
		cta: {
			onClick: () =>
				navigate(generateAppPath(AppPaths.propertyFinder, { locale })),
			text: t("homePagev4.brokerageArgs.viewListings"),
			isRebranded: isRebrandedFlag,
		},
		title: {
			text: t("homePagev4.brokerageArgs.roshnBrokerage"),
		},
		subTitle: {
			text: t("homePagev4.brokerageArgs.readyForPurchase"),
		},
		description: {
			text: t("homePagev4.brokerageArgs.discription"),
		},
	};

	const mapArgs = {
		description: t("homePagev4.mapArgs.description"),
		innerMaps: [
			{
				communities: [
					{
						cardInfo: {
							areaImage: {
								altText: "sedra",
								src: "https://alb-home.roshn.sa/pvw_i1_61741a58cc/pvw_i1_61741a58cc.png",
							},
							areaLogo: {
								altText: t("features.signUp.leadForm.communities.sedra"),
								src: t("homePagev4.mapArgs.sedraLogo"),
							},
							cardLabel: t("homePagev4.mapArgs.cardLabelSedra"),
							cta: {
								onClick: () => goToSelectedCommunity(COMMUNITY_NAMES.SEDRA),
								text: t("homePagev4.mapArgs.viewMasterplan"),
							},
							tagline: t("homePagev4.mapArgs.tagline"),
						},
						desktopPosition: { left: "46%", top: "49%" },
						dotLabel: t("homePagev4.mapArgs.sedra"),
						id: t("homePagev4.mapArgs.sedra"),
						mobilePosition: { left: "47%", top: "49%" },
					},
					{
						cardInfo: {
							areaImage: {
								altText: "warefa",
								src: "https://alb-home.roshn.sa/warefa_628a08ee8c/warefa_628a08ee8c.webp",
							},
							areaLogo: {
								altText: "WAREFA",
								src: t("homePagev4.mapArgs.warefaLogo"),
							},
							cardLabel: t("homePagev4.mapArgs.cardLabelWarefa"),
							cta: {
								onClick: () => goToSelectedCommunity(COMMUNITY_NAMES.WAREFA),
								text: t("homePagev4.mapArgs.viewMasterplan"),
							},
							tagline: t("homePagev4.mapArgs.tagline"),
						},
						desktopPosition: { left: "55%", top: "55%" },
						dotLabel: t("homePagev4.mapArgs.warefa"),
						id: t("homePagev4.mapArgs.warefa"),
					},
				],
				id: "RIYADH",
			},
			{
				communities: [
					{
						cardInfo: {
							areaImage: {
								altText: "alarous",
								src: "https://alb-home.roshn.sa/Main_Header_2x_aaf9589128/Main_Header_2x_aaf9589128.webp",
							},
							areaLogo: {
								altText: "ALAROUS",
								src: t("homePagev4.mapArgs.alarousLogo"),
							},
							cardLabel: t("homePagev4.mapArgs.cardLabelAlarous"),
							cta: {
								onClick: () => goToSelectedCommunity(COMMUNITY_NAMES.ALAROUS),
								text: t("homePagev4.mapArgs.viewMasterplan"),
							},
							tagline: t("homePagev4.mapArgs.tagline"),
						},
						desktopPosition: { left: "19%", top: "67%" },
						dotLabel: t("homePagev4.mapArgs.alarous"),
						id: t("homePagev4.mapArgs.alarous"),
						mobilePosition: { left: "19%", top: "62%" },
						tabletPosition: { left: "21%", top: "65%" },
					},
					enableAlmanarCommunity && {
						cardInfo: {
							areaImage: {
								altText: "almanar",
								src: "https://alb-home.roshn.sa/ALMANAR_Community_Image_ef90467120/ALMANAR_Community_Image_ef90467120.webp",
							},
							areaLogo: {
								altText: t("features.signUp.leadForm.communities.sedra"),
								src: t("homePagev4.mapArgs.almanarLogo"),
							},
							cardLabel: t("homePagev4.mapArgs.cardLabelALMANAR"),
							cta: {
								onClick: () => goToSelectedCommunity(COMMUNITY_NAMES.ALMANAR),
								text: t("homePagev4.mapArgs.viewMasterplan"),
							},
							tagline: t("homePagev4.mapArgs.tagline"),
						},
						dotLabel: t("homePagev4.mapArgs.almanar"),
						id: t("homePagev4.mapArgs.almanar"),
						desktopPosition: { left: "28%", top: "70%" },
						mobilePosition: { left: "28%", top: "67%" },
						tabletPosition: { left: "26%", top: "70%" },
					},
					enableAldanah && {
						cardInfo: {
							areaImage: {
								altText: "aldanah",
								src: "https://************.nip.io/185_ROSHN_ALDANAH_cam_05_1ea6b61db6/185_ROSHN_ALDANAH_cam_05_1ea6b61db6.jpg",
							},
							areaLogo: {
								altText: t("features.signUp.leadForm.communities.aldanah"),
								src: t("homePagev4.mapArgs.aldanahLogo"),
							},
							cardLabel: t("homePagev4.mapArgs.cardLabelAldanah"),
							cta: {
								onClick: () => goToSelectedCommunity(COMMUNITY_NAMES.ALDANAH),
								text: t("homePagev4.mapArgs.viewMasterplan"),
							},
							tagline: t("homePagev4.mapArgs.tagline"),
						},
						dotLabel: t("homePagev4.mapArgs.aldanah"),
						id: t("homePagev4.mapArgs.aldanah"),
						desktopPosition: { left: "68%", top: "40%" },
						mobilePosition: { left: "68%", top: "32%" },
						tabletPosition: { left: "70%", top: "35%" },
					},
				].filter(Boolean),
				id: "JEDDAH",
			},
			{
				communities: [],
				id: "ASIR",
			},
		],
		mapBackgroundImage: {
			altText: "Map Background Image",
			src: enableNewHomePage
				? "https://alb-home.roshn.sa/Map_Bg_e03b8727e4/Map_Bg_e03b8727e4.webp"
				: "https://alb-home.roshn.sa/new_gradient_602d8e6d2c/new_gradient_602d8e6d2c.jpeg",
		},
		title: t("homePagev4.mapArgs.title"),
		isRebranded: isRebrandedFlag,
		isNewHomePage: enableNewHomePage ? true : false,
	};

	const digitalExperienceArgs = {
		heading: t("homePagev4.digitalExperience.heading"),
		description: t("homePagev4.digitalExperience.description"),
		title: t("homePagev4.digitalExperience.title"),
		appStoreUrl: t("homePagev4.digitalExperience.appStoreUrl"),
		playStoreUrl: t("homePagev4.digitalExperience.playStoreUrl"),
		appStoreImageUrl: t("homePagev4.digitalExperience.appStore"),
		playStoreImageUrl: t("homePagev4.digitalExperience.playStore"),
		qrCodeUrl: t("homePagev4.digitalExperience.qrCode"),
		previewImageUrl: t("homePagev4.digitalExperience.previewImage"),
		patternImageUrl:
			"https://alb-home.roshn.sa/Pattern_0353face0f/Pattern_0353face0f.svg",
	};

	useEffect(() => {
		const script = document.createElement("script");
		script.innerHTML = `
				document.querySelectorAll('a[href^="#"]').forEach(anchor => {
					anchor.addEventListener('click', function(e) {
						e.preventDefault();
						const targetId = this.getAttribute('href').substring(1);
						const targetElement = document.getElementById(targetId);
						if (targetElement) {
							targetElement.scrollIntoView({
								behavior: 'smooth'
							});
						}
					});
				});
			`;
		document.body.appendChild(script);
		return () => {
			document.body.removeChild(script);
		};
	}, []);

	const pendingActionContent = {
		heading: t("features.resignDocuments.pendingAction.heading"),
		message: t("features.resignDocuments.pendingAction.message"),
		btn: t("features.resignDocuments.pendingAction.cta"),
		skip: t("features.resignDocuments.pendingAction.skip"),
	};

	const thankUContent = {
		heading: t("features.resignDocuments.thankYou.heading"),
		message: t("features.resignDocuments.thankYou.message"),
		btn: t("features.resignDocuments.thankYou.cta"),
	};

	if (isLoading) {
		return <FullScreenLoader />;
	}

	return (
		<>
			<Global
				styles={css`
					html {
						scroll-behavior: smooth;
					}
				`}
			/>
			<div
				css={
					enableNationalDayContent
						? styles.bannerOverride(!isDesktop && !isMobile)
						: undefined
				}
			>
				<HomeStaticBanner {...bannerArgs} />
			</div>
			<div id="communities" css={styles.communitiesSection}>
				<Suspense
					fallback={
						<div
							style={{
								height: "400px",
								display: "flex",
								justifyContent: "center",
								alignItems: "center",
							}}
						>
							Loading...
						</div>
					}
				>
					<CardCarouselComponent onClickAppointment={handleVisitSalesCenter} />
				</Suspense>
			</div>
			<Suspense
				fallback={
					<div
						style={{
							height: "600px",
							display: "flex",
							justifyContent: "center",
							alignItems: "center",
						}}
					>
						Loading map...
					</div>
				}
			>
				{/* @ts-ignore - Ignoring type error for CommunityMap props */}
				<CommunityMap {...mapArgs} />
			</Suspense>
			{enableNewHomePage && (
				<Suspense
					fallback={
						<div
							style={{
								height: "400px",
								display: "flex",
								justifyContent: "center",
								alignItems: "center",
							}}
						>
							Loading brokerage...
						</div>
					}
				>
					<DigitalExperienceSection {...digitalExperienceArgs} />
				</Suspense>
			)}
			{featureFlagAPI?.enableBrokerageFeature && (
				<Suspense
					fallback={
						<div
							style={{
								height: "400px",
								display: "flex",
								justifyContent: "center",
								alignItems: "center",
							}}
						>
							Loading brokerage...
						</div>
					}
				>
					<HeroBrokerageBanner {...brokerageBannerArgs} />
				</Suspense>
			)}
			<Suspense
				fallback={
					<div
						style={{
							height: "200px",
							display: "flex",
							justifyContent: "center",
							alignItems: "center",
						}}
					>
						Loading footer...
					</div>
				}
			>
				<Footer />
			</Suspense>
			<RegisterInterestDialogWrapper
				open={isModalOpen}
				onClose={() => setIsModalOpen(false)}
				onSuccess={() => setIsModalOpen(false)}
				hasLoaded={false}
				isCheckState={checkState}
			/>
			<PendingActionModal
				isOpen={openPendingActionModal}
				content={pendingActionContent}
				onClose={handlePendingActionModal}
				onSkip={() => setOpenPendingActionModal(false)}
			/>
			<VirtualAppointment
				community={community}
				clickType={appointmentType}
				open={openVirtual}
				onClose={() => handleCloseVirtualAppointment()}
				onDone={handleOnClose}
			/>
			{showNationalDayVideo && (
				<NationalDayVideo onVideoEnd={handleNationalDayVideoEnd} />
			)}
		</>
	);
};
