import { css, useTheme } from "@emotion/react";
import { useState, useEffect, useMemo } from "react";
import {
	RDSTabGroup,
	RDSButton,
	MapWidgetV2,
	RDSTypography,
} from "@roshn/ui-kit";
import { DividerV2 } from "@/components/dividerV2";
import ListItem from "./list-item";
import { AppTheme, mobileMQ } from "@/theme";
import * as shared from "@roshn/shared";
import { VirtualAppointment } from "@/features/virtual-appointment/virtual-appointment";
import { appointmentTypeProps } from "@/features/virtual-appointment/constant";
import { useScreenSize } from "@/hooks/media-query-hooks";
import { generatePath, useParams } from "@remix-run/react";
import { AppPaths } from "@/routes";
import { useNavigateRedirect } from "@/hooks/use-navigate-redirect";
import { useAppDispatch } from "@/store";
import { authModalSlice } from "@/store/global-modal-slices";
import { useFeatureFlagApi } from "@/features/feature-flags";
import { useInjection } from "@roshn/shared";

type Center = {
	address: string;
	hours: string;
	id: string;
	markerLabel: string;
	markerLinkLabel: string;
	name: string;
	title: string;
};

type ListItemData = {
	icon: React.ReactNode;
	label: string;
	text: string;
};

type SalesCentersSectionProps = {
	btnText: string;
	centers: Center[];
	googleApiKey?: string;
	listItems: (center: Center) => ListItemData[];
	sectionTitle: string;
};

const styles = {
	centerTitle: (theme: AppTheme) =>
		css({
			color: theme?.rds?.color.text.ui.primary,
		}),

	container: (theme: AppTheme) =>
		css({
			[mobileMQ(theme)]: {
				flexDirection: "column",
			},
			display: "flex",
			flex: 1,
			flexDirection: "row",
			gap: theme?.rds?.dimension[500],
			marginTop: theme?.rds?.dimension[400],
			width: "100%",
		}),

	contentContainer: (theme: AppTheme) =>
		css({
			[mobileMQ(theme)]: {
				maxWidth: "100%",
			},
			display: "flex",
			flexDirection: "column",
			gap: theme?.rds?.dimension[500],
			maxWidth: "35rem",
			width: "100%",
		}),

	info: (theme: AppTheme) =>
		css({
			display: "flex",
			flex: 1,
			flexDirection: "column",
			gap: theme?.rds?.dimension[500],
			minWidth: 0,
		}),

	map: (theme: AppTheme) =>
		css({
			[mobileMQ(theme)]: {
				maxWidth: "100%",
			},
			flex: "1 0 0",
			maxWidth: "40%",
		}),

	section: (theme: AppTheme) =>
		css({
			[mobileMQ(theme)]: {
				padding: `${theme?.rds?.dimension[400]} ${theme?.rds?.dimension[300]}`,
			},
			padding: `${theme?.rds?.dimension[500]} ${theme?.rds?.dimension[1000]}`,
		}),
	tabsGroup: css({
		maxWidth: "100%",
		overflowX: "auto",
	}),
};

const SalesCentersSection = ({
	centers,
	googleApiKey = "",
	listItems,
	sectionTitle,
	btnText,
}: SalesCentersSectionProps) => {
	const { isTablet } = useScreenSize();
	const theme = useTheme();
	const [activeIndex, setActiveIndex] = useState(0);
	const activeCenter = centers[activeIndex];
	const [community, setCommunity] = useState("");
	const navigateRedirect = useNavigateRedirect();
	const { locale = "" } = useParams();
	const dispatch = useAppDispatch();

	const [openVirtual, setOpenVirtual] = useState(false);
	const [appointmentType, setAppointmentType] = useState("");
	const [
		shouldOpenVirtualAppointmentAfterLogin,
		setShouldOpenVirtualAppointmentAfterLogin,
	] = useState(false);

	const { data: featureFlagAPI } = useFeatureFlagApi();
	const enableVirtualAppointment = featureFlagAPI?.enableVirtualAppointment;
	const isLoggedIn = useInjection<shared.AuthService>(
		shared.AuthService,
	).useStore().signedIn;

	const handleBookAppointment = () => {
		if (enableVirtualAppointment) {
			if (isLoggedIn) {
				setOpenVirtual(true);
				setAppointmentType(appointmentTypeProps.VIRTUAL);
			} else {
				dispatch(authModalSlice.actions.openModal());
				setShouldOpenVirtualAppointmentAfterLogin(true);
				setAppointmentType(appointmentTypeProps.VIRTUAL);
				setOpenVirtual(false);
			}
		} else {
			navigateRedirect(generatePath(AppPaths.visitSaleCenter, { locale }));
		}
	};
	const handleCloseVirtualAppointment = () => {
		setOpenVirtual(false);
		setCommunity("");
		setAppointmentType("");
	};
	const handleOnClose = () => {
		setOpenVirtual(false);
		navigateRedirect(
			generatePath(AppPaths.myAppointments, { locale: locale || "" }),
		);
	};

	useEffect(() => {
		if (shouldOpenVirtualAppointmentAfterLogin && isLoggedIn) {
			setOpenVirtual(true);
			setShouldOpenVirtualAppointmentAfterLogin(false);
		}
	}, [isLoggedIn, shouldOpenVirtualAppointmentAfterLogin, dispatch]);

	useEffect(() => {
		setCommunity(activeCenter?.id || "");
	}, [activeCenter]);
	
	const tabContent = useMemo(() => {
		return centers.map((center, index) => ({
			isDisabled: false,
			label: center.name.toUpperCase(),
			level: "1" as const,
			onClick: () => setActiveIndex(index),
			platform: "desktop" as const,
			rdsBadge: false,
			state: (index === activeIndex ? "active" : "default") as
				| "active"
				| "default",
		}));
	}, [centers, activeIndex]);
	return (
		<section css={styles.section}>
			<DividerV2 dividerLine="after">
				<RDSTypography
					fontName={
						isTablet
							? theme?.rds?.typographies.heading.h4
							: theme?.rds?.typographies.heading.emphasis.h6
					}
				>
					{sectionTitle}
				</RDSTypography>
			</DividerV2>

			<div css={styles.container}>
				<div css={styles.info}>
					<div css={styles.tabsGroup}>
						<RDSTabGroup
							platform="desktop"
							level="2"
							tabContent={tabContent}
							activeTabIndex={activeIndex}
							data-testid="sales-centers-tab-group"
						/>
					</div>

					<div css={styles.contentContainer}>
						<RDSTypography
							fontName={
								isTablet
									? theme?.rds?.typographies.heading.h4
									: theme?.rds?.typographies.heading.h6
							}
							color={theme?.rds?.color.text.ui.primary}
							data-testid="sales-centers-center-title"
						>
							{activeCenter.title}
						</RDSTypography>

						{listItems(activeCenter).map((item, index) => (
							<ListItem
								key={index}
								icon={item.icon}
								label={item.label}
								text={item.text}
								data-testid={`sales-centers-list-item-${index}`}
							/>
						))}

						<RDSButton
							text={btnText}
							size="lg"
							onClick={handleBookAppointment}
							data-testid="sales-centers-book-appointment-button"
						/>
					</div>
				</div>

				<div css={styles.map} data-testid="sales-centers-map">
					<MapWidgetV2
						key={activeCenter.id}
						googleApiKey={googleApiKey}
						markerLabel={activeCenter.markerLabel}
						linkLabel={activeCenter.markerLinkLabel}
					/>
				</div>
			</div>

			{/* Virtual Appointment Dialog */}
			<VirtualAppointment
				community={community.toUpperCase() || ""}
				clickType={appointmentType}
				open={openVirtual}
				onClose={() => handleCloseVirtualAppointment()}
				onDone={handleOnClose}
			/>
		</section>
	);
};

export default SalesCentersSection;
