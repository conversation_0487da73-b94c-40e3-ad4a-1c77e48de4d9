import { css, Theme, useTheme } from "@emotion/react";
import { useState, useEffect, useCallback, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useParams } from "@remix-run/react";
import {
	RDSButton,
	RDSSelect,
	RDSTextArea,
	RDSTypography,
} from "@roshn/ui-kit";
import * as React from "react";
import { useAppDispatch } from "@/store";
import { authModalSlice } from "@/store/global-modal-slices";
import { AuthService, AccountService, useInjection } from "@roshn/shared";
import {
	ContactRequestTypeImpl,
	ContactRequestTypeOption,
} from "@/services/contact-request-type";
import { ContactServiceImpl } from "@/services/contact";
import { showToast } from "@/components/toast/toast";

const styles = {
	form: (theme: Theme) =>
		css({
			display: "flex",
			flexDirection: "column",
			gap: theme?.rds?.dimension[200],
			width: "100%",
		}),
	section: (theme: Theme) =>
		css({
			alignItems: "flex-start",
			backgroundColor: theme?.rds?.color.background.ui.primary.default,
			display: "flex",
			flexDirection: "column",
			gap: theme?.rds?.dimension[300],
			padding: theme?.rds?.dimension[400],
			position: "relative",
			width: "100%",
		}),
};

const MESSAGE_MAX_LENGTH = 500;

type ContactFormValues = {
	inquiryType: ContactRequestTypeOption | null;
	message: string;
};

const initialFormState: ContactFormValues = {
	inquiryType: null,
	message: "",
};

const ContactFormSection = () => {
	const { t } = useTranslation();
	const { locale } = useParams();
	const dispatch = useAppDispatch();
	const theme = useTheme();

	const [formData, setFormData] = useState(initialFormState);
	const [shouldSubmitAfterLogin, setShouldSubmitAfterLogin] = useState(false);
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [inquiryTypeOptions, setInquiryTypeOptions] = useState<
		ContactRequestTypeOption[]
	>([]);

	const isLoggedIn = useInjection<AuthService>(AuthService).useStore().signedIn;
	const isRegistered = useInjection<AccountService>(AccountService).useIsRegistered();
	const contactRequestTypeService = useInjection<ContactRequestTypeImpl>(
		ContactRequestTypeImpl,
	);
	const contactService = useInjection<ContactServiceImpl>(ContactServiceImpl);

	useEffect(() => {
		const fetchInquiryTypes = async () => {
			const types = await contactRequestTypeService.getContactRequestTypes(
				locale ?? "en",
			);
			setInquiryTypeOptions(types);
		};

		fetchInquiryTypes();
	}, [contactRequestTypeService, locale]);
	const handleTextAreaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
		const { name, value } = e.target;
		setFormData((prev) => ({ ...prev, [name]: value }));
	};

	const submitForm = useCallback(async () => {
		if (!formData.inquiryType || !formData.message.trim()) {
			return;
		}

		setIsSubmitting(true);

		try {
			const submitData = {
				Enquiry: formData.message.trim(),
				Interest: formData.inquiryType?.value || "",
			};
			await contactService.submitContactInquiry(submitData);

			showToast({
				appearance: "success",
				label: t("contactUs.contactForm.successMsg"),
				leadIcon: true,
				position: "top-center",
				version: "v2",
			});
			setFormData(initialFormState);
			setShouldSubmitAfterLogin(false);
		} catch (err: any) {
			showToast({
				appearance: "danger",
				label: err?.message || t("contactUs.contactForm.submitError"),
				leadIcon: true,
				position: "top-center",
				version: "v2",
			});
		} finally {
			setIsSubmitting(false);
		}
	}, [formData.inquiryType, formData.message, contactService, t]);

	const handleSubmit = useCallback(
		(e: React.FormEvent) => {
			e.preventDefault();
			if (isLoggedIn && isRegistered) {
				submitForm();
			} else {
				dispatch(authModalSlice.actions.openModal());
				setShouldSubmitAfterLogin(true);
			}
		},
		[isLoggedIn, isRegistered, submitForm, dispatch],
	);

	useEffect(() => {
		if (shouldSubmitAfterLogin && isLoggedIn && isRegistered) {
			submitForm();
		}
	}, [isLoggedIn, isRegistered, shouldSubmitAfterLogin, submitForm]);

	const isFormValid = useMemo(
		() =>
			formData.inquiryType &&
			formData.message.trim().length > 0 &&
			formData.message.length <= MESSAGE_MAX_LENGTH,
		[formData.inquiryType, formData.message],
	);

	return (
		<>
			<section css={styles.section}>
				<RDSTypography fontName={theme?.rds?.typographies.heading.emphasis.h4}>
					{t("contactUs.contactForm.title")}
				</RDSTypography>

				<form css={styles.form} onSubmit={handleSubmit}>
					<RDSSelect
						label={t("contactUs.contactForm.requestType")}
						placeholder={t("contactUs.contactForm.selectPlaceholder")}
						isRequired
						options={inquiryTypeOptions}
						value={formData.inquiryType}
						onChange={(selectedOption) => {
							const option = selectedOption as ContactRequestTypeOption | null;
							setFormData((prev) => ({
								...prev,
								inquiryType: option,
							}));
						}}
						data-testid="contact-form-request-type-select"
					/>

					<RDSTextArea
						label={t("contactUs.contactForm.message")}
						name="message"
						placeholder={t("contactUs.contactForm.messagePlaceholder")}
						value={formData.message}
						onChange={handleTextAreaChange}
						isInvalid={formData.message.length > MESSAGE_MAX_LENGTH}
						helperText={
							formData.message.length > MESSAGE_MAX_LENGTH
								? t("contactUs.contactForm.messageTooLong")
								: `${formData.message.length}/${MESSAGE_MAX_LENGTH}`
						}
						data-testid="contact-form-message-text-area"
						css={{ resize: "none" }}
						isRequired
					/>

					<RDSButton
						type="submit"
						text={t("contactUs.contactForm.submitCTA")}
						size="md"
						disabled={!isFormValid || isSubmitting}
						loading={isSubmitting}
						data-testid="contact-form-submit-button"
					/>
				</form>
			</section>
		</>
	);
};

export default ContactFormSection;
