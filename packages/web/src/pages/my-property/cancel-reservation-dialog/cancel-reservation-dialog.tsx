import {
	CheckCircleDarkIcon,
	InfoIcon,
	RoshnLogoHorizontal,
	SquareWarningIcon,
} from "@/components/icons";
import { RDSDialog } from "@/components/rds-dialog";
import {
	cancelReservationCreateOtp,
	cancelReservationGetResult,
	cancelReservationRequest,
	getCancelReservationUnSignedDoc,
	getGccBanks,
	reservationSlice,
	setReservationCancellationStatus,
	setReservationId,
	signCancellationDocs,
} from "@/features/reservation/redux";
import { useAppDispatch, useAppSelector } from "@/store";
import { css, Theme, useTheme } from "@emotion/react";
import { useParams, useNavigate } from "@remix-run/react";
import {
	AccountService,
	CancellationData,
	CancellationReason,
	CancellationSubCategory,
	QueryKey,
	ReservationCancellationStatus,
	useInjection,
	UserBank,
} from "@roshn/shared";
import { useFeatureFlagApi } from "@/features/feature-flags";
import { AppPaths, useAppPathGenerator } from "@/routes";
import {
	RDSButton,
	RDSSelect,
	RDSTextArea,
	RDSTextInput,
	RDSToolTip,
	RDSUploadFile,
} from "@roshn/ui-kit";
import { useQuery } from "@tanstack/react-query";
import { Dispatch, SetStateAction, useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import * as SignatureInput from "@/features/signature-input";
import { useSignatureInputDispatch } from "@/features/signature-input/context/signature-input-context";
import { showDocumentModal } from "@/features/signature-input/context/signature-input-actions";
import { RDSRoshnLoadingModal } from "@/features/loading";
import {
	ACCEPTED_FILE_TYPES,
	validateGccIban,
	MAX_FILE_SIZE,
	formatIbanForDisplay,
} from "@/utils/reservation-util";
import { RESERVATION_ERROR } from "@/features/reservation/constants";
import { ValidationErrorsIBAN } from "ibantools";
import { useGetCancellationReasons } from "@/features/reservation/utils";
import { tabletMQ } from "@/theme";

const style = {
	cancellationReasonContainer: (theme: Theme) =>
		css({
			display: "flex",
			flexDirection: "column",
			gap: theme.spaces.md,
			maxWidth: "480px",
		}),
	spaceShipErrorContainer: () =>
		css({
			display: "flex",
			justifyContent: "center",
		}),
	minWidthContainer: (theme: Theme) =>
		css({
			width: "100%",
			display: "flex",
			alignItems: "center",
			justifyContent: "center",
			[tabletMQ(theme)]: {
				minWidth: "416px",
			},
		}),
};

type CancellationFieldValues =
	| CancellationReason
	| CancellationSubCategory
	| UserBank
	| File
	| string;

export type CancelReservationDialogProp = {
	reservationId: string;
	reservationCancellationStatus?: ReservationCancellationStatus;
	financingMethod: "CASH" | "HOME_FINANCE" | "UNSELECTED" | undefined;
};

const CancelReservationDialog = ({
	reservationId,
	reservationCancellationStatus,
	financingMethod,
}: CancelReservationDialogProp) => {
	const { t } = useTranslation(undefined, {
		keyPrefix: "pages.cancelReservation",
	});
	const { t: t_ } = useTranslation();
	const dispatch = useAppDispatch();
	const navigate = useNavigate();
	const { data: featureFlagAPI } = useFeatureFlagApi();
	const { enableNewContactPage } = featureFlagAPI || {};
	const generateAppPath = useAppPathGenerator();
	const theme = useTheme();
	const reservationData = useAppSelector((state) => state.reservation);
	const cancellationData = reservationData.cancellationData;
	const cancellationStatus =
		reservationData.data.reservationCancellationStatus ??
		reservationCancellationStatus;
	const [isLoading, setIsLoading] = useState(false);
	const [isOpenErrorModal, setIsOpenErrorModal] = useState(false);
	const [isBeingSigned, setIsBeingSigned] = useState(false);
	const [hasSignedDoc, setHasSignedDoc] = useState(false);
	const [isCancellationReasonModalOpen, setCancellationReasonModalOpen] =
		useState(false);
	const [isCancellationBankModalOpen, setCancellationBankModalOpen] =
		useState(false);
	const [isAcknowledgmentModalOpen, setAcknowledgmentModalOpen] = useState(
		cancellationStatus ===
			ReservationCancellationStatus.DOCUMENT_SIGNED_BY_USER && hasSignedDoc,
	);
	const user = useInjection<AccountService>(AccountService).useAccountStore();
	const isSigned =
		cancellationStatus ==
			ReservationCancellationStatus.DOCUMENT_SIGNED_BUT_NO_CALLBACK &&
		isBeingSigned;

	const isCancellationRequested = [
		ReservationCancellationStatus.DOCUMENT_SIGNED_BY_USER,
	].includes(cancellationStatus!);

	const isCancellationDocumentGenerated =
		Boolean(cancellationStatus) && !isCancellationRequested;

	const isOpen =
		reservationData.openCancelReservationDialog &&
		!isCancellationDocumentGenerated;
	const errorCancellationRequest =
		reservationData.error === RESERVATION_ERROR.CANCELLATION_REQUEST_ERROR;

	const handleCloseConfirmationModal = () => {
		dispatch(reservationSlice.actions.setOpenCancelReservationDialog(false));
	};
	const handleCloseCancellationReasonModal = () => {
		setCancellationReasonModalOpen(false);
	};
	const handleCloseCancellationBankModal = () => {
		setCancellationBankModalOpen(false);
	};
	const updateCancellation = (
		key: keyof CancellationData,
		value: CancellationFieldValues,
	) => {
		dispatch(reservationSlice.actions.setCancellationData({ [key]: value }));
	};
	const cancellationReasons = useGetCancellationReasons();
	const listAllCancellationReasons = cancellationReasons?.cancellationCategories
		?.filter((reason) => {
			if (
				(financingMethod === undefined || financingMethod !== "HOME_FINANCE") &&
				reason.code === "HOME_FINANCE_CRITERIA_NOT_MET"
			) {
				return false;
			}
			return true;
		})
		.map((reason) => ({
			label: reason.displayName,
			value: reason,
		}));

	const cancellationSubReasons =
		cancellationData?.selectedReason?.cancellationSubCategories?.map((sub) => ({
			label: sub.displayName,
			value: sub,
		}));
	const selectedReasonOption = cancellationData?.selectedReason
		? {
				label: cancellationData?.selectedReason.displayName,
				value: cancellationData?.selectedReason,
			}
		: null;
	const selectedSubReasonOption = cancellationData?.selectedSubReason
		? {
				label: cancellationData?.selectedSubReason.displayName,
				value: cancellationData?.selectedSubReason,
			}
		: null;
	const { data: bankList } = useQuery(
		[QueryKey.CMS, QueryKey.GCC_BANKS],
		() => dispatch(getGccBanks()).unwrap(),
		{ refetchOnWindowFocus: false, suspense: false },
	);

	const allBankList = bankList
		?.filter((bank) => bank.bankName && bank.bankName.trim() !== "")
		.map((bank) => ({
			label: bank.bankName,
			value: bank.bankId,
		}));

	const canContinueCancellationReasons = useMemo(() => {
		return (
			cancellationData?.selectedReason && cancellationData?.selectedSubReason
		);
	}, [cancellationData]);

	const getDocs = async () => {
		let res;
		if (isCancellationDocumentGenerated) {
			res = await dispatch(
				getCancelReservationUnSignedDoc(reservationId),
			).unwrap();
		}
		setIsLoading(true);
		const docData =
			reservationData?.cancellationResponse?.cancellationFormLink ??
			res?.eSignatureDocumentUrl;
		setIsLoading(false);
		return docData;
	};

	const generateOtp = async () => {
		setIsBeingSigned(true);
		await dispatch(cancelReservationCreateOtp(reservationId)).unwrap();
	};

	const validateOtp = async (otp: string): Promise<boolean> => {
		setHasSignedDoc(true);
		dispatch(reservationSlice.actions.setOpenCancelReservationDialog(false));
		return dispatch(signCancellationDocs(otp)).unwrap();
	};

	const handleFetchSignedSignal = async () => {
		return dispatch(cancelReservationGetResult()).unwrap();
	};

	const handleContactUsRedirect = () => {
		enableNewContactPage
			? navigate(generateAppPath(AppPaths.contactUs))
			: window.open(t("layout.header.tabs.contactUsUrl"), "_blank");
		dispatch(reservationSlice.actions.resetCancellationData());
		dispatch(reservationSlice.actions.setError(null));
	};

	const handleCloseErrorModal = () => {
		setIsOpenErrorModal(false);
		dispatch(reservationSlice.actions.resetCancellationData());
		dispatch(reservationSlice.actions.setError(null));
	};
	useEffect(() => {
		dispatch(setReservationId(reservationId));
		if (errorCancellationRequest) {
			setIsOpenErrorModal(true);
		}
	}, [errorCancellationRequest]);

	useEffect(() => {
		if (
			cancellationStatus ===
				ReservationCancellationStatus.DOCUMENT_SIGNED_BY_USER &&
			hasSignedDoc
		) {
			setAcknowledgmentModalOpen(true);
		}
	}, [cancellationStatus]);

	return (
		<SignatureInput.Root
			signed={isSigned}
			getDocument={getDocs}
			validateOtp={validateOtp}
			generateOtp={generateOtp}
		>
			{errorCancellationRequest ? (
				<RDSDialog
					isOpen={isOpenErrorModal}
					showContent={false}
					onClose={() => handleCloseErrorModal()}
					dialogHeaderProps={{
						leadIcon: false,
						trailIcon: true,
						trailIconProps: {
							onClick: () => handleCloseErrorModal(),
						},
						label: t("somethingWentWrong.title"),
						type: "centred",
						hasAsset: true,
						assetProps: {
							children: <SquareWarningIcon />,
							size: "56px",
						},
					}}
					showDescription={true}
					description={t("somethingWentWrong.description")}
					showFooter={true}
					buttonsGroup={{
						direction: "vertical",
						buttons: [
							<RDSButton
								variant={"primary"}
								size={"md"}
								text={t("somethingWentWrong.primaryCta")}
								onClick={() => {
									setIsOpenErrorModal(false);
									dispatch(reservationSlice.actions.setError(null));
									setCancellationBankModalOpen(true);
								}}
								data-testid="somethingWentWrong-primaryCta"
							/>,
							<RDSButton
								variant={"secondary"}
								size={"md"}
								text={t("somethingWentWrong.secondaryCta")}
								onClick={() => handleContactUsRedirect()}
								data-testid="somethingWentWrong-secondaryCta"
							/>,
						],
					}}
				/>
			) : (
				<>
					<div className="confirmation-modal">
						<RDSDialog
							isOpen={isOpen}
							showContent={false}
							onClose={() => handleCloseConfirmationModal()}
							dialogHeaderProps={{
								leadIcon: false,
								trailIcon: true,
								trailIconProps: {
									onClick: () => handleCloseConfirmationModal(),
								},
								label: t("confirmationModal.title"),
								type: "centred",
								hasAsset: true,
								assetProps: {
									children: <SquareWarningIcon />,
									size: "56px",
								},
							}}
							showDescription={true}
							description={t("confirmationModal.description")}
							showFooter={true}
							buttonsGroup={{
								direction: "vertical",
								buttons: [
									<RDSButton
										variant={"primary"}
										size={"md"}
										text={t("confirmationModal.primaryCta")}
										onClick={() => {
											handleCloseConfirmationModal();
											setCancellationReasonModalOpen(true);
										}}
										data-testid="start-cancellation"
									/>,
									<div css={style.minWidthContainer}>
										<RDSButton
											variant={"secondary"}
											size={"md"}
											text={t("confirmationModal.secondaryCta")}
											onClick={() => handleCloseConfirmationModal()}
											data-testid="discard-cancellation"
											css={{ width: "100%" }}
										/>
									</div>,
								],
							}}
						/>
					</div>
					<div className="cancellation-reason-modal">
						<RDSDialog
							isOpen={isCancellationReasonModalOpen}
							showContent={true}
							content={
								<div css={style.cancellationReasonContainer}>
									<RDSSelect
										isRequired={true}
										isDisabled={false}
										placeholder={t(
											"cancellationReasonModal.cancelReason.placeholder",
										)}
										label={t("cancellationReasonModal.cancelReason.question")}
										value={selectedReasonOption}
										onChange={(selectedOption) => {
											const option = selectedOption as {
												label: string;
												value: CancellationReason;
											};
											updateCancellation("selectedReason", option.value);
											updateCancellation("selectedSubReason", "");
										}}
										options={listAllCancellationReasons}
										helperText=""
										data-testid="cancellation-reason"
									/>
									{cancellationData?.selectedReason && (
										<RDSSelect
											isRequired={true}
											isDisabled={false}
											placeholder={t(
												"cancellationReasonModal.cancelSubReason.placeholder",
											)}
											label={t(
												"cancellationReasonModal.cancelSubReason.question",
											)}
											value={selectedSubReasonOption}
											onChange={(selectedOption) => {
												const option = selectedOption as {
													label: string;
													value: CancellationSubCategory;
												};
												updateCancellation("selectedSubReason", option.value);
											}}
											options={cancellationSubReasons}
											helperText=""
											data-testid="cancellation-sub-reason"
										/>
									)}
									<div css={style.minWidthContainer}>
										<RDSTextArea
											label={t("cancellationReasonModal.extraNote.question")}
											placeholder={t(
												"cancellationReasonModal.extraNote.placeHolder",
											)}
											maxLength={500}
											value={cancellationData?.extraNotes}
											onChange={(e) => {
												const extraNotes: string = e.target.value;
												updateCancellation("extraNotes", extraNotes);
											}}
											helperText=" "
											css={{ resize: "none" }}
											data-testid="cancellation-reason-extra-notes"
										/>
									</div>
								</div>
							}
							onClose={() => {
								handleCloseCancellationReasonModal();
								dispatch(reservationSlice.actions.resetCancellationData());
							}}
							dialogHeaderProps={{
								leadIcon: false,
								trailIcon: true,
								trailIconProps: {
									onClick: () => {
										handleCloseCancellationReasonModal();
										dispatch(reservationSlice.actions.resetCancellationData());
									},
								},
								label: t("cancellationReasonModal.title"),
								type: "centred",
								hasAsset: true,
								assetProps: {
									children: <RoshnLogoHorizontal />,
									size: "128px",
								},
							}}
							showFooter={true}
							buttonsGroup={{
								direction: "vertical",
								buttons: [
									<RDSButton
										variant={"primary"}
										size={"md"}
										text={t("cancellationReasonModal.primaryCta")}
										onClick={() => {
											setCancellationBankModalOpen(true);
											handleCloseCancellationReasonModal();
										}}
										disabled={!canContinueCancellationReasons}
										data-testid="cancellationReasonModal-primaryCta"
									/>,
									<RDSButton
										variant={"secondary"}
										size={"md"}
										text={t("cancellationReasonModal.secondaryCta")}
										onClick={() => {
											handleCloseCancellationReasonModal();
											dispatch(
												reservationSlice.actions.resetCancellationData(),
											);
										}}
										data-testid="cancellationReasonModal-secondaryCta"
									/>,
								],
							}}
						/>
					</div>
					<div className="cancellation-bank-modal">
						<CancellationBankModal
							isCancellationBankModalOpen={isCancellationBankModalOpen}
							updateCancellation={updateCancellation}
							cancellationData={cancellationData}
							allBankList={allBankList}
							handleCloseCancellationBankModal={
								handleCloseCancellationBankModal
							}
							handleOpenCancellationReasonModalOpen={
								setCancellationReasonModalOpen
							}
							reservationId={reservationId}
							setIsLoading={setIsLoading}
							isCancellationDocumentGenerated={isCancellationDocumentGenerated}
							showCancellationDocumentModal={
								reservationData.showCancellationDocumentModal
							}
						/>
					</div>
					<RDSRoshnLoadingModal isOpen={isLoading} />
					<SignatureInput.DocumentModal
						title={t("cancellationDocument.title")}
						consentText={t("cancellationDocument.consent")}
						buttonText={t("cancellationDocument.esign")}
						isRdsDialog={true}
					/>
					<SignatureInput.OtpModal
						phoneNumber={user?.phoneNumber || ""}
						title={t("cancellationDocument.confirmOtp")}
						onResend={generateOtp}
						isRdsDialog={true}
					/>
					{!isCancellationRequested && Boolean(cancellationStatus) && (
						<SignatureInput.WaitingModal
							title={t("cancellationDocument.loadingText")}
							onFetch={handleFetchSignedSignal}
							isRdsDialog={true}
						/>
					)}

					<div className="cancellation-acknowledgment">
						<RDSDialog
							isOpen={isAcknowledgmentModalOpen}
							showContent={false}
							onClose={() => {
								setHasSignedDoc(false);
								setAcknowledgmentModalOpen(false);
							}}
							dialogHeaderProps={{
								leadIcon: false,
								trailIcon: true,
								trailIconProps: {
									onClick: () => {
										setHasSignedDoc(false);
										setAcknowledgmentModalOpen(false);
										dispatch(reservationSlice.actions.resetCancellationData());
									},
								},
								label: t("acknowledgmentModal.title"),
								type: "centred",
								hasAsset: true,
								assetProps: {
									children: <CheckCircleDarkIcon />,
									size: "56px",
									shadow:
										theme.rds.color.background.functional.success.lighter
											.default,
								},
							}}
							showDescription={true}
							description={t("acknowledgmentModal.description")}
							showFooter={true}
							buttonsGroup={{
								direction: "vertical",
								buttons: [
									<div css={style.minWidthContainer}>
										<RDSButton
											variant={"primary"}
											size={"md"}
											text={t("acknowledgmentModal.cta")}
											onClick={() => {
												setHasSignedDoc(false);
												setAcknowledgmentModalOpen(false);
												dispatch(
													reservationSlice.actions.resetCancellationData(),
												);
											}}
											isFullWidth={true}
											css={{ width: "100%" }}
										/>
									</div>,
								],
							}}
						/>
					</div>
				</>
			)}
		</SignatureInput.Root>
	);
};

export default CancelReservationDialog;

type CancellationBankModalProps = {
	isCancellationBankModalOpen: boolean;
	cancellationData?: CancellationData;
	reservationId: string;
	updateCancellation: (
		key: keyof CancellationData,
		value: CancellationFieldValues,
	) => void;
	allBankList:
		| {
				label: string;
				value: string;
		  }[]
		| undefined;
	handleCloseCancellationBankModal: () => void;
	handleOpenCancellationReasonModalOpen: Dispatch<SetStateAction<boolean>>;
	setIsLoading: Dispatch<SetStateAction<boolean>>;
	isCancellationDocumentGenerated?: boolean;
	showCancellationDocumentModal?: boolean;
};

const CancellationBankModal = ({
	isCancellationBankModalOpen,
	cancellationData,
	updateCancellation,
	allBankList,
	handleCloseCancellationBankModal,
	handleOpenCancellationReasonModalOpen,
	reservationId,
	setIsLoading,
	isCancellationDocumentGenerated,
	showCancellationDocumentModal,
}: CancellationBankModalProps) => {
	const { t } = useTranslation(undefined, {
		keyPrefix: "pages.cancelReservation",
	});
	const { locale } = useParams();
	const isArabic = locale === "ar";
	const dispatch = useAppDispatch();
	const SignatureInputDispatch = useSignatureInputDispatch();
	const reservationData = useAppSelector((state) => state.reservation);
	const [ibanError, setIbanError] = useState<string | null>(null);
	const [fileError, setFileError] = useState<string | undefined>(undefined);
	const canContinueCancellationBankDetail = useMemo(() => {
		return (
			cancellationData?.userBank &&
			cancellationData?.userIban &&
			cancellationData?.userIbanCertificate &&
			!ibanError &&
			!fileError
		);
	}, [cancellationData]);

	const handleIbanChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const ibanValue = e.target.value.replace(/\s/g, "");
		updateCancellation("userIban", ibanValue);

		const { valid, errors } = validateGccIban(ibanValue);

		if (!valid) {
			setIbanError(
				errors.includes(ValidationErrorsIBAN.WrongBBANLength)
					? t("cancellationBankModal.refundBankIban.errorMessageWrongLength")
					: t("cancellationBankModal.refundBankIban.errorMessage"),
			);
		} else {
			setIbanError(null);
		}
	};
	const validateAndSetFile = (file: File | undefined) => {
		if (!file) {
			updateCancellation("userIbanCertificate", "");
			return;
		}

		const isInvalidFileType = !ACCEPTED_FILE_TYPES.includes(file.type);
		const isFileTooLarge = file.size > MAX_FILE_SIZE;

		if (isInvalidFileType && isFileTooLarge) {
			setFileError(
				t("cancellationBankModal.uploadFile.invalidFileTypeAndFileSizeError"),
			);
			updateCancellation("userIbanCertificate", "");
			return;
		}

		if (isInvalidFileType) {
			setFileError(t("cancellationBankModal.uploadFile.invalidFileTypeError"));
			updateCancellation("userIbanCertificate", "");
			return;
		}

		if (isFileTooLarge) {
			setFileError(t("cancellationBankModal.uploadFile.fileSizeError"));
			updateCancellation("userIbanCertificate", "");
			return;
		}

		setFileError(undefined);
		updateCancellation("userIbanCertificate", file);
	};

	const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const file = e.target.files?.[0];
		validateAndSetFile(file);
		if (e.target) {
			e.target.value = "";
		}
	};

	const handleFileDrop = (e: React.DragEvent<HTMLDivElement>) => {
		// Letting the event propagate to RDSUploadFile's internal handlers for UI update
		const file = e.dataTransfer?.files?.[0];
		validateAndSetFile(file);
	};

	const handleSubmitCancellationRequest = async () => {
		try {
			handleCloseCancellationBankModal();
			setIsLoading(true);
			const { cancellationFormLink } = await dispatch(
				cancelReservationRequest(reservationId),
			).unwrap();

			if (cancellationFormLink) {
				setIsLoading(false);
				SignatureInputDispatch(showDocumentModal(true));
				dispatch(setReservationCancellationStatus("DOCUMENT_GENERATED"));
			}
			setIsLoading(false);
		} catch (err) {
			console.error("Cancellation failed", err);
			setIsLoading(false);
		}
	};

	useEffect(() => {
		if (
			isCancellationDocumentGenerated &&
			reservationData.openCancelReservationDialog
		) {
			SignatureInputDispatch(showDocumentModal(true));
		}
	}, [showCancellationDocumentModal]);
	return (
		<div className="cancellation-bank-modal">
			<RDSDialog
				isOpen={isCancellationBankModalOpen}
				showContent={true}
				content={
					<div css={style.cancellationReasonContainer}>
						<RDSSelect
							isRequired={true}
							isDisabled={false}
							placeholder={t("cancellationBankModal.refundBank.placeholder")}
							label={t("cancellationBankModal.refundBank.question")}
							value={cancellationData?.userBank}
							onChange={(selectedOption) => {
								const option = selectedOption as {
									label: string;
									value: string;
								};
								updateCancellation("userBank", option);
								updateCancellation("userIban", "");
							}}
							options={allBankList}
							helperText=" "
							data-testid="refund-bank-name"
						/>
						<div css={style.minWidthContainer}>
							<RDSTextInput
								required
								isRequired
								type="text"
								value={formatIbanForDisplay(cancellationData?.userIban ?? "")}
								onChange={handleIbanChange}
								placeholder={t(
									"cancellationBankModal.refundBankIban.placeholder",
								)}
								label={t("cancellationBankModal.refundBankIban.question")}
								helperText={ibanError ?? ""}
								isInvalid={!!ibanError}
								data-testid="refund-bank-IBAN"
								infoIcon={
									<RDSToolTip
										label={t("cancellationBankModal.toolTip.title")}
										description={t("cancellationBankModal.toolTip.description")}
										direction="bottom"
										clickable={true}
									>
										<InfoIcon />
									</RDSToolTip>
								}
							/>
						</div>
						<div onDrop={handleFileDrop}>
							<RDSUploadFile
								isRequired={true}
								label={t("cancellationBankModal.uploadFile.question")}
								caption={t("cancellationBankModal.uploadFile.caption")}
								browseFilePlaceholder={t(
									"cancellationBankModal.uploadFile.placeholder",
								)}
								onChange={(e) => handleFileChange(e)}
								onDismissHandler={() =>
									updateCancellation("userIbanCertificate", "")
								}
								validationMsg={fileError}
								validationType={fileError ? "error" : "success"}
								data-testid="upload-file"
							/>
						</div>
					</div>
				}
				onClose={() => {
					handleCloseCancellationBankModal();
					dispatch(reservationSlice.actions.resetCancellationData());
				}}
				dialogHeaderProps={{
					leadIcon: true,
					trailIcon: true,
					trailIconProps: {
						onClick: () => {
							handleCloseCancellationBankModal();
							dispatch(reservationSlice.actions.resetCancellationData());
						},
					},
					leadIconProps: {
						onClick: () => {
							handleCloseCancellationBankModal();
							handleOpenCancellationReasonModalOpen(true);
						},
					},
					label: t("cancellationBankModal.title"),
					type: "centred",
					hasAsset: true,
					assetProps: {
						children: <RoshnLogoHorizontal />,
						size: "128px",
					},
				}}
				showFooter={true}
				buttonsGroup={{
					direction: "vertical",
					buttons: [
						<RDSButton
							variant={"primary"}
							size={"md"}
							text={t("cancellationBankModal.primaryCta")}
							onClick={() => handleSubmitCancellationRequest()}
							disabled={!canContinueCancellationBankDetail}
							data-testid="refund-bank-confirm"
						/>,
						<RDSButton
							variant={"secondary"}
							size={"md"}
							text={t("cancellationBankModal.secondaryCta")}
							onClick={() => {
								handleCloseCancellationBankModal();
								dispatch(reservationSlice.actions.resetCancellationData());
							}}
							data-testid="refund-bank-discard"
						/>,
					],
				}}
			/>
		</div>
	);
};
