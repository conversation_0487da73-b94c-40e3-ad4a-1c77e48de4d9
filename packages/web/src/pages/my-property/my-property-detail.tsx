import { Container } from "@/components/container/container";
import { Divider } from "@/components/divider";
import {
	ArrowLeftIcon,
	ArrowRightIcon,
	CallIcon,
	ChevronLeftV2Icon,
	ChevronRightV2Icon,
	DetailIcon,
	InfoV2Icon,
	LoadingIcon,
} from "@/components/icons";
import { Typography } from "@/components/typography";
import { MyPropertyDetailCard } from "@/features/my-properties";
import { SalesAdvisor } from "@/features/my-properties/sales-advisor";
import { ConstructionProgress } from "@/features/reservation/components/construction-progress";
import { useScreenSize } from "@/hooks/media-query-hooks";
import { withAuthenticated } from "@/layouts/auth/with-authenticated";
import { MyPropertyDetailSkeleton } from "@/pages/my-property/my-properties-skeleton";
import { generateAppPath, useAppPath } from "@/routes";
import { AppPaths } from "@/routes/app-paths";
import { AppTheme, desktopMQ, tabletMQ } from "@/theme";
import { css, useTheme } from "@emotion/react";
import {
	Navigate,
	Outlet,
	Link as RouterLink,
	matchPath,
	useLocation,
	useMatches,
	useNavigate,
	useParams,
} from "@remix-run/react";
import * as React from "react";
import { useTranslation } from "react-i18next";
import { useMyPropertyDetail } from "@/features/my-properties/hooks";
import { ParamParseKey } from "@remix-run/router";
import { PROPERTY_ERROR } from "@/features/my-properties/types";
import { AccountService, usePropertyDetail } from "@roshn/shared/es/features";
import { useFeatureFlag, useFeatureFlagApi } from "@/features/feature-flags";
import { ButtonIcon } from "@/components/button-icon";
import { Root } from "@radix-ui/react-portal";
import { AsyncButton } from "@/components/button/async-button";
import { StepsCompletion } from "@/features/my-properties/utils";
import { ShadowGreen1 } from "@/theme/palette/all-colors";
import ActionBanner from "./action-banner";
import { DocumentTaskModal } from "@/features/signature-input/components/document-task-modal";
import { useFreshCallback } from "rooks";
import { AppState, useAppDispatch, useAppSelector } from "@/store";
import {
	getResignDocumentsListByUnitCode,
	resetReservationCancellationStatus,
} from "@/features/reservation/redux";
import { RESIGN_DOCUMENT_STATUS } from "@/features/resign-documents/constant";
import PendingActionModal from "@/features/thank-u-modal/thank-u-modal";
import { PostContactDialog } from "@/features/property-finder/search-result/result/post-contact-dialog";
import { useRequestProperties } from "@/features/property-finder/hooks/use-search-property";
import { RDSButton, RDSCallout, RDSLink as Link } from "@roshn/ui-kit";
import { desktopMarginTop, mobileMarginTop } from "@/utils/rds-navBar-utils";
import CancelReservationDialog from "./cancel-reservation-dialog/cancel-reservation-dialog";
import {
	ReservationCancellationStatus,
	ResignDocumentsListData,
	useInjection,
} from "@roshn/shared";
import { emailVerificationModalSlice } from "@/store/global-modal-slices";
import { useSelector } from "react-redux";
import {
	propertyTypeKeyMap,
	unitClassification,
} from "@/utils/property-type-utils";

const styles = {
	bottomBar: (theme: AppTheme) =>
		css({
			paddingInline: theme.spaces.md,
		}),
	container: (theme: AppTheme, enableNewProfilePage?: boolean) =>
		css({
			paddingBottom: theme.spaces.xl,
			marginTop: enableNewProfilePage ? mobileMarginTop : "unset",
			[tabletMQ(theme)]: {
				marginTop: enableNewProfilePage ? desktopMarginTop : "unset",
			},
		}),
	contentWrapper: (theme: AppTheme) =>
		css({
			display: "flex",
			flexDirection: "column",
			marginBlockEnd: theme.spaces.xxl,
			width: "100%",
			[desktopMQ(theme)]: {
				display: "grid",
				gap: theme.spaces.xl4,
				gridTemplateAreas: `
					'content card'
					'content sales'
				`,
				gridTemplateColumns: "2fr 1fr",
			},
		}),

	detailContainer: css({
		gridArea: "content",
	}),
	divider: (theme: AppTheme) =>
		css({
			marginBlock: theme.spaces.lg,
			[desktopMQ(theme)]: {
				display: "none",
			},
		}),
	kitchenButton: (theme: AppTheme) =>
		css({
			background: theme.colors.common.white,
			boxShadow: "0px 0px 8px 0px rgba(0, 0, 0, 0.12)",
			":hover": {
				background: theme.colors.common.white,
			},
		}),
	kitchenContainer: (theme: AppTheme) =>
		css({
			marginBottom: theme.spaces.xl3,
			background: theme.colors.primary.light,
			borderRadius: theme.borderRadius.lg,
			display: "flex",
			justifyContent: "space-between",
			alignItems: "center",
			padding: "32px 24px",
			gap: "24px",
			cursor: "pointer",
		}),
	kitchenContainerRebranded: (theme: AppTheme) =>
		css({
			marginBottom: theme.spaces.xl3,
			background: ShadowGreen1,
			borderRadius: theme.borderRadius.lg,
			display: "flex",
			justifyContent: "space-between",
			alignItems: "center",
			padding: "32px 24px",
			gap: "24px",
			cursor: "pointer",
		}),
	infoContainer: (theme: AppTheme) =>
		css({
			marginBottom: theme.spaces.xl3,
			border: ` 1px solid ${theme.colors.grey[300]}`,
			borderRadius: theme.borderRadius.md,
			display: "flex",
			justifyContent: "space-between",
			alignItems: "center",
			padding: "1.5rem 1rem",
		}),
	flexContainer: (theme: AppTheme) =>
		css({
			display: "flex",
			alignItems: "center",
			gap: theme.spaces.xs3,
		}),
	text: (theme: AppTheme) =>
		css({
			color: theme.colors.grey[900],
		}),
	button: css({
		padding: "0px",
	}),
	navigationWrapper: (theme: AppTheme) =>
		css({
			display: "flex",
			marginBlock: theme.spaces.xl4,
			svg: {
				fontSize: "0.75em",
				marginInlineEnd: "0.5em",
			},
		}),
	pageTitle: (theme: AppTheme) =>
		css({
			...theme.typography.h5,
			[tabletMQ(theme)]: {
				...theme.typography.h3,
			},
			svg: {
				transform: `rotate(${theme.direction == "rtl" ? 180 : 0}deg)`,
			},
		}),
	salesAdvisorContainer: (theme: AppTheme) =>
		css({
			gridArea: "sales",
			marginBlockStart: theme.spaces.xl,
			[desktopMQ(theme)]: {
				margin: 0,
			},
		}),
	unitCardContainer: css({
		gridArea: "card",
	}),
	bottomModel: (theme: AppTheme) =>
		css({
			background: "white",
			bottom: 0,
			boxShadow: theme.shadows.sm,
			left: 0,
			padding: `${theme.spaces.lg} 0`,
			position: "fixed",
			width: "100%",
			zIndex: theme.zIndices.appBar - 1,
		}),
	bottomModelContent: (theme: AppTheme) =>
		css({
			display: "flex",
			justifyContent: "center",

			[tabletMQ(theme)]: {
				justifyContent: "end",
			},
		}),
	propertyManagementContainer: (theme: AppTheme) =>
		css({
			display: "flex",
			flexDirection: "column",
			justifyContent: "flex-start",
			alignItems: "flex-start",
			padding: "26px",
			alignSelf: "stretch",
			borderRadius: "1.5rem",
			background: "#F5F5F5",
			marginBottom: "16px",
		}),
	propertyManagementTitle: () =>
		css({
			display: "flex",
			width: "100%",
			alignItems: "center",
			gap: "1.5rem",
		}),

	propertyManagementHeading: (theme: AppTheme) =>
		css({
			color: "#212121",
			fontFamily: "IBM Plex Sans Arabic",
			fontSize: "18px",
			fontStyle: "normal",
			fontWeight: 600,
			lineHeight: "120%",
			whiteSpace: "nowrap",
		}),

	propertyManagementDivider: () =>
		css({
			flexGrow: 1,
			height: "1px",
		}),

	propertyManagementCta: (theme: AppTheme) =>
		css({
			display: "flex",
			flexDirection: "column",
			alignItems: "flex-start",
			gap: "0.5rem",
			width: "100%",
		}),

	propertyManagementDetails: (theme: AppTheme) =>
		css({
			color: "#212121",
			fontFamily: "IBM Plex Sans Arabic",
			fontSize: "1rem",
			fontStyle: "normal",
			fontWeight: 400,
			lineHeight: "130%",
			textAlign: "left",
		}),

	btnStyles: () =>
		css({
			display: "flex",
			justifyContent: "flex-start",
			alignItems: "center",
			gap: "1.5rem",
			width: "100%",

			"@media (max-width: 600px)": {
				flexDirection: "column",
				gap: "0.5rem",
				width: "100%",
			},
		}),

	propertyManagementButton: (theme: AppTheme) =>
		css({
			color: "#FFF",
			fontFamily: "IBM Plex Sans Arabic",
			fontSize: "0.875rem",
			fontStyle: "normal",
			fontWeight: 400,
			lineHeight: "110%",
			letterSpacing: "0.21px",

			"@media (max-width: 600px)": {
				flexDirection: "column",
				gap: "0.5rem",
				width: "100%",
			},
		}),
	loadingIcon: (theme: AppTheme) =>
		css({
			alignItems: "center",
			animation: theme.animations.spin(),
			display: "flex",
		}),
	cancelCallout: css({ width: "100%", marginBottom: "40px" }),
	backIcon: (theme: AppTheme) =>
		css({
			width: theme.spaces.md,
			height: theme.spaces.md,
			[desktopMQ(theme)]: {
				width: theme.spaces.xl,
				height: theme.spaces.xl,
			},
		}),
};

export const MyPropertyDetailPage = withAuthenticated(
	() => {
		const { isDesktop } = useScreenSize();
		const { locale } = useParams();
		const isRTL = locale === "ar";
		const dispatch = useAppDispatch();
		const { t } = useTranslation(undefined, {
			keyPrefix: "features.myProperties.propertyDetail.steps",
		});
		const isArabic = locale === "ar";
		const { data: featureFlag } = useFeatureFlagApi();
		const accountSvc = useInjection<AccountService>(AccountService);

		const [userProfile, setUserProfile] = React.useState<any>();
		const [isEmailVerified, setIsEmailVerified] = React.useState(false);

		const isNafathVerified = accountSvc.useIsVerified();
		const isEmailModalOpen = useSelector(
			(state: AppState) => state.emailVerificationModal.open,
		);

		const getUserProfile = useFreshCallback(async () => {
			try {
				const profile = await accountSvc.getFullProfile();
				const isEmailVerify =
					profile.hasOwnProperty("isMailVerified") &&
					profile.isMailVerified === true;
				setIsEmailVerified(isEmailVerify);
				setUserProfile(profile);
			} catch (error) {
				console.error("Error fetching profile in MyPropertyDetailPage", error);
			}
		});
		React.useEffect(() => {
			getUserProfile();
		}, []);

		React.useEffect(() => {
			if (!isEmailModalOpen) {
				getUserProfile();
			}
		}, [isEmailModalOpen]);

		const handleEmailModal = React.useCallback(() => {
			dispatch(
				emailVerificationModalSlice.actions.openModal({
					isNafathVerified: isNafathVerified,
					//@ts-ignore
					open: true,
					errorType: "",
					from: "",
					rdsModal: false,
					isRdsEditModal: false,
				}),
			);
		}, [dispatch]);

		const t_ = useTranslation().t;
		const params = useParams<ParamParseKey<typeof AppPaths.myPropertyDetail>>();
		const { pathname } = useLocation();
		const navigate = useNavigate();
		const theme = useTheme();
		const { data: featureFlagAPI } = useFeatureFlagApi();
		const { enableNewProfilePage, enableCancelReservation } =
			featureFlagAPI || {};
		const [isDocumentTaskModalOpen, setDocumentTaskModal] =
			React.useState<boolean>();
		const [currentResignDoc, setCurrentResignDoc] = React.useState<any>();
		const [resignDocumentList, setResignDocumentList] = React.useState<any>();
		const [showActionBanner, setShowActionBanner] = React.useState(false);
		const [isOpenSignatureModal, setIsOpenSignatureModal] =
			React.useState(false);
		const [isThankUModalOpen, setIsThankUModalOpen] = React.useState(false);
		const [isLoadings, setIsLoading] = React.useState(false);
		const [latestHandoverDateFlag] = useFeatureFlag("latestHandoverDateFlag");
		const [worldCheckScreening] = useFeatureFlag("WorldCheckScreening");
		const isRebrandedFlag = import.meta.env.VITE_FF_REBRANDED === "TRUE";
		const kitchenContainerStyle = isRebrandedFlag
			? styles.kitchenContainerRebranded
			: styles.kitchenContainer;
		const worldCheckFeatureFlag = featureFlagAPI?.enableWorldCheckScreening;
		const resignDocumentsFeatureFlag = featureFlagAPI?.resignDocuments;
		const reservationData = useAppSelector((state) => state.reservation);

		const match = useMatches().find((match) =>
			Boolean((match.handle as any)?.crumbs),
		);
		const Crumbs = (match?.handle as any)?.crumbs;
		const { i18n } = useTranslation();
		const isLTR = i18n.dir() === "ltr";

		const { data, isLoading, error, refetch } = useMyPropertyDetail(
			params.unitId ?? "",
		);
		const currentStep = data?.step ?? StepsCompletion.STARTED;

		const unit = data?.unit;
		const source = data?.source;
		const resignDocuments = data?.resignDocuments;
		const kitchenPreferenceExistingBuyersRequired =
			data?.kitchenPreferenceExistingBuyersRequired;
		const kitchenPreferenceDocumentAvailable =
			data?.kitchenPreferenceDocumentAvailable;
		const saleAdvisorInfo = data?.salesAdvisorInfo;
		const showConstructionProgress = data?.showConstructionProgress;
		const currentProgress = unit?.currentProgress;
		//@ts-ignore
		const screeningResultStatus = data?.screeningResultStatus;
		const cantProceedAfterWCScreening = screeningResultStatus === false;

		const { unitId = "" } = useParams();
		const [showInfoDiv, setShowInfoDiv] = React.useState(false);
		const [showPropertyModal, setShowPropertyModal] = React.useState(false);
		const [formDetails, setFormDetails] = React.useState<string | null>(null);
		const [load, setLoad] = React.useState(true);
		const { data: unitData } = usePropertyDetail(unit?.unitCode!);
		const continueReservation = () => {
			setShowInfoDiv(false);
			navigate(
				generateAppPath(AppPaths.myPropertyReservationDetail, { unitId }),
			);
		};

		const handleGetResignDocumentsList = useFreshCallback(async () => {
			setIsLoading(true);
			const docData = await dispatch(
				getResignDocumentsListByUnitCode(params?.unitId ?? ""),
			).unwrap();
			setResignDocumentList(docData);
			const currentResignDocRes = docData
				? docData.find(
						(element) =>
							element.state !== RESIGN_DOCUMENT_STATUS.DOCUMENT_SIGNED_BY_USER,
					)
				: null;
			if (docData.length > 0) {
				// navigateRedirect(generatePath(AppPaths.myProperties, { locale }));
				setDocumentTaskModal(true);
			} else {
				setShowActionBanner(false);
				setIsThankUModalOpen(true);
			}

			setIsLoading(false);
		});
		const handleThankYouDoneCta = () => {
			setIsThankUModalOpen(false);
		};

		React.useEffect(() => {
			if (resignDocuments?.length > 0) {
				refetch();
				setResignDocumentList(resignDocuments);
				setShowActionBanner(true);
				const currentResignDocRes = resignDocuments?.find(
					(element: { state: string }) =>
						element.state !== RESIGN_DOCUMENT_STATUS.DOCUMENT_SIGNED_BY_USER,
				);
				setCurrentResignDoc(currentResignDocRes.documentId);
				// if (currentResignDocRes) {
				// 	setDocumentTaskModal(true);
				// }
			}
		}, [resignDocuments]);
		const { data: propertyData, isLoading: isPropertyLoading } =
			useRequestProperties("SUPPLY", showPropertyModal);

		const { sellIds, leaseIds, propertyAllowed } = React.useMemo(() => {
			const sellIds: string[] = [];
			const leaseIds: string[] = [];
			let propertyAllowed = false;

			const items: any = propertyData?.pages?.[0] || [];

			for (const item of items) {
				for (const request of item.requests) {
					if (request.type === "SELL" && request.status === "ALLOWED") {
						sellIds.push(item.propertyId);
						propertyAllowed = true;
					}
					if (request.type === "LEASE" && request.status === "ALLOWED") {
						leaseIds.push(item.propertyId);
						propertyAllowed = true;
					}
				}
			}
			return { sellIds, leaseIds, propertyAllowed };
		}, [propertyData]);
		const typeKey =
			(unit && propertyTypeKeyMap[unit.unitType?.toLowerCase()]) || "villa";
		const propertyTypeKey = `features.masterPlan.name.propertyType.${typeKey}`;

		const isPremiumCollection =
			unitData?.unitTypeClassification === unitClassification;
		const propertyTypeName = isPremiumCollection
			? isArabic
				? `${t_(propertyTypeKey)} ${t_("common.premium")}`
				: `${t_("common.premium")} ${t_(propertyTypeKey)} `
			: t_(propertyTypeKey);

		const propertyName = unit?.unitInfo
			? `${propertyTypeName} ${unit?.unitInfo?.typologyGroup ?? ""} ${
					unit?.unitInfo?.facade
						? t_(
								`features.masterPlan.name.facadeType.${unit.community?.toLowerCase()}.${unit?.unitInfo?.facade?.toLowerCase()}`,
								unit?.unitInfo?.facade ?? "",
							)
						: ""
				}`
			: "";

		const title = React.useMemo(() => {
			if (matchPath(AppPaths.myPropertyReservationDetail, pathname)) {
				return t("reservation.title");
			} else if (matchPath(AppPaths.myPropertyFinanceDetail, pathname)) {
				return t("finance.title");
			} else if (matchPath(AppPaths.myPropertyPurchaseDetail, pathname)) {
				return t("purchase.title");
			}
			return propertyName;
		}, [pathname, propertyName]);

		React.useEffect(() => {
			if (matchPath(AppPaths.myPropertyDetail, pathname)) {
				setShowInfoDiv(true);
			} else {
				setShowInfoDiv(false);
			}
		}, [pathname]);

		const detailPath = useAppPath(AppPaths.myPropertyDetail, {
			unitId: params.unitId ?? "",
		});
		const allPropertiesPath = useAppPath(AppPaths.myProperties);

		if (
			(error as Error | undefined)?.message ===
			PROPERTY_ERROR.PROPERTY_NOT_FOUND
		) {
			return <Navigate to={allPropertiesPath} replace={true} />;
		}

		if (!unit && isLoading) {
			return <MyPropertyDetailSkeleton />;
		}
		const handleOpenKitchenPreferences = () => {
			const kitchenPath = generateAppPath(AppPaths.myKitchenPreferences, {
				unitId: unit?.id,
			});
			navigate(kitchenPath);
		};
		const handleMyPropertyButton = (supply: string) => {
			setFormDetails(supply);
			setShowPropertyModal(true);
		};

		const continuePurchase = () =>
			navigate(
				useAppPath(AppPaths.reservationPurchaseComplete, {
					communityName: unit?.community?.toLowerCase() ?? "",
					groupId: unit?.typology ?? "",
					unitId,
				}),
			);
		const cancellationStatus =
			reservationData.data.reservationCancellationStatus ??
			data?.reservationCancellationStatus;

		const isCancellationRequestSigned = [
			ReservationCancellationStatus.DOCUMENT_SIGNED_BY_USER,
		].includes(cancellationStatus!);

		const showCancellationRequest =
			enableCancelReservation && isCancellationRequestSigned;

		const cancellationEligibility = data?.cancellationEligibility;

		const isCancellationRequestBeenSubmitted = Boolean(cancellationStatus);

		const showCancellationModal =
			cancellationEligibility || isCancellationRequestBeenSubmitted;

		const showIfScreeningError =
			showInfoDiv &&
			cantProceedAfterWCScreening &&
			worldCheckFeatureFlag &&
			currentStep == 4;

		const mappedStepsItems = (resignDocumentList ?? []).map(
			(item: ResignDocumentsListData) => ({
				...item,
				documentTitleAr: item.documentTitleAr ?? item.documentNameAr,
				documentTitleEn: item.documentTitleEn ?? item.documentNameEn,
			}),
		);

		const actionBannerArgs = {
			buttonCta: {
				cta: {
					onClick: () => {
						if (!isEmailVerified || !isNafathVerified) {
							handleEmailModal();
						} else {
							handleGetResignDocumentsList();
						}
					},
					text: t_("features.resignDocuments.propertyDetailBanner.cta"),
				},
				variant: "tertiary",
				size: "md",
			},
			bannerTitle: t_("features.resignDocuments.propertyDetailBanner.heading"),
			numOfDocs: resignDocumentList?.length,
			bannerBody: {
				bannerContentSection1: t_(
					"features.resignDocuments.propertyDetailBanner.message1",
				),
				bannerContentSection2: t_(
					"features.resignDocuments.propertyDetailBanner.message2",
				),
			},
		};

		const thankUContent = {
			heading: t_("features.resignDocuments.thankYou.heading"),
			message: t_("features.resignDocuments.thankYou.message"),
			btn: t_("features.resignDocuments.thankYou.cta"),
		};
		const sellMyPropertyDisabled =
			isPropertyLoading || !sellIds?.includes(data?.unit?.id);
		const leaseMypropertyDisabled =
			isPropertyLoading || !leaseIds?.includes(data?.unit?.id);

		return (
			<main data-testid="my-property-detail">
				<React.Suspense fallback={<MyPropertyDetailSkeleton />}>
					<Container css={styles.container(theme, enableNewProfilePage)}>
						{isDesktop ? <Crumbs propertyName={propertyName} /> : null}
						<div css={styles.navigationWrapper}>
							<Typography
								css={styles.pageTitle}
								onClick={() => {
									dispatch(resetReservationCancellationStatus());
								}}
							>
								<Link
									component={RouterLink}
									to={
										!matchPath(AppPaths.myPropertyDetail, pathname)
											? detailPath
											: allPropertiesPath
									}
								>
									{isRTL ? (
										<ArrowRightIcon css={styles.backIcon} />
									) : (
										<ArrowLeftIcon css={styles.backIcon} />
									)}
								</Link>
								{title}
							</Typography>
						</div>
						<div css={styles.contentWrapper}>
							<div css={styles.unitCardContainer}>
								<ConstructionProgress
									progress={currentProgress}
									showConstructionProgress={showConstructionProgress}
									projectName={unit?.project}
								/>
								<MyPropertyDetailCard
									latestHandoverDateFlag={latestHandoverDateFlag}
									unit={unit}
									exteriorPreview={unit?.exteriorPreview?.url ?? ""}
									handoverDate={unit?.estDeliveryDate}
									showConstructionProgress={showConstructionProgress}
								/>
							</div>
							<Divider thickness={1} css={styles.divider} />
							<div css={styles.detailContainer}>
								{featureFlag?.enableBrokerageFeature &&
									(data?.status === "Purchased" ||
										data?.status === "Reserved") &&
									propertyAllowed && (
										<div css={styles.propertyManagementContainer}>
											<div css={styles.propertyManagementTitle}>
												<p css={styles.propertyManagementHeading}>
													{t_("homePagev4.multiStepForm.CardHeading")}
												</p>
												<Divider
													color={"#212121"}
													css={styles.propertyManagementDivider}
												/>
											</div>
											<div css={styles.propertyManagementCta}>
												<p css={styles.propertyManagementDetails}>
													{t_("homePagev4.multiStepForm.CardSubHeading")}
												</p>
												<div css={styles.btnStyles}>
													<RDSButton
														variant="primary"
														size="md"
														disabled={sellMyPropertyDisabled}
														css={styles.propertyManagementButton}
														onClick={
															sellMyPropertyDisabled
																? undefined
																: () => handleMyPropertyButton("Sell")
														}
														text={
															isPropertyLoading ? (
																<span css={styles.loadingIcon}>
																	<LoadingIcon size={24} />
																</span>
															) : (
																t_("homePagev4.multiStepForm.SellMyProperties")
															)
														}
													/>
													<RDSButton
														variant="primary"
														size="md"
														disabled={leaseMypropertyDisabled}
														css={styles.propertyManagementButton}
														onClick={
															leaseMypropertyDisabled
																? undefined
																: () => handleMyPropertyButton("Lease")
														}
														text={
															isPropertyLoading ? (
																<span css={styles.loadingIcon}>
																	<LoadingIcon size={24} />
																</span>
															) : (
																t_("homePagev4.multiStepForm.LeaseMyProperties")
															)
														}
													/>
												</div>
											</div>
										</div>
									)}
								{formDetails && showPropertyModal && (
									<PostContactDialog
										open={showPropertyModal}
										onClose={() => {
											setShowPropertyModal(false);
											setLoad(true);
										}}
										data={data}
										formDetails={formDetails}
										load={load}
										setLoad={setLoad}
									/>
								)}
								{showInfoDiv &&
									featureFlagAPI?.enableKitchenPreference &&
									kitchenPreferenceExistingBuyersRequired &&
									!kitchenPreferenceDocumentAvailable &&
									!showActionBanner && (
										<div
											css={kitchenContainerStyle}
											onClick={handleOpenKitchenPreferences}
										>
											<Typography variant="subtitleM">
												{t("reservation.kitchenPreferences.title")}
												{t("reservation.kitchenPreferences.date")}{" "}
												{t("reservation.kitchenPreferences.title2")}
											</Typography>
											<ButtonIcon
												onClick={handleOpenKitchenPreferences}
												css={styles.kitchenButton}
											>
												{isLTR ? <ChevronRightV2Icon /> : <ChevronLeftV2Icon />}
											</ButtonIcon>
										</div>
									)}
								{/*  Cancellation request */}
								{showCancellationRequest && (
									<div css={styles.cancelCallout}>
										<RDSCallout
											title={t_(
												"pages.cancelReservation.cancellationCallout.title",
											)}
											description={t_(
												"pages.cancelReservation.cancellationCallout.description",
											)}
											actionText={
												<span dir="ltr">
													{t_(
														"pages.cancelReservation.cancellationCallout.cta",
													)}
												</span>
											}
											appearance="info"
											actionUrl={`tel:${t_(
												"pages.cancelReservation.cancellationCallout.cta",
											)}`}
											leadIcon={true}
											dismissible={false}
											actionLeadIcon={<CallIcon />}
											css={{ width: "100%" }}
										/>
									</div>
								)}
								<div css={styles.detailContainer}>
									{showInfoDiv &&
										featureFlagAPI?.enableKitchenPreference &&
										!kitchenPreferenceExistingBuyersRequired &&
										kitchenPreferenceDocumentAvailable &&
										!showActionBanner && (
											<div css={styles.infoContainer}>
												<div css={styles.flexContainer}>
													<InfoV2Icon />
													<Typography css={styles.text} variant="subtitleM">
														{t(
															"reservation.kitchenPreferences.kitchenConfirmedMessage",
														)}
													</Typography>
												</div>
												<RDSButton
													variant="tertiary"
													size="sm"
													css={styles.button}
													onClick={continueReservation}
													text={t("reservation.kitchenPreferences.viewDetail")}
												/>
											</div>
										)}
								</div>
								{/*  AML WC result */}
								{showIfScreeningError && (
									<div css={styles.detailContainer}>
										<div css={styles.infoContainer}>
											<div css={styles.flexContainer}>
												<div css={{ width: "16px", height: "16px" }}>
													<InfoV2Icon css={{ width: "16px", height: "16px" }} />
												</div>
												<Typography css={styles.text} variant="subtitleM">
													{t("reservation.worldCheckMessage")} {propertyName}
												</Typography>
											</div>
										</div>
									</div>
								)}
								{/*  Resign Document */}
								{resignDocumentsFeatureFlag &&
									showActionBanner &&
									showInfoDiv && (
										<ActionBanner
											{...actionBannerArgs}
											currentResignDoc={currentResignDoc}
											stepsItems={mappedStepsItems}
										/>
									)}
								<Outlet />
							</div>

							{saleAdvisorInfo && (
								<SalesAdvisor
									{...saleAdvisorInfo}
									css={styles.salesAdvisorContainer}
								/>
							)}
						</div>
					</Container>
					<PendingActionModal
						isOpen={isThankUModalOpen}
						content={thankUContent}
						onClose={handleThankYouDoneCta}
					/>
					<DocumentTaskModal
						handleGetResignDocumentsList={handleGetResignDocumentsList}
						isDocumentTaskModalOpen={isDocumentTaskModalOpen}
						setDocumentTaskModal={setDocumentTaskModal}
						currentResignDoc={currentResignDoc}
						setCurrentResignDoc={setCurrentResignDoc}
						stepsDetails={{
							headingQuestion: t(
								"features.resignDocuments.documentModal.subHeading",
							),
							stepsItems: resignDocumentList,
						}}
						setIsOpenSignatureModal={setIsOpenSignatureModal}
					/>
					{enableCancelReservation && showCancellationModal && (
						<CancelReservationDialog
							reservationId={data?.id ?? ""}
							reservationCancellationStatus={
								data?.reservationCancellationStatus
							}
							financingMethod={data?.financialInfo?.financingMethod}
						/>
					)}
				</React.Suspense>
			</main>
		);
	},
	{
		mustVerify: true,
	},
);
