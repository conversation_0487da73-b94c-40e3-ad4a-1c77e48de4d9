import React, {
	useState,
	useMemo,
	useC<PERSON>back,
	useEffect,
	useRef,
} from "react";
import {
	RDSButton,
	RDSTagInteractive,
	RDSSwitch,
	RDSIconButton,
	RDSSelectSlotDropdown,
	RDSRangeSlider,
	RDSTextInput,
	RDSTabGroup,
	RDSPagination,
	RDSRadioGroup,
	RDSRadio,
	RDSCheckboxGroup,
	RDSSlider,
	RDSSelect,
	Image,
	RDSLinkButton,
	RDSEmptyState,
	RDSTypography,
} from "@roshn/ui-kit";
import PropertyCard from "./property-card";
import { css, Theme, useTheme } from "@emotion/react";
import { useTranslation } from "react-i18next";
import { useParams, useSearchParams } from "@remix-run/react";
import { FilterIcon, SquareWarningIcon } from "@/components/icons";
import { desktopMQ, mobileMQ, tabletMQ } from "@/theme";
import { BedsAndBathsFilter, MoreFiltersDrawer } from "./filter-component";
import {
	useListViewProperties,
	useGetDevelopers,
} from "@/features/property-finder/hooks/use-search-property";
import { FilterUnitsArg } from "@/features/property-finder/filter-schema";
import * as _ from "lodash-es";
import { PropertiesResultSkeleton } from "@/features/property-finder/search-result/result/properties-result-skeleton";

const styles = {
	cardRow: (theme: any) =>
		css({
			alignItems: "flex-start",
			display: "grid",
			"& > *": {
				maxWidth: "411px",
				[mobileMQ(theme)]: {
					maxWidth: "100%",
					"> div": {
						maxWidth: "100%",
						minWidth: "unset",
					},
				},
			},
			gap: theme.spaces.lg,
			"&:has(:nth-of-type(2)):not(:has(:nth-of-type(3)))": {
				gridTemplateColumns: "repeat(2, minmax(352px, auto))",
			},
			gridTemplateColumns: "repeat(auto-fit, minmax(352px, auto))",
			"@media (width: 540px) and (height: 720px)": {
				justifyContent: "flex-start",
			},
			justifyContent: "center",
			[mobileMQ(theme)]: {
				gridTemplateColumns: "repeat(auto-fit, minmax(250px, auto))",
			},
		}),
	buttonWrapper: css({
		display: "inline-block",
		position: "relative",
	}),
	container: (theme: any) =>
		css({
			alignItems: "flex-start",
			display: "flex",
			flexWrap: "wrap",
			gap: theme.spaces.xl,
			justifyContent: "space-between",
			margin: "0 auto",
			width: "100%",
			[mobileMQ(theme)]: {
				alignItems: "flex-start",
				flexDirection: "column",
				gap: theme.spaces.lg,
			},
		}),
	content: (theme: any) =>
		css({
			display: "flex",
			flex: "1",
			flexDirection: "column",
			gap: theme.spaces.xs3,
		}),
	filterMoreContainer: css({
		"> div": {
			"&:first-of-type": {
				maxWidth: "26.5rem",
				top: "6rem",
			},
			"&:last-of-type": {
				display: "none",
			},
			[`@media (min-width: 767px) and (max-width: 1024px)`]: {
				"&:first-of-type": {
					top: "17rem",
				},
			},
		},
	}),
	filtersContainer: (theme: Theme) =>
		css({
			alignItems: "flex-end",
			display: "flex",
			flexDirection: "row",
			gap: "1rem",
			justifyContent: "flex-start",
			order: 2,
			button: {
				alignSelf: "flex-end",
				minWidth: "unset",
			},
			padding: "1rem",

			[`@media (max-width: 540px)`]: {
				alignItems: "flex-end",
				gap: "0.5rem",
				"> div": {
					"&:first-of-type": {
						flex: 1,
						display: "none",
					},
				},
				padding: 0,
			},

			[mobileMQ(theme)]: {
				alignItems: "flex-end",
				gap: "0.5rem",
				"> div": {
					"&:first-of-type": {
						flex: 1,
					},
				},
				padding: 0,
			},

			width: "100%",
		}),
	heading: (theme: Theme) =>
		css({
			color: theme?.rds?.color.text.ui.primary,
			marginBlock: "0 !important",
			...theme?.rds?.typographies.heading.h3,
			[mobileMQ(theme)]: {
				...theme?.rds?.typographies.heading.h5,
			},
		}),
	iconButton: (theme: Theme) =>
		css({
			alignItems: "center",
			background: "#fff",
			border: `1px solid #000`,
			display: "flex",
			"& svg": {
				"& path": {
					stroke: `${theme?.rds?.colors.black} !important`,
				},
			},
			top: 0,
			":hover": {
				background: "inherit",
				color: `${theme?.rds?.colors.black} !important`,
			},
			height: "2.5rem",
			maxHeight: "2.5rem",
			maxWidth: "2.5rem",
			width: "2.5rem",
		}),
	mainSection: (theme: any, hasPhases: boolean) =>
		css({
			alignItems: "center",
			display: "flex",
			alignSelf: "stretch",
			flexDirection: "column",
			backgroundColor: "#fff",
			[mobileMQ(theme)]: {
				gap: theme.spaces.lg,
			},
			[tabletMQ(theme)]: {
				gap: theme.spaces.xl3,
			},
			[desktopMQ(theme)]: {
				gap: hasPhases ? "5rem" : theme.spaces.xl3,
			},
		}),
	priceDrop: (theme: Theme) =>
		css({
			[`@media (max-width: 992px)`]: {
				maxWidth: "none",
				width: "100%",
				marginTop: theme?.rds?.spaces.xs2,
			},
			"> div": {
				paddingBottom: "1rem",
				width: "100%",
				'[data-disabled="false"]': {
					display: "none",
				},
				input: {
					width: "100%",
				},
				"& > div > div > div > div > div > div > p": {
					margin: 0,
				},
				"> div": {
					"> div": {
						"> p": { margin: 0 },
					},
				},
			},
			flexBasis: "160px",
			[`@media (max-width: 767px)`]: {
				display: "none",
			},

			flexGrow: 1,

			display: "block",
			flexShrink: 1,
		}),
	propertiesLoadingOverlay: css({
		"&::after": {
			content: '""',
			position: "absolute",
			left: 0,
			top: 0,
			bottom: 0,
			right: 0,
			background: "rgba(255, 255, 255, 0.7)",
			alignItems: "center",
			display: "flex",
			justifyContent: "center",
			zIndex: 10,
		},
		minHeight: "400px",
		opacity: 0.6,
		pointerEvents: "none",
		position: "relative",
	}),
	propertySection: (theme: any) =>
		css({
			display: "flex",
			flexDirection: "column",
			gap: theme.spaces.xl3,
			width: "100%",
			[mobileMQ(theme)]: {
				gap: theme.spaces.lg,
			},
		}),
	sectionContainer: css({
		display: "flex",
		flexDirection: "column",
		gap: "1.5rem",
	}),
	subtitle: (theme: Theme) =>
		css({
			color: theme?.rds?.color.text.ui.secondary,
			maxWidth: "600px",
			...theme?.rds?.typographies.label.lg,
			[mobileMQ(theme)]: {
				...theme?.rds?.typographies.label.md,
			},
		}),
	sectionTabsWrapper: (theme: Theme) =>
		css({
			borderBottom: `1px solid ${theme?.rds?.color.border.ui.primary}`,
			display: "flex",
			flexDirection: "column",
			gap: "1.5rem",
			paddingBottom: "1.5rem",
		}),
	tabsGroup: (theme: any) =>
		css({
			display: "flex",
			flexDirection: "row",
			gap: "16px",
			[mobileMQ(theme)]: {
				order: -1,
				width: "100%",
				overflow: "hidden",
				"& > div": {
					display: "flex",
					flexDirection: "row",
					overflowX: "auto",
					whiteSpace: "nowrap",
					flexWrap: "wrap",
					width: "100%",
					maxWidth: "100%",
					scrollbarWidth: "none",
					"&::-webkit-scrollbar": { display: "none" },
				},
			},
		}),
	sectionTabsWrapperEnd: css({
		borderBottom: "none",
		paddingBottom: 0,
	}),
	advancedPriceDrop: css({
		display: "flex",
		flexDirection: "column",
		gap: "1rem",
		maxWidth: 220,
		p: {
			margin: 0,
		},
	}),
	selectAll: css({
		cursor: "pointer",
	}),
	showAll: css({
		cursor: "pointer",
	}),
	tabsScrollable: (theme: any) =>
		css({
			display: "flex",
			flexDirection: "row",
			gap: theme.spaces.md,
			overflowX: "auto",
			flexWrap: "nowrap",
			whiteSpace: "nowrap",
			maxWidth: "100%",
			width: "100%",
			"&::-webkit-scrollbar": { display: "none" },
			scrollbarWidth: "none",
		}),
	subsectionSelect: css({
		display: "flex",
		alignItems: "center",
		justifyContent: "space-between",
		button: {
			fontSize: "0.75rem",
			lineHeight: "1rem",
			fontWeight: 400,
		},
		width: "100%",
	}),
	tabsScrollableWrap: (theme: any) =>
		css({
			flexShrink: 0,
			minWidth: 123,
			"> div": {
				padding: "1.25rem",
				"> div": {
					justifyContent: "center",
					lineHeight: "1.25rem",
				},
			},
			[mobileMQ(theme)]: {
				flex: 1,
			},
		}),
	subsectionShowAll: (theme: Theme) =>
		css({
			display: "flex",
			borderBottom: `1px solid ${theme?.rds?.color.border.ui.primary}`,
			flexDirection: "column",
			gap: "1rem",
			paddingBottom: "1rem",
		}),
	subsectionTitle: css({
		fontSize: "1rem",
		fontWeight: "500",
		lineHeight: "1.25rem",
		margin: "0 0 1rem 0",
	}),
	subsectionTitleEnd: css({
		margin: "0",
	}),
	tabsContainer: css({
		"> div": {
			"> div": {
				"> div": {
					justifyContent: "center",
				},
				flex: 1,
				fontSize: "14px",
				lineHeight: "16px",
				padding: "1rem 1.5rem",
			},
			[`@media (min-width: 767px) and (max-width: 1024px)`]: {
				flexWrap: "wrap",
			},
			alignItems: "center",
			display: "flex",
		},
	}),
	tagContainer: css({
		display: "flex",
		flexWrap: "wrap",
		gap: "0.5rem",
	}),
	tagContainerOverflow: css({
		"&::-webkit-scrollbar": {
			display: "none",
		},
		"> div": {
			minWidth: "unset",
			padding: "0 1rem",
			whiteSpace: "nowrap",
		},
		flexWrap: "nowrap",
		overflow: "auto",
	}),
};

export const DeveloperPropertiesSection = () => {
	const { locale } = useParams();
	const { projectId } = useParams();
	const { developerId } = useParams();
	const theme = useTheme();
	const propertiesSectionRef = useRef<HTMLDivElement>(null);

	const { t } = useTranslation();
	const { t: tGlobal } = useTranslation();

	const MIN_PRICE = 0;
	const MAX_PRICE = 10000000;
	const PRICE_STEP = 100000;
	const SEARCH_DEBOUNCE_DELAY = 500;
	const PRICE_DEBOUNCE_DELAY = 800;

	const [bedsAndBaths, setBedsAndBaths] = useState({
		Bathrooms: [],
		Bedrooms: [],
	});
	const [price, setPrice] = useState({
		maxPrice: 10000000,
		minPrice: 0,
		priceRange: [0, 10000000] as [number, number],
	});

	const [debouncedPrice, setDebouncedPrice] = useState({
		maxPrice: 10000000,
		minPrice: 0,
		priceRange: [0, 10000000] as [number, number],
	});

	const [isMoreFiltersOpen, setIsMoreFiltersOpen] = useState(false);
	const [propertyType, setPropertyType] = useState<string[]>([]);
	const [propertyCategory, setPropertyCategory] = useState("residential");
	const [isMobile, setIsMobile] = useState(false);

	const [searchInputValue, setSearchInputValue] = useState("");
	const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");

	const [tempSelectedCheckboxes, setTempSelectedCheckboxes] = useState<{
		[key: string]: string[];
	}>({});
	const [tempSelectedValues, setTempSelectedValues] = useState<{
		[key: string]: any;
	}>({});
	const [tempSelectedRadios, setTempSelectedRadios] = useState<{
		[key: string]: string;
	}>({});
	const [tempTextInputValues, setTempTextInputValues] = useState<{
		[key: string]: string;
	}>({});
	const [tempSliderValue, setTempSliderValue] = useState<number>(0);
	const [tempSelectedDevelopers, setTempSelectedDevelopers] = useState([]);
	const [tempShowSoldProperties, setTempShowSoldProperties] = useState(false);

	const [appliedCheckboxes, setAppliedCheckboxes] = useState<{
		[key: string]: string[];
	}>({});
	const [appliedValues, setAppliedValues] = useState<{ [key: string]: any }>(
		{},
	);
	const [appliedRadios, setAppliedRadios] = useState<{
		[key: string]: string;
	}>({});
	const [appliedTextInputValues, setAppliedTextInputValues] = useState<{
		[key: string]: string;
	}>({});
	const [appliedSliderValue, setAppliedSliderValue] = useState<number>(0);
	const [appliedSelectedDevelopers, setAppliedSelectedDevelopers] = useState(
		[],
	);
	const [appliedShowSoldProperties, setAppliedShowSoldProperties] =
		useState(false);

	const [showAllAmenities, setShowAllAmenities] = useState(false);
	const [visibleAmenityCount, setVisibleAmenityCount] = useState(4);
	const [resetTrigger, setResetTrigger] = useState(false);

	const { data: developerApiData } = useGetDevelopers(
		"brokerage-properties-dev",
	);
	const propertyData = developerApiData?.data?.BuilderName?.values;

	const { developerKey } = useMemo(() => {
		const slug = projectId || developerId;
		if (!slug || !propertyData)
			return { developerKey: null, matchedDeveloper: null };

		const slugLower = slug.toLowerCase();

		const matchingDeveloper = propertyData.find((dev) => {
			const devKey = dev.key.toLowerCase();
			const slugNormalized = slugLower
				.replace(/-/g, " ")
				.replace(/real estate|company/g, "")
				.trim();
			const keyNormalized = devKey
				.toLowerCase()
				.replace(/real estate|company/g, "")
				.trim();

			const matches =
				slugLower.includes(devKey.toLowerCase()) ||
				devKey.toLowerCase().includes(slugLower) ||
				slugNormalized.includes(keyNormalized) ||
				keyNormalized.includes(slugNormalized) ||
				Object.values(dev.translations || {}).some((translation) => {
					const translationLower = translation.toLowerCase();
					return (
						slugLower.includes(translationLower) ||
						translationLower.includes(slugLower) ||
						slugNormalized.includes(
							translationLower.replace(/real estate|company/g, "").trim(),
						)
					);
				});

			return matches;
		});

		return {
			developerKey: matchingDeveloper?.key || null,
			matchedDeveloper: matchingDeveloper || null,
		};
	}, [projectId, developerId, propertyData]);

	const debouncedUpdateSearch = useMemo(
		() =>
			_.debounce((newSearchTerm: string, currentDebouncedTerm: string) => {
				if (newSearchTerm !== currentDebouncedTerm) {
					setDebouncedSearchTerm(newSearchTerm);
				}
			}, SEARCH_DEBOUNCE_DELAY),
		[SEARCH_DEBOUNCE_DELAY],
	);

	const debouncedUpdatePrice = useMemo(
		() =>
			_.debounce((newPrice: any) => {
				setDebouncedPrice(newPrice);
			}, PRICE_DEBOUNCE_DELAY),
		[PRICE_DEBOUNCE_DELAY],
	);

	useEffect(() => {
		debouncedUpdateSearch(searchInputValue, debouncedSearchTerm);
		return () => {
			debouncedUpdateSearch.cancel();
		};
	}, [searchInputValue, debouncedSearchTerm, debouncedUpdateSearch]);

	useEffect(() => {
		debouncedUpdatePrice(price);
		return () => {
			debouncedUpdatePrice.cancel();
		};
	}, [price, debouncedUpdatePrice]);

	const actualParamsState = useMemo((): FilterUnitsArg => {
		const ANY_ARRAY = ["any"];
		const priceValue =
			debouncedPrice.maxPrice !== MAX_PRICE ||
			debouncedPrice.minPrice !== MIN_PRICE
				? debouncedPrice
				: "any";

		const getValueOrAny = (arr?: any[]) => (arr?.length ? [...arr] : ANY_ARRAY);

		const getPlotAreaValue = (value?: any) => {
			if (!value) return ANY_ARRAY;
			if (Array.isArray(value)) {
				return value.length ? [...value] : ANY_ARRAY;
			}
			if (typeof value === "string" && value.trim() !== "") {
				return [value];
			}
			return ANY_ARRAY;
		};

		const getDeveloperValues = (arr?: any[]) => {
			if (!arr || !Array.isArray(arr) || arr.length === 0) {
				return developerKey ? [developerKey] : ["any"];
			}
			return arr.map((d) => d.value || d).filter(Boolean);
		};

		const extractValue = (item: any): string => {
			if (typeof item === "object" && item !== null && "value" in item) {
				return item.value;
			}
			return item;
		};

		const processFilterValue = (value: any): string[] => {
			if (!value) return ANY_ARRAY;

			if (Array.isArray(value)) {
				if (value.length === 0) return ANY_ARRAY;
				return value.map(extractValue);
			}

			const extractedValue = extractValue(value);
			return extractedValue ? [extractedValue] : ANY_ARRAY;
		};

		const body: FilterUnitsArg = {
			bathrooms: getValueOrAny(bedsAndBaths?.["Bathrooms"]),
			bedrooms: getValueOrAny(bedsAndBaths?.["Bedrooms"]),
			builderName: getDeveloperValues(appliedSelectedDevelopers),
			availability:
				appliedRadios["availability"] === t("onlyShowAvailableProperties")
					? true
					: undefined,
			communities: getValueOrAny(appliedCheckboxes?.["communities"]),
			buildingAge: processFilterValue(appliedValues?.["propertyAge"]),
			furnishing: processFilterValue(appliedValues?.["furnishing"]),
			listingType: ["Exclusive Right To Sell"],
			otherAmenities: getValueOrAny(appliedCheckboxes?.["otherAmenities"]),
			plotArea: getPlotAreaValue(appliedTextInputValues?.["Plot Area"]),
			propertyType: getValueOrAny(propertyType),
			search: debouncedSearchTerm || "",
			sortByPrice: priceValue,
			standardStatus: appliedRadios["availability"] ? ["Active"] : ANY_ARRAY,
			unitTypes: [],
		};

		return body;
	}, [
		bedsAndBaths,
		appliedCheckboxes,
		debouncedSearchTerm,
		debouncedPrice,
		propertyType,
		appliedTextInputValues,
		appliedSelectedDevelopers,
		appliedValues,
		appliedRadios,
		developerKey,
		t,
		MIN_PRICE,
		MAX_PRICE,
	]);

	const [searchParams, setSearchParams] = useSearchParams({
		page: "1",
	});
	const pageParam = parseInt(searchParams.get("page") || "1", 10);
	const currentPage = pageParam < 1 ? 1 : pageParam;

	const {
		data: apiData,
		isLoading,
		error,
	} = useListViewProperties(actualParamsState, currentPage);

	useEffect(() => {
		if (!isLoading && currentPage > 1 && propertiesSectionRef.current) {
			setTimeout(() => {
				propertiesSectionRef.current?.scrollIntoView({
					behavior: "smooth",
					block: "start",
				});
			}, 100);
		}
	}, [isLoading, currentPage]);

	const data = apiData;

	const totalCount = data?.pageParams?.[0]?.total || data?.pages?.length || 0;
	const pageSize = data?.pageSize || 12;
	const totalPages = Math.ceil(totalCount / pageSize);

	const propertyTypeTabs = useMemo(() => {
		if (!data?.pages) return [];

		const typeCount: { [key: string]: number } = {};

		data.pages.forEach((property) => {
			const type = property.propertyType || property.type;
			if (type) {
				typeCount[type] = (typeCount[type] || 0) + 1;
			}
		});

		return Object.entries(typeCount).map(([type, count]) => ({
			properties: count,
			tabName: type,
			testId: `${type.toLowerCase()}-tab`,
			value: type,
		}));
	}, [data?.pages]);

	const unformatNumber = (str: string) => str.replace(/,/g, "");
	const formatNumber = (num: number) => new Intl.NumberFormat().format(num);

	const propertyTypeMap: Record<string, string> = {
		[t("villa")]: "Villa",
		[t("townhouse")]: "Townhouse",
		[t("apartment")]: "Apartment",
		[t("duplex")]: "Duplex",
	};

	const amenitiesArray = [
		{ label: t("homePagev4.brokerageFilterArgs.any"), value: "any" },
		{
			label: t("homePagev4.brokerageFilterArgs.mustHaveAC"),
			value: "mustHaveAC",
		},
		{
			label: t("homePagev4.brokerageFilterArgs.kitchenInstalled"),
			value: "kitchenInstalled",
		},
		{ label: t("homePagev4.brokerageFilterArgs.cabinets"), value: "cabinets" },
		{
			label: t("homePagev4.brokerageFilterArgs.schoolNearby"),
			value: "schoolNearby",
		},
		{ label: t("homePagev4.brokerageFilterArgs.pool"), value: "pool" },
	];

	const localizedPropertyTypes = (propertyType || []).map((val) => {
		const localizedLabel = Object.keys(propertyTypeMap).find(
			(key) => propertyTypeMap[key] === val,
		);

		return (localizedLabel || val).toLowerCase();
	});

	const getDeveloperOptions = (
		propertyData: any[] | undefined,
		t: (key: string) => string,
		locale: string = "en",
	) => {
		const options =
			propertyData?.map((dev) => ({
				label: dev.translations?.[locale] || dev.key,
				leadIcon: dev.icon ? (
					<Image src={dev.icon} alt={dev.key} width={24} height={24} />
				) : null,
				type: "multiSelect",
				value: dev.key,
			})) ?? [];

		return [
			{
				label: t("any"),
				type: "multiSelect",
				value: "any",
			},
			...options,
		];
	};

	const developersOptions = getDeveloperOptions(propertyData, t, locale);

	const handleTempCheckboxChange = (label: string, selectedItems: string[]) => {
		setTempSelectedCheckboxes((prev) => ({
			...prev,
			[label]: selectedItems,
		}));
	};

	const handleTempRadioChange = (name: string, value: string) => {
		setTempSelectedRadios((prev) => ({
			...prev,
			[name]: value,
		}));
	};

	const handleTempSelectChange = (label: string, selectedValue: any) => {
		setTempSelectedValues((prev) => ({
			...prev,
			[label]: selectedValue,
		}));
	};

	const handleTempTextInputChange = (key: string, value: string) => {
		setTempTextInputValues((prev) => ({
			...prev,
			[key]: value,
		}));
	};

	const handleTempSliderChange = (val: number) => {
		setTempSliderValue(val);
		handleTempTextInputChange("Plot Area", val.toString());
	};

	const handleTempDeveloperChange = (selectedOptions: any) => {
		const filteredOptions = (selectedOptions || []).filter(
			(option: any) => option.value !== "any",
		);

		const safeDevelopers = filteredOptions.map((option: any) => ({
			label: option.label,
			value: option.value,
		}));

		if (
			safeDevelopers.length === 0 &&
			selectedOptions.some((opt: any) => opt.value === "any")
		) {
			setTempSelectedDevelopers([]);
		} else {
			setTempSelectedDevelopers(safeDevelopers);
		}
	};

	const applyMoreFilters = () => {
		setAppliedCheckboxes(tempSelectedCheckboxes);
		setAppliedValues(tempSelectedValues);
		setAppliedRadios(tempSelectedRadios);
		setAppliedTextInputValues(tempTextInputValues);
		setAppliedSliderValue(tempSliderValue);
		setAppliedSelectedDevelopers(tempSelectedDevelopers);
		setAppliedShowSoldProperties(tempShowSoldProperties);

		setIsMoreFiltersOpen(false);
	};

	const resetAllMoreFilters = () => {
		setTempSelectedCheckboxes({});
		setTempSelectedValues({});
		setTempSelectedRadios({});
		setTempTextInputValues({});
		setTempSliderValue(0);
		setTempShowSoldProperties(false);
		setTempSelectedDevelopers([]);

		setAppliedCheckboxes({});
		setAppliedValues({});
		setAppliedRadios({});
		setAppliedTextInputValues({});
		setAppliedSliderValue(0);
		setAppliedSelectedDevelopers([]);
		setAppliedShowSoldProperties(false);

		setResetTrigger((prev) => !prev);
		setIsMoreFiltersOpen(false);
		setBedsAndBaths({ Bathrooms: [], Bedrooms: [] });
		setPrice({
			maxPrice: MAX_PRICE,
			minPrice: MIN_PRICE,
			priceRange: [MIN_PRICE, MAX_PRICE],
		});
		setDebouncedPrice({
			maxPrice: MAX_PRICE,
			minPrice: MIN_PRICE,
			priceRange: [MIN_PRICE, MAX_PRICE],
		});
		setPropertyType([]);
		setPropertyCategory("residential");
	};

	useEffect(() => {
		if (isMoreFiltersOpen) {
			setTempSelectedCheckboxes(appliedCheckboxes);
			setTempSelectedValues(appliedValues);
			setTempSelectedRadios(appliedRadios);
			setTempTextInputValues(appliedTextInputValues);
			setTempSliderValue(appliedSliderValue);
			setTempSelectedDevelopers(appliedSelectedDevelopers);
			setTempShowSoldProperties(appliedShowSoldProperties);
		}
	}, [
		isMoreFiltersOpen,
		appliedCheckboxes,
		appliedValues,
		appliedRadios,
		appliedTextInputValues,
		appliedSliderValue,
		appliedSelectedDevelopers,
		appliedShowSoldProperties,
	]);

	const isFurnishingTagActive = (optionValue: string): boolean => {
		const currentSelection = tempSelectedValues["furnishing"];

		if (optionValue === "any") {
			return !currentSelection;
		}

		if (typeof currentSelection === "string") {
			return currentSelection === optionValue;
		}

		if (typeof currentSelection === "object" && currentSelection?.value) {
			return currentSelection.value === optionValue;
		}

		return false;
	};

	const isPropertyAgeTagActive = (optionValue: string): boolean => {
		const currentSelection = tempSelectedValues["propertyAge"];

		if (optionValue === "any") {
			return !currentSelection;
		}

		if (typeof currentSelection === "string") {
			return currentSelection === optionValue;
		}

		if (typeof currentSelection === "object" && currentSelection?.value) {
			return currentSelection.value === optionValue;
		}

		return false;
	};

	const isAmenityTagActive = (optionValue: string): boolean => {
		const currentSelections = tempSelectedCheckboxes[t("otherAmenities")] || [];

		if (optionValue === "any") {
			return currentSelections.length === 0;
		}

		return currentSelections.includes(optionValue);
	};

	const handleFurnishingTagClick = (option: {
		label: string;
		value: string;
	}) => {
		const currentSelection = tempSelectedValues["furnishing"];
		const currentValue =
			typeof currentSelection === "object"
				? currentSelection?.value
				: currentSelection;

		if (option.value === "any" || currentValue === option.value) {
			handleTempSelectChange("furnishing", null);
		} else {
			handleTempSelectChange("furnishing", {
				label: option.label,
				value: option.value,
			});
		}
	};

	const handlePropertyAgeTagClick = (option: {
		label: string;
		value: string;
	}) => {
		const currentSelection = tempSelectedValues["propertyAge"];
		const currentValue =
			typeof currentSelection === "object"
				? currentSelection?.value
				: currentSelection;

		if (option.value === "any" || currentValue === option.value) {
			handleTempSelectChange("propertyAge", null);
		} else {
			handleTempSelectChange("propertyAge", {
				label: option.label,
				value: option.value,
			});
		}
	};

	const handleAmenityTagClick = (option: { label: string, value: string; }) => {
		const currentSelections = tempSelectedCheckboxes[t("otherAmenities")] || [];

		if (option.value === "any") {
			handleTempCheckboxChange(t("otherAmenities"), []);
		} else {
			if (currentSelections.includes(option.value)) {
				const newSelections = currentSelections.filter(
					(item) => item !== option.value,
				);
				handleTempCheckboxChange(t("otherAmenities"), newSelections);
			} else {
				const newSelections = [...currentSelections, option.value];
				handleTempCheckboxChange(t("otherAmenities"), newSelections);
			}
		}
	};

	const handleSelectAllAmenities = () => {
		const newSelections = amenitiesArray
			.filter((option) => option.value !== "any")
			.map((option) => option.value);
		handleTempCheckboxChange(t("otherAmenities"), newSelections);
	};

	const handleShowAllAmenities = () => {
		setShowAllAmenities(!showAllAmenities);
		setVisibleAmenityCount(showAllAmenities ? 4 : amenitiesArray.length);
	};

	useEffect(() => {
		function handleResize() {
			setIsMobile(window.innerWidth <= 768);
		}

		window.addEventListener("resize", handleResize);
		handleResize();

		return () => {
			window.removeEventListener("resize", handleResize);
		};
	}, []);

	const handleBedroomSelection = useCallback((value: string) => {
		setBedsAndBaths((prev) => {
			let newBedrooms: string[];
			if (value === "any") {
				newBedrooms = [];
			} else {
				const current = prev.Bedrooms || [];
				if (current.includes(value)) {
					newBedrooms = current.filter((item) => item !== value);
				} else {
					newBedrooms = [...current.filter((item) => item !== "any"), value];
				}
			}
			return { ...prev, Bedrooms: newBedrooms };
		});
	}, []);

	const handleBathroomSelection = useCallback((value: string) => {
		setBedsAndBaths((prev) => {
			let newBathrooms: string[];
			if (value === "any") {
				newBathrooms = [];
			} else {
				const current = prev.Bathrooms || [];
				if (current.includes(value)) {
					newBathrooms = current.filter((item) => item !== value);
				} else {
					newBathrooms = [...current.filter((item) => item !== "any"), value];
				}
			}
			return { ...prev, Bathrooms: newBathrooms };
		});
	}, []);

	const handlePriceChange = useCallback((newPrice: any) => {
		setPrice(newPrice);
	}, []);

	const handleBedsAndBathsReset = useCallback(() => {
		setBedsAndBaths({ Bathrooms: [], Bedrooms: [] });
	}, []);

	const handlePropertyTypeSelection = useCallback(
		(value: string) => {
			let newPropertyTypes: string[];
			if (value === "any") {
				newPropertyTypes = [];
			} else {
				const current = propertyType || [];
				if (current.includes(value)) {
					newPropertyTypes = current.filter((item) => item !== value);
				} else {
					newPropertyTypes = [
						...current.filter((item) => item !== "any"),
						value,
					];
				}
			}
			setPropertyType(newPropertyTypes);
		},
		[propertyType, propertyTypeTabs],
	);

	const handlePropertyTypeReset = useCallback(() => {
		setPropertyType([]);
		setPropertyCategory("residential");
	}, []);

	const handlePropertyCategoryChange = (category: string) => {
		setPropertyCategory(category);
		setPropertyType([]);
	};

	const residentialOptions = [
		{ label: t("homePagev4.brokerageFilterArgs.any"), value: "any" },
		{ label: t("homePagev4.brokerageFilterArgs.villa"), value: "Villa" },
		{
			label: t("homePagev4.brokerageFilterArgs.townhouse"),
			value: "Townhouse",
		},
		{
			label: t("homePagev4.brokerageFilterArgs.apartment"),
			value: "Apartment",
		},
		{ label: t("homePagev4.brokerageFilterArgs.duplex"), value: "Duplex" },
	];

	const commercialOptions = [
		{ label: t("homePagev4.brokerageFilterArgs.any"), value: "any" },
		{ label: t("homePagev4.brokerageFilterArgs.villa"), value: "Villa" },
		{
			label: t("homePagev4.brokerageFilterArgs.townhouse"),
			value: "Townhouse",
		},
		{
			label: t("homePagev4.brokerageFilterArgs.apartment"),
			value: "Apartment",
		},
		{ label: t("homePagev4.brokerageFilterArgs.duplex"), value: "Duplex" },
	];

	const getCurrentPropertyOptions = () => {
		return propertyCategory === "residential"
			? residentialOptions
			: commercialOptions;
	};

	const Filter3Args = {
		buttonLabel: tGlobal("homePagev4.brokerageFilterArgs.reset"),
		content: (
			<div>
				<div css={styles.priceDrop(theme)}>
					<RDSRangeSlider
						value={price.priceRange}
						step={PRICE_STEP}
						max={MAX_PRICE}
						min={MIN_PRICE}
						showLabels={true}
						showHeader={true}
						label={tGlobal("homePagev4.brokerageFilterArgs.priceRange")}
						showLabel={true}
						showCaption={false}
						isRequired={false}
						infoIcon={false}
						caption="Caption"
						captionIcon={false}
						dotsOnTrack={5}
						onValueChange={(range: [number, number]) => {
							const [minVal, maxVal] = range;
							const newState = {
								maxPrice: maxVal,
								minPrice: minVal,
								priceRange: [minVal, maxVal] as [number, number],
							};
							handlePriceChange(newState);
						}}
						currencySymbol=""
					/>
					<RDSTextInput
						label={tGlobal("homePagev4.brokerageFilterArgs.minimum")}
						placeholder={tGlobal("homePagev4.brokerageFilterArgs.minimum")}
						leadIcon=""
						value={
							isNaN(Number(price.minPrice))
								? ""
								: price.minPrice.toLocaleString()
						}
						onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
							const value = parseInt(e.target.value.replace(/,/g, ""));
							if (value > MAX_PRICE || value < MIN_PRICE || isNaN(value))
								return;

							const newState = {
								...price,
								minPrice: value,
								priceRange: [value, Math.max(value, price.maxPrice)] as [
									number,
									number,
								],
							};
							handlePriceChange(newState);
						}}
					/>
					<RDSTextInput
						label={tGlobal("homePagev4.brokerageFilterArgs.maximum")}
						placeholder={tGlobal("homePagev4.brokerageFilterArgs.maximum")}
						leadIcon=""
						value={
							isNaN(Number(price.maxPrice))
								? ""
								: price.maxPrice.toLocaleString()
						}
						onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
							const value = parseInt(e.target.value.replace(/,/g, ""));
							if (value > MAX_PRICE || value < MIN_PRICE || isNaN(value))
								return;

							const newState = {
								...price,
								maxPrice: value,
								priceRange: [Math.min(value, price.minPrice), value] as [
									number,
									number,
								],
							};
							handlePriceChange(newState);
						}}
					/>
				</div>
			</div>
		),
		dropdownOpenByDefault: false,
		iconSize: 20,
		inputStyles: {
			backgroundColor: "#f3f4f6",
			borderRadius: "8px",
			minWidth: "220px",
		},
		label: tGlobal("homePagev4.brokerageFilterArgs.price"),
		onChange: useCallback(
			(value: any) => {
				if (
					value.priceRange[0] !== price.priceRange[0] ||
					value.priceRange[1] !== price.priceRange[1]
				) {
					handlePriceChange(value);
				}
			},
			[price.priceRange, handlePriceChange],
		),

		onReset: () => {
			const resetState = {
				maxPrice: MAX_PRICE,
				minPrice: MIN_PRICE,
				priceRange: [MIN_PRICE, MAX_PRICE] as [number, number],
			};
			setPrice(resetState);
			setDebouncedPrice(resetState);
		},
		placeholder: tGlobal("homePagev4.brokerageFilterArgs.addRange"),

		selectedValues: {
			PriceRange:
				price.minPrice === MIN_PRICE && price.maxPrice === MAX_PRICE
					? []
					: [
							`${" " + price.minPrice.toLocaleString()} - ${" " + price.maxPrice.toLocaleString()}`,
						],
		},

		showButton: true,

		valueDisplayConfig: {
			PriceRange: {
				separator: "",
			},
		},
	};

	const propertyTabsArgs = {
		activeTabIndex: propertyCategory === "residential" ? 0 : 1,
		defaultValue: propertyCategory,
		isParent: false,
		level: "2",
		platform: isMobile ? "mobile" : "desktop",
		size: "sm",
		tabContent: [
			{
				assetProps: {
					heading: "residential",
				},
				badgeProps: {
					content: "text",
					label: "badge",
					type: "important",
				},
				isDisabled: false,
				isParent: false,
				label: t("homePagev4.brokerageFilterArgs.residential"),
				onClick: () => handlePropertyCategoryChange("residential"),
				rdsBadge: false,
				size: "sm",
				state: propertyCategory === "residential" ? "active" : "default",
				value: "residential",
			},
			{
				assetProps: {
					heading: "commercial",
				},
				badgeProps: {
					content: "text",
					label: "badge",
					type: "important",
				},
				isDisabled: false,
				isParent: false,
				label: t("homePagev4.brokerageFilterArgs.commercial"),
				onClick: () => handlePropertyCategoryChange("commercial"),
				rdsBadge: false,
				size: "sm",
				state: propertyCategory === "commercial" ? "active" : "inactive",
				value: "commercial",
			},
		],
	};

	const PropertyTypeFilterArgs = {
		buttonLabel: t("homePagev4.brokerageFilterArgs.reset"),
		content: (
			<div style={{ display: "flex", flexDirection: "column", gap: "1.5rem" }}>
				<div>
					<RDSTypography fontName={theme?.rds?.typographies.label.md}>
						{t("homePagev4.brokerageFilterArgs.propertyType")}
					</RDSTypography>
					<div css={styles.tabsContainer}>
						<RDSTabGroup {...propertyTabsArgs} />
					</div>
				</div>

				<div>
					<div
						style={{
							display: "flex",
							flexWrap: "wrap",
							gap: "0.5rem",
							maxWidth: "261px",
						}}
					>
						{getCurrentPropertyOptions().map((option) => (
							<RDSTagInteractive
								key={`property-${option.value}`}
								label={option.label}
								size="md"
								state={
									(option.value === "any" && propertyType?.length === 0) ||
									propertyType?.includes(option.value)
										? "active"
										: "default"
								}
								disabled={false}
								onClick={() => handlePropertyTypeSelection(option.value)}
							/>
						))}
					</div>
				</div>
			</div>
		),
		disabled: false,
		dropdownOpenByDefault: false,
		iconSize: 20,
		inputStyles: { backgroundColor: "#f3f4f6", borderRadius: "8px" },
		label: t("homePagev4.brokerageFilterArgs.propertyType"),
		onChange: () => {},

		onReset: handlePropertyTypeReset,
		placeholder: t("homePagev4.brokerageFilterArgs.select"),

		selectedValues: {
			PropertyTypes: propertyType || localizedPropertyTypes,
		},

		showButton: true,
		valueDisplayConfig: {
			PropertyTypes: {
				separator: ", ",
			},
		},
	};

	const emptyStatesArgs = {
		appearance: "danger",
		body: t(
			"features.propertyFinder.noPropertiesFoundMatchingYourCriteriaTryAdjustingYourFiltersAndTryAgain",
		),
		heading: t("features.propertyFinder.noResultsFound"),
		icon: <SquareWarningIcon width={64} height={64} />,
		showContent: true,
		showFooter: false,
		showMedia: true,
		size: "md",
	};

	const getPropertiesContent = () => {
		if (!propertyData && !error && developerKey === null) {
			return <PropertiesResultSkeleton />;
		}

		if (isLoading && developerKey) {
			return <PropertiesResultSkeleton />;
		}

		if (!data?.pages || data.pages.length === 0) {
			return <RDSEmptyState {...emptyStatesArgs} />;
		}

		return data.pages.map((property, idx) => (
			<PropertyCard
				key={property.id || idx}
				property={property}
				currentPage={currentPage}
			/>
		));
	};

	return (
		<section css={styles.mainSection(theme, false)}>
			<div css={styles.propertySection(theme)} ref={propertiesSectionRef}>
				<div css={styles.container(theme)}>
					<div css={styles.content(theme)}>
						<RDSTypography css={styles.heading}>
							{t("features.propertyFinder.exploreUnits")}
						</RDSTypography>
						<RDSTypography css={styles.subtitle}>
							{t("features.propertyFinder.samplePropertyInfo")}
						</RDSTypography>
					</div>

					<div css={styles.filtersContainer(theme)}>
						<BedsAndBathsFilter
							label={t("homePagev4.brokerageFilterArgs.bedsAndBaths")}
							placeholder={t("homePagev4.brokerageFilterArgs.addNumbers")}
							buttonLabel={t("homePagev4.brokerageFilterArgs.reset")}
							bedsAndBaths={bedsAndBaths}
							onBedroomSelection={handleBedroomSelection}
							onBathroomSelection={handleBathroomSelection}
							onReset={handleBedsAndBathsReset}
						/>

						<RDSSelectSlotDropdown {...PropertyTypeFilterArgs} />

						{!isMobile && <RDSSelectSlotDropdown {...Filter3Args} />}

						{!isMobile ? (
							<RDSButton
								variant="secondary"
								text={tGlobal("homePagev4.brokerageFilterArgs.moreFilters")}
								onClick={() => setIsMoreFiltersOpen(true)}
							/>
						) : (
							<>
								<div css={styles.buttonWrapper}>
									<RDSIconButton
										icon={<FilterIcon />}
										size="sm"
										variant="primary"
										css={styles.iconButton}
										onClick={() => setIsMoreFiltersOpen(true)}
									/>
								</div>
							</>
						)}
					</div>
				</div>

				<div
					css={[
						styles.cardRow(theme),
						isLoading && styles.propertiesLoadingOverlay,
					]}
				>
					{getPropertiesContent()}
				</div>

				<div css={{ marginTop: "2.5rem" }}>
					{totalPages > 1 &&
						!isLoading &&
						data?.pages &&
						data.pages.length > 0 && (
							<RDSPagination
								pageCount={totalPages}
								activePage={currentPage}
								onPageChange={(page) => {
									setSearchParams({
										page: page.toString(),
									});
								}}
							/>
						)}
				</div>
			</div>

			<div css={styles.filterMoreContainer}>
				<MoreFiltersDrawer
					isOpen={isMoreFiltersOpen}
					onClose={() => setIsMoreFiltersOpen(false)}
					title={t("homePagev4.brokerageFilterArgs.moreFilters")}
					onApply={applyMoreFilters}
					onResetAll={resetAllMoreFilters}
					applyLabel={t("homePagev4.brokerageFilterArgs.apply")}
					resetLabel={t("homePagev4.brokerageFilterArgs.resetAll")}
				>
					<div css={styles.sectionContainer}>
						<div css={styles.sectionTabsWrapper}>
							<RDSTypography fontName={theme?.rds?.typographies.heading.h5}>
								{t("homePagev4.brokerageFilterArgs.propertyFeatures")}
							</RDSTypography>

							{isMobile && (
								<div style={{ marginBottom: "1.5rem" }}>
									<div css={styles.advancedPriceDrop}>
										<RDSRangeSlider
											value={price.priceRange}
											step={PRICE_STEP}
											max={MAX_PRICE}
											min={MIN_PRICE}
											showLabels={true}
											showHeader={true}
											label={t("homePagev4.brokerageFilterArgs.priceRange")}
											showLabel={true}
											showCaption={false}
											isRequired={false}
											infoIcon={false}
											caption="Caption"
											captionIcon={false}
											dotsOnTrack={5}
											onValueChange={(range: [number, number]) => {
												const [minVal, maxVal] = range;
												const newState = {
													maxPrice: maxVal,
													minPrice: minVal,
													priceRange: [minVal, maxVal] as [number, number],
												};
												setPrice(newState);
											}}
											currencySymbol=""
										/>
									</div>
								</div>
							)}

							<div>
								<RDSTypography
									fontName={theme?.rds?.typographies.label.emphasis.lg}
								>
									{t("homePagev4.brokerageFilterArgs.furnishing")}
								</RDSTypography>
								<div css={[styles.tagContainer, styles.tagContainerOverflow]}>
									{[
										{
											label: t("homePagev4.brokerageFilterArgs.any"),
											value: "any",
										},
										{
											label: t("homePagev4.brokerageFilterArgs.furnished"),
											value: t("homePagev4.brokerageFilterArgs.furnished"),
										},
										{
											label: t("homePagev4.brokerageFilterArgs.semiFurnished"),
											value: t("homePagev4.brokerageFilterArgs.semiFurnished"),
										},
										{
											label: t("homePagev4.brokerageFilterArgs.unfurnished"),
											value: t("homePagev4.brokerageFilterArgs.unfurnished"),
										},
									].map((option) => (
										<RDSTagInteractive
											key={`furnishing-${option.value}`}
											label={option.label}
											size="md"
											state={
												isFurnishingTagActive(option.value)
													? "active"
													: "default"
											}
											disabled={false}
											onClick={() => handleFurnishingTagClick(option)}
										/>
									))}
								</div>
							</div>
						</div>

						<div css={styles.sectionTabsWrapper}>
							<RDSCheckboxGroup
								label={t("homePagev4.brokerageFilterArgs.communities")}
								checkboxCount={3}
								checkboxLabels={[
									t("homePagev4.brokerageFilterArgs.sedra"),
									t("homePagev4.brokerageFilterArgs.warefa"),
									t("homePagev4.brokerageFilterArgs.alarous"),
								]}
								onSelectionChange={(selected) =>
									handleTempCheckboxChange(
										t("homePagev4.brokerageFilterArgs.communities"),
										selected,
									)
								}
								value={
									tempSelectedCheckboxes[
										t("homePagev4.brokerageFilterArgs.communities")
									] || []
								}
								resetTrigger={resetTrigger}
								showLabel={true}
								showHeader={true}
								rtl={theme?.rds?.direction === "rtl"}
							/>
						</div>

						<div css={styles.subsectionShowAll}>
							<div css={styles.subsectionSelect}>
								<RDSTypography
									fontName={theme?.rds?.typographies.label.emphasis.lg}
								>
									{t("homePagev4.brokerageFilterArgs.otherAmenities")}
								</RDSTypography>
								<RDSLinkButton
									css={styles.selectAll}
									text={t("homePagev4.brokerageFilterArgs.selectAll")}
									onClick={handleSelectAllAmenities}
								/>
							</div>
							<div css={styles.tagContainer}>
								{amenitiesArray.slice(0, visibleAmenityCount).map((option) => (
									<RDSTagInteractive
										key={`amenity-${option.value}`}
										label={option.label}
										size="md"
										state={
											isAmenityTagActive(option.value) ? "active" : "default"
										}
										disabled={false}
										onClick={() => handleAmenityTagClick(option)}
									/>
								))}
							</div>
							<div>
								<RDSLinkButton
									css={styles.showAll}
									text={
										showAllAmenities
											? t("homePagev4.brokerageFilterArgs.showLess")
											: t("homePagev4.brokerageFilterArgs.showAll") +
												" (" +
												amenitiesArray.length +
												")"
									}
									onClick={handleShowAllAmenities}
								/>
							</div>
						</div>

						<div
							css={[styles.sectionTabsWrapper, styles.sectionTabsWrapperEnd]}
						>
							<RDSTypography
								fontName={theme?.rds?.typographies.label.emphasis.lg}
							>
								{t("homePagev4.brokerageFilterArgs.additionalDetails")}
							</RDSTypography>

							<div
								css={{
									alignItems: "center",
									display: "flex",
									justifyContent: "space-between",
									marginBottom: "1.5rem",
								}}
							>
								<RDSTypography
									css={[styles.subsectionTitle, styles.subsectionTitleEnd]}
								>
									{t("homePagev4.brokerageFilterArgs.showSoldProperties")}
								</RDSTypography>
								<RDSSwitch
									checked={tempShowSoldProperties}
									onCheckedChange={setTempShowSoldProperties}
								/>
							</div>

							<div css={styles.sectionTabsWrapper}>
								<RDSTypography
									css={[styles.subsectionTitle, styles.subsectionTitleEnd]}
								>
									{t("homePagev4.brokerageFilterArgs.plotArea")}
								</RDSTypography>

								<div>
									<RDSTextInput
										key="Plot Area"
										placeholder={t(
											"homePagev4.brokerageFilterArgs.selectRange",
										)}
										value={formatNumber(
											Number(tempTextInputValues["Plot Area"] || "0"),
										)}
										onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
											const val = unformatNumber(e.target.value);
											if (/^\d*$/.test(val)) {
												const num = parseInt(val || "0", 10);
												setTempSliderValue(num);
												handleTempTextInputChange("Plot Area", val);
											}
										}}
										rtl={theme?.rds?.direction === "rtl"}
									/>
								</div>

								<RDSSlider
									value={tempSliderValue}
									step={10}
									max={100000}
									unit={t("homePagev4.brokerageFilterArgs.sqm")}
									rtl={theme?.rds?.direction === "rtl"}
									onValueChange={handleTempSliderChange}
								/>
							</div>

							<div
								css={[styles.sectionTabsWrapper, styles.sectionTabsWrapperEnd]}
							>
								<RDSTypography
									css={[styles.subsectionTitle, styles.subsectionTitleEnd]}
								>
									{t("homePagev4.brokerageFilterArgs.propertyAge")}
								</RDSTypography>
								<div css={[styles.tagContainer, styles.tagContainerOverflow]}>
									{[
										{
											label: t("homePagev4.brokerageFilterArgs.any"),
											value: "any",
										},
										{
											label: `0-1 ${t("homePagev4.brokerageFilterArgs.years")}`,
											value: "0-1 Year",
										},
										{
											label: `1-5 ${t("homePagev4.brokerageFilterArgs.years")}`,
											value: "1-5 Years",
										},
										{
											label: `5-10 ${t("homePagev4.brokerageFilterArgs.years")}`,
											value: "5-10 Years",
										},
										{
											label: `10+ ${t("homePagev4.brokerageFilterArgs.years")}`,
											value: "10+ Years",
										},
									].map((option) => (
										<RDSTagInteractive
											key={`age-${option.value}`}
											label={option.label}
											size="md"
											state={
												isPropertyAgeTagActive(option.value)
													? "active"
													: "default"
											}
											disabled={false}
											onClick={() => handlePropertyAgeTagClick(option)}
										/>
									))}
								</div>
							</div>

							<div
								css={[styles.sectionTabsWrapper, styles.sectionTabsWrapperEnd]}
							>
								<RDSRadioGroup
									name={t("homePagev4.brokerageFilterArgs.availability")}
									label={t("homePagev4.brokerageFilterArgs.availability")}
									value={
										tempSelectedRadios[
											t("homePagev4.brokerageFilterArgs.availability")
										] || ""
									}
									onValueChange={(value) =>
										handleTempRadioChange(
											t("homePagev4.brokerageFilterArgs.availability"),
											value,
										)
									}
									dir={theme?.rds?.direction === "rtl"}
								>
									<RDSRadio
										value={t(
											"homePagev4.brokerageFilterArgs.showAllProperties",
										)}
										label={t(
											"homePagev4.brokerageFilterArgs.showAllProperties",
										)}
									/>
									<RDSRadio
										value={t(
											"homePagev4.brokerageFilterArgs.onlyShowAvailableProperties",
										)}
										label={t(
											"homePagev4.brokerageFilterArgs.onlyShowAvailableProperties",
										)}
									/>
								</RDSRadioGroup>
							</div>

							<div>
								<RDSSelect
									label={t("homePagev4.brokerageFilterArgs.developer")}
									placeholder={t(
										"homePagev4.brokerageFilterArgs.selectDeveloper",
									)}
									options={developersOptions}
									value={tempSelectedDevelopers || []}
									onChange={handleTempDeveloperChange}
									isClearable={true}
									isSearchable={true}
									isMulti={true}
									closeMenuOnSelect={false}
									hideSelectedOptions={false}
									menuPosition="absolute"
									menuShouldScrollIntoView={false}
								/>
							</div>
						</div>
					</div>
				</MoreFiltersDrawer>
			</div>
		</section>
	);
};
