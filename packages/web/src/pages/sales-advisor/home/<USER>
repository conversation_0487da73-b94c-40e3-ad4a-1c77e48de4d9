import { Container } from "@/components/container";
import { Col, Row } from "@/components/grid";
import { MasterplanSection } from "@/features/sales-advisor/dashboard/masterplan";
import { useScreenSize } from "@/hooks/media-query-hooks";
import { AppPaths, useAppPath, useAppPathGenerator } from "@/routes";
import {
	useCommunitiesMasterPlanQuery,
} from "@roshn/shared";
import { SAFeatureFlags, useSAFeatureFlagApi } from "@/features/feature-flags";
import {
	HoldUnitArgs,
	useCustomerList,
	useHoldUnit,
	useInsightSummary,
	useSalesAdvisorUser,
	// useNumberOfAssignedCustomers,
} from "@/services/sales-advisor";
import { HomeSkeleton } from "./home-skeleton";
import { InsightsSummarySection } from "@/features/sales-advisor/dashboard/insights-summary";
import { LatestCustomers } from "@/features/sales-advisor/dashboard/latest-customers";
import { Typography } from "@/components/typography";
import { AppTheme, tabletMQ } from "@/theme";
import { css } from "@emotion/react";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Navigate } from "@/utils/navigate";
import { convertDateToString } from "./home.mapper";
import { SearchByUnitSection } from "@/features/sales-advisor/dashboard/search-unit/search-unit";
import { RDSButton as Button } from "@roshn/ui-kit";
import { useNavigate } from "@remix-run/react";
import { HoldUnitForm } from "@/features/sales-advisor/hold-unit/hold-unit-form";
import { Dialog } from "@/features/sales-advisor/customer-profile/dialog";
import { ErrorBanner } from "@/features/sales-advisor/notification-hub/error-banner";
import { RoshnLoadingModal } from "@/features/loading/roshn-loading-modal";
import RoshnLogo from "@/assets/svgs/roshn-logo.svg?url";
import { QrDialog } from "@/components/qr-dialog";
import { useSARegistration } from "@/hooks/use-sa-registration";
import { useProjectsQuery, useSearchUnitByUnitNumber } from "@/features/sales-advisor/communities";

const styles = {
	button: (theme: AppTheme) =>
		css({
			...theme.typography.captionM,
			width: "100%",
		}),
	dialogWrapper: (theme: AppTheme) =>
		css({
			alignItems: "center",
			display: "flex",
			flexDirection: "column",
			gap: theme.spaces.lg,
			width: "480px",
		}),
	infoDialogTypo: (theme: AppTheme) =>
		css({
			color: theme.colors.grey["900"],
			textAlign: "center",
		}),
	quickActionsWrapper: (theme: AppTheme) =>
		css({
			paddingBlock: theme.spaces.lg,
		}),
	quickNavButton: (theme: AppTheme) =>
		css({
			marginBlockEnd: theme.spaces.md,
			width: "100%",
		}),
	root: (theme: AppTheme) =>
		css({
			display: "flex",
			flexDirection: "column",
			gap: theme.spaces.xl3,
			marginBlockStart: theme.spaces.xxl,
		}),
	subtitle: (theme: AppTheme) =>
		css({
			color: theme.colors.text.secondary,
			display: "none",
			marginBlock: theme.spaces.sm,
			[tabletMQ(theme)]: {
				display: "block",
			},
		}),

	title: (theme: AppTheme) =>
		css({
			...theme.typography.bodyS,
			marginBlock: theme.spaces.md,
			[tabletMQ(theme)]: {
				textTransform: "none",
				...theme.typography.bodyL,
			},
			textTransform: "uppercase",
		}),
};

export const Dashboard = () => {
	const { isTablet } = useScreenSize();
	const { t, i18n } = useTranslation(undefined, {
		keyPrefix: "pages.salesAdvisor.dashboard",
	});

	const { t: tc } = useTranslation();

	const [startDate, setStartDate] = useState<Date | null>(null);
	const [endDate, setEndDate] = useState<Date | null>(null);
	const [showHold, setHold] = useState(false);
	const [modalType, setModalType] = useState<
		"inprogress" | "success" | undefined
	>(undefined);
	const [reservationOpen, setReservationOpen] = useState(false);
	const [navigateInfo, setNavigateInfo] = useState({
		community: "",
		unitCode: "",
	});

	const generateAppPath = useAppPathGenerator();

	const {
		data: masterplanData,
		isLoading,
		error,
	} = useCommunitiesMasterPlanQuery();

	const { data: communityProjectMap } = useProjectsQuery();
	const masterPlanDataWithProjects =
		communityProjectMap &&
		masterplanData?.map((item: any) => {
			return {
				...item,
				projects:
					communityProjectMap[
						item?.community?.toLowerCase() as keyof typeof communityProjectMap
					],
			};
		});

	const { data: registrationQR } = useSAFeatureFlagApi(
		SAFeatureFlags.RegistrationQR,
	);

	const { data: holdUnitPrice } = useSAFeatureFlagApi(
		SAFeatureFlags.HoldUnitPrice,
	);

	const [value] = useSARegistration();
	const [showPriceError, setShowPriceError] = useState(false);

	const { data: customersData, isLoading: isCustomersLoading } =
		useCustomerList();
	const {
		mutate,
		reset,
		isError: holdUnitError,
		errorDescription: holdUnitErrorDes,
		isSuccess: holdUnitSuccess,
		isLoading: holdIsLoading,
	} = useHoldUnit();
	const {
		searchUnit,
		error: searchUnitError,
		reset: resetUnitSearch,
		isLoading: isUnitSearchLoading,
	} = useSearchUnitByUnitNumber({
		onSuccess: () => {
			setShowPriceError(true);
		},
	});

	const [price, setPrice] = useState(0);

	const salesAdvisorInfo = useSalesAdvisorUser();
	const customers = customersData?.pages?.at(0)?.data?.slice(0, 5);
	const assignedCustomers = customersData?.pages?.at(0)?.assignedCustomersCount;

	const { data: insightSummary, isLoading: isInsightSummaryLoading } =
		useInsightSummary(
			convertDateToString(startDate),
			convertDateToString(endDate),
		);

	// const { data: assignedCustomers, isLoading: isAssignedCustomersLoading } =
	// useNumberOfAssignedCustomers();
	const navigate = useNavigate();
	const customerListPath = useAppPath(AppPaths.salesAdvisorCustomerList);
	const SAQuickActions = [
		{
			label: t("followUpList"),
			to: {
				path: customerListPath,
				search: {
					activeTab: "follow-up-list",
				},
			},
		},
		{
			disabled: isCustomersLoading,
			label: tc("common.holdUnit"),
			onClick: () => setHold(true),
		},
		{
			label: t("unitsOnHold"),
			to: {
				path: customerListPath,
				search: {
					activeTab: "hold",
				},
			},
		},
		...(registrationQR
			? [
					{
						label: tc("pages.salesAdvisor.qr.referral"),
						onClick: () => setReservationOpen(true),
					},
				]
			: []),
	];

	const { data: projects, isLoading: isProjectsLoading } = useProjectsQuery();
	const { data: SAHoldTime } = useSAFeatureFlagApi(SAFeatureFlags.HoldTime);
	const ModalTimeOut =
		SAHoldTime && Number.isInteger(Number(SAHoldTime))
			? Number(SAHoldTime) * 1000
			: null;

	useEffect(() => {
		if (ModalTimeOut) {
			let timer: any;

			if (holdIsLoading) {
				timer = setTimeout(() => {
					reset();
					setModalType("inprogress");
				}, ModalTimeOut);
			}

			return () => {
				clearTimeout(timer);
			};
		}
	}, [holdIsLoading, SAHoldTime]);

	useEffect(() => {
		if (holdUnitSuccess) {
			setModalType("success");
		}
	}, [holdUnitSuccess]);

	if (isLoading) {
		return <HomeSkeleton />;
	}

	if (!isLoading && error) {
		return <Navigate to={generateAppPath(AppPaths.notfound)} />;
	}

	return (
		<Container css={styles.root}>
			<QrDialog
				onClose={() => setReservationOpen(false)}
				open={reservationOpen}
				value={value}
			/>

			<Dialog open={showHold} onClose={() => setHold(false)} closeIcon>
				<ErrorBanner
					trigger={!!searchUnitError}
					description={tc("pages.salesAdvisor.customerProfile.noUnitFound")}
					top="60"
					reset={resetUnitSearch}
				/>

				<ErrorBanner
					trigger={holdUnitError}
					description={holdUnitErrorDes as string}
					top="60"
					reset={reset}
				/>

				{holdUnitPrice && (
					<ErrorBanner
						trigger={showPriceError && !price}
						description={tc("pages.salesAdvisor.customerProfile.priceError")}
						top="60"
						reset={() => {
							setPrice(0);
							setShowPriceError(false);
						}}
					/>
				)}

				<HoldUnitForm
					onHold={(values) => {
						searchUnit({
							communityName: values.communityName,
							projectName: values.project,
							unitNumber: values.unitNumber,
						}).then((data: any) => {
							const unitPrice = data?.price ?? 0;

							setPrice(unitPrice);

							const mutation = () =>
								mutate({
									contactRegistryId: values.contactRegistryId,
									leadNumber: values.leadNumber,
									unitCode: data?.unitCode ?? data,
								} as HoldUnitArgs).then(() => {
									setHold(false);
									setNavigateInfo({
										community: values?.communityName as string,
										unitCode: (data?.unitCode ?? data) as string,
									});
								});

							if (holdUnitPrice) {
								if (unitPrice) {
									mutation();
								}
							} else {
								mutation();
							}
						});
					}}
					disabled={holdUnitPrice ? !!price : false}
					isUnitSearchLoading={isUnitSearchLoading}
					onDiscard={() => {
						setShowPriceError(false);
						setHold(false);
						setPrice(0);
					}}
				/>
			</Dialog>
			<RoshnLoadingModal isOpen={holdIsLoading} />
			<Dialog
				open={!!modalType}
				onClose={() => {
					setModalType(undefined);
					reset();
				}}
			>
				<div css={styles.dialogWrapper}>
					<img src={RoshnLogo} alt="Roshn Logo" height={64} width={90} />
					<Typography css={styles.infoDialogTypo} variant="subtitleL" isBold>
						{modalType === "inprogress"
							? tc("pages.salesAdvisor.customerProfile.holdInProgress")
							: tc("pages.salesAdvisor.customerProfile.holdSuccess")}
					</Typography>
					<Button
						css={styles.button}
						onClick={() => {
							navigate(
								generateAppPath(AppPaths.propertyDetails, {
									communityName: navigateInfo.community.toLowerCase(),
									unitId: navigateInfo.unitCode,
								}),
							);
							setModalType(undefined);
						}}
						variant="primary"
						text={tc("common.continue")}
					/>
				</div>
			</Dialog>
			<Typography variant="h3">
				{t("title", {
					salesAdvisorName: salesAdvisorInfo?.name || "",
				})}
			</Typography>
			<InsightsSummarySection
				isAssignedCustomersLoading={isCustomersLoading}
				assignedCustomers={assignedCustomers}
				isLoading={isInsightSummaryLoading}
				data={insightSummary}
				startDate={startDate}
				endDate={endDate}
				onStartDateChanged={setStartDate}
				onEndDateChanged={setEndDate}
			/>
			<SearchByUnitSection data={projects} isLoading={isProjectsLoading} />
			<Col
				tablet={{
					span: 24,
				}}
				css={{ paddingInlineStart: 0 }}
			>
				<MasterplanSection
					masterplans={masterPlanDataWithProjects}
					dir={"horizontal"}
				/>
			</Col>
			<Row gutter={isTablet ? 80 : 0}>
				<Col
					mobile={{
						span: 24,
					}}
					tablet={{
						span: 16,
					}}
					css={{ paddingInlineStart: 0 }}
				>
					<LatestCustomers
						customers={customers}
						isLoading={isCustomersLoading}
					/>
				</Col>
				<Col mobile={{ span: 24 }} tablet={{ span: 8 }}>
					<Typography css={styles.title} variant="bodyS">
						{t("quickNavigations")}
					</Typography>
					{/* can be updated from strapi from pages.salesAdvisor.dashboard.subtitle*/}
					{i18n.exists(`pages.salesAdvisor.dashboard.subtitle`) && (
						<Typography variant="subtitleL" css={styles.subtitle}>
							{t("subtitle")}
						</Typography>
					)}

					<div css={styles.quickActionsWrapper}>
						{SAQuickActions.map((action, index) => (
							<Button
								key={index}
								variant="secondary"
								disabled={action.disabled ?? false}
								onClick={() => {
									action?.onClick?.();

									if (action?.to) {
										if (action.to.search) {
											const params = new URLSearchParams(action.to.search);
											navigate(`${action.to.path}?${params.toString()}`);
										} else {
											navigate(action.to.path);
										}
									}
								}}
								css={styles.quickNavButton}
								text={action.label}
							/>
						))}
					</div>
				</Col>
			</Row>
		</Container>
	);
};
