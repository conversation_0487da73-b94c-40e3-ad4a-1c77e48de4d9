import * as React from "react";
import { useLocation, useNavigate, useParams } from "@remix-run/react";
import { Container } from "@/components/container";
import { Typography } from "@/components/typography";
import { ChevronRightIcon } from "@/components/icons";
import { useTranslation } from "react-i18next";
import { AppTheme } from "@/theme";
import { css, useTheme } from "@emotion/react";
import {
	useSalesRegisters,
	useCustomerInfo,
	useLeads,
	useLeadsNotes,
	useFetchLookUps,
	useHoldUnit,
	HoldUnitArgs,
	useFetchTeamMembers,
	LeadCasedInfo,
	useEligibleLeadsForHold,
	LeadStatus,
} from "@/services/sales-advisor";
import {
	useCommunitiesMasterPlanQuery,
} from "@roshn/shared";

import { Col, Row } from "@/components/grid";
import { Breadcrumb } from "@/components/breadcrumb";
import { AppPaths, generateAppPath, useAppPath } from "@/routes";
import {
	FavouriteList,
	CustomerDetails,
	LeadCard,
	PropertyList,
	HistoryCard,
} from "@/features/sales-advisor/customer-profile";
import { MasterplanSection } from "@/features/sales-advisor/dashboard/masterplan";
import * as InfoCard from "@/features/sales-advisor/customer-profile/card";
import { useScreenSize } from "@/hooks/media-query-hooks";
import { MasterplansSwiper } from "@/features/sales-advisor/customer-profile/masterplans-swiper";
import {
	CustomerProfileSkeleton,
	ProfileItemSkeleton,
} from "./customer-profile-skeleton";
import { useCreateNote } from "@/services/sales-advisor/hooks/use-create-note";
import { Tabs } from "@/components/tab";
import { AuxiliaryButton } from "@/components/button";
import { SearchByUnitSection } from "@/features/sales-advisor/dashboard/search-unit/search-unit";
import { RoshnLoadingModal } from "@/features/loading";
import {
	HoldUnitFields,
	HoldUnitForm,
} from "@/features/sales-advisor/hold-unit/hold-unit-form";
import RoshnLogo from "@/assets/svgs/roshn-logo.svg?url";
import { Dialog } from "@/features/sales-advisor/customer-profile/dialog";
import { toTitleCase } from "@/services/sales-advisor/helpers";
import { ErrorBanner } from "@/features/sales-advisor/notification-hub/error-banner";
import { RawAppPaths } from "@/constants/raw-paths";
import { CustomerInsights } from "./customer-insights";
import { BreakerBay500 } from "@/theme/palette/all-colors";
import { RDSButton } from "@roshn/ui-kit";
import { UNIT_STATUS } from "@/constants";
import { useProjectsQuery, useSearchUnitByUnitNumber } from "@/features/sales-advisor/communities";
import { useSAFeatureFlagApi, SAFeatureFlags } from "@/features/feature-flags";


const styles = {
	backButton: (theme: AppTheme) =>
		css({
			"&:active": {
				marginBottom: theme.spaces.xl,
			},
			"&:hover": {
				marginBottom: theme.spaces.xl,
			},
			marginBottom: theme.spaces.xl,
		}),
	breadcrumb: (theme: AppTheme) =>
		css({
			color: theme.colors.text.secondary,
			position: "relative",
			zIndex: "auto",
		}),
	button: (theme: AppTheme) =>
		css({
			...theme.typography.captionM,
			width: "100%",
		}),
	dialogWrapper: (theme: AppTheme) =>
		css({
			alignItems: "center",
			display: "flex",
			flexDirection: "column",
			gap: theme.spaces.lg,
			width: "480px",
		}),
	goBack: (theme: AppTheme) =>
		css({
			display: "flex",
			marginBlockEnd: theme.spaces.lg,
			marginBlockStart: theme.spaces.md,
			svg: {
				marginInlineEnd: theme.spaces.sm,
				rotate: theme.direction === "rtl" ? "180deg" : "0deg",
			},
		}),

	infoDialogTypo: (theme: AppTheme) =>
		css({
			color: theme.colors.grey["900"],
			textAlign: "center",
		}),

	root: (theme: AppTheme) =>
		css({
			margin: "0 auto",
			marginBlockStart: theme.spaces.xl9,
			paddingBlock: theme.spaces.xl4,
		}),

	searchUnitWrapper: (theme: AppTheme) =>
		css({
			marginBlock: theme.spaces.lg,
		}),

	tabContent: (theme: AppTheme) =>
		css({
			paddingTop: theme.spaces.md,
		}),

	tabList: css({
		borderBottom: "none",
	}),

	tabRoot: (theme: AppTheme) =>
		css({
			marginTop: theme.spaces.xl,
		}),

	tabTrigger: (theme: AppTheme) =>
		css({
			"&[data-state=active]": {
				borderBottomWidth: "2px",
				boxShadow: "inset 0 -1px 0 0 currentColor, 0 .05px 0 0 currentColor",
				color: BreakerBay500,
			},

			alignItems: "center",
			color: theme.colors.grey[500],
			cursor: "pointer",
			display: "flex",
			fontSize: "16px",
			fontWeight: theme.typography.fontWeightBold,
			height: "45px",
			justifyContent: "center",
			lineHeight: "18px",
			marginRight: theme.spaces.sm,
			paddingInline: "8px",
			textTransform: "uppercase",
			userSelect: "none",
		}),
};

export const CustomerProfile = () => {
	const { t, i18n } = useTranslation(undefined, {
		keyPrefix: "pages.salesAdvisor.customerProfile",
	});
	const { t: tc } = useTranslation();
	const { isTablet } = useScreenSize();
	const CustomerListPath = useAppPath(AppPaths.salesAdvisorCustomerList);
	const homeDashboardPath = RawAppPaths.salesAdvisor;
	const homeDashboardPathLocale = useAppPath(AppPaths.salesAdvisor);
	const navigate = useNavigate();

	const { data: history } = useSAFeatureFlagApi(SAFeatureFlags.HistoryChanges);

	const { data: customerHold } = useSAFeatureFlagApi(
		SAFeatureFlags.CustomerProfileHold,
	);

	const { data: unitRecommendation } = useSAFeatureFlagApi(
		SAFeatureFlags.UnitRecommendation,
	);

	const { data: favoriteList } = useSAFeatureFlagApi(
		SAFeatureFlags.FavoriteList,
	);

	const { data: profileLayoutChange } = useSAFeatureFlagApi(
		SAFeatureFlags.ProfileLayoutChange,
	);

	const { data: holdAvailabilityCheck } = useSAFeatureFlagApi(
		SAFeatureFlags.HoldAvailabilityCheck,
	);

	const { data: holdUnitPrice } = useSAFeatureFlagApi(
		SAFeatureFlags.HoldUnitPrice,
	);

	useFetchLookUps();

	const { data: projects, isLoading: isProjectsLoading } = useProjectsQuery();
	const currentLocation = useLocation();
	const isPrevPathHomeRoute = currentLocation.state?.prevPath
		? (currentLocation.state.prevPath as string).endsWith(homeDashboardPath)
		: document?.referrer?.includes?.(CustomerListPath)
			? false
			: true;

	const theme = useTheme();

	const { registryId = "" } = useParams();
	const { data: salesRegisters, isLoading: loadingSalesRegisters } =
		useSalesRegisters(registryId);
	const { userInfo } = useCustomerInfo(registryId);
	const { data: masterplansData } = useCommunitiesMasterPlanQuery();
	const { data: communityProjectMap } = useProjectsQuery();
	const masterPlanDataWithProjects =
		communityProjectMap &&
		masterplansData?.map((item: any) => {
			return {
				...item,
				projects:
					communityProjectMap[
						item?.community?.toLowerCase() as keyof typeof communityProjectMap
					],
			};
		});

	const { data: leads, isLoading: loadingLeads } = useLeads(registryId);
	const { data: notesByLeadId } = useLeadsNotes(leads);

	const [createNoteError, setCreateNoteError] = React.useState<{
		[key: string]: any;
	}>({});
	const { mutateAsync: createNote, isLoading: isCreatingNote } = useCreateNote({
		onError(error, variables) {
			setCreateNoteError({ ...createNoteError, [variables.leadId]: error });
		},
		onSuccess(_data, variables) {
			setCreateNoteError({ ...createNoteError, [variables.leadId]: undefined });
		},
	});
	useFetchTeamMembers();
	const holdEligibleLeads = useEligibleLeadsForHold(leads as LeadCasedInfo[]);
	const [expanded, setExpanded] = React.useState<undefined | string>();
	const [unitCode, setUnitCode] = React.useState<string>("");
	const [holdAtLeadLevel, setHoldAtLeadLevel] = React.useState<boolean>(false);
	const [holdUnitValues, setHoldUnitValues] =
		React.useState<HoldUnitFields | null>(null);

	const [unitStatusError, setUnitStatusError] = React.useState<boolean>(false);

	const [navigateInfo, setNavigateInfo] = React.useState({
		community: "",
		unitCode: "",
	});

	const isAr = i18n.resolvedLanguage === "ar";

	const [holdUnitDefaults, setHoldUnitDefaults] =
		React.useState<HoldUnitFields>({
			communityName: "",
			customerName: "",
			phoneNumber: "",
			price: "",
			project: "",
			unitNumber: "",
		});

	const {
		mutate,
		isLoading,
		reset,
		isSuccess,
		isError: holdUnitError,
		errorDescription: holdUnitErrorDes,
	} = useHoldUnit();

	const {
		searchUnit,
		error: searchUnitError,
		reset: resetUnitSearch,
		isLoading: isUnitSearchLoading,
	} = useSearchUnitByUnitNumber({
		onSuccess: (data) => {
			setUnitCode(data?.unitCode ?? data);
		},
	});

	const [saDialog, setSaDialog] = React.useState<boolean>(false);
	const [modalType, setModalType] = React.useState<
		"inprogress" | "success" | undefined
	>(undefined);

	const handleSaDialog = () => {
		setSaDialog(false);
		resetUnitSearch(); // to reset error banner state
		setUnitCode(""); // to reset again so that next time fresh call happens
	};
	const { data: SAHoldTime } = useSAFeatureFlagApi(SAFeatureFlags.HoldTime);
	const ModalTimeOut =
		SAHoldTime && Number.isInteger(Number(SAHoldTime))
			? Number(SAHoldTime) * 1000
			: null;
	React.useEffect(() => {
		if (ModalTimeOut) {
			let timer: any;

			if (isLoading) {
				timer = setTimeout(() => {
					setNavigateInfo({
						community: holdUnitValues?.communityName as string,
						unitCode: unitCode,
					});
					reset();
					setUnitCode("");
					setHoldUnitValues(null);
					setModalType("inprogress");
				}, ModalTimeOut);
			}

			return () => {
				clearTimeout(timer);
			};
		}
	}, [isLoading]);

	React.useEffect(() => {
		if (holdUnitError) {
			setUnitCode("");
			setHoldUnitValues(null);
		}
	}, [holdUnitError]);

	React.useEffect(() => {
		if (unitCode && holdUnitValues?.leadNumber) {
			setSaDialog(false);
			mutate({
				contactRegistryId: registryId,
				leadNumber: holdUnitValues.leadNumber,
				unitCode: unitCode,
			} as HoldUnitArgs);
		}
	}, [unitCode, holdUnitValues?.leadNumber]);

	React.useEffect(() => {
		if (isSuccess) {
			setNavigateInfo({
				community: holdUnitValues?.communityName as string,
				unitCode: unitCode,
			});
			setHoldUnitValues(null);
			setUnitCode("");
			setSaDialog(false);
			setModalType("success");
		}
	}, [isSuccess]);

	const errorMessage = (): string => {
		if (holdUnitPrice && !holdUnitDefaults?.price && unitStatusError) {
			return t("statusPriceError");
		} else if (holdUnitPrice && !holdUnitDefaults?.price) {
			return t("priceError");
		} else if (unitStatusError) {
			return t("notAvailable");
		} else if (searchUnitError) {
			return t("noUnitFound");
		}
		return "";
	};

	return (
		<Container css={styles.root}>
			<ErrorBanner
				trigger={holdUnitError}
				description={holdUnitErrorDes as string}
				top="120"
				reset={reset}
			/>

			<Dialog open={saDialog} onClose={handleSaDialog} closeIcon>
				<>
					<ErrorBanner
						trigger={
							!!searchUnitError || !holdUnitDefaults?.price || unitStatusError
						}
						description={errorMessage()}
						top="60"
					/>

					<HoldUnitForm
						defaultValue={holdUnitDefaults}
						onDiscard={handleSaDialog}
						onHold={(values) => {
							setHoldUnitValues(values);

							if (holdAtLeadLevel) {
								searchUnit({
									...values,
									projectName: values.project,
								});
							}
						}}
						contactRegistryId={registryId}
						isUnitSearchLoading={isUnitSearchLoading}
						disabled={
							!!searchUnitError ||
							(holdAvailabilityCheck &&
								holdUnitDefaults?.unitStatus !== UNIT_STATUS.AVAILABLE)
						}
						withPrice={!!holdUnitPrice}
					/>
				</>
			</Dialog>
			<RoshnLoadingModal isOpen={isLoading} />
			<Dialog
				open={!!modalType}
				onClose={() => {
					setModalType(undefined);
					reset();
				}}
			>
				<div css={styles.dialogWrapper}>
					<img src={RoshnLogo} alt="Roshn Logo" height={64} width={90} />
					<Typography css={styles.infoDialogTypo} variant="subtitleL" isBold>
						{modalType === "inprogress"
							? t("holdInProgress")
							: t("holdSuccess")}
					</Typography>
					<RDSButton
						css={styles.button}
						onClick={() => {
							navigate(
								generateAppPath(AppPaths.propertyDetails, {
									communityName: navigateInfo.community.toLowerCase(),
									unitId: navigateInfo.unitCode,
								}),
							);
							setModalType(undefined);
						}}
						variant="primary"
						text={tc("common.continue")}
					/>
				</div>
			</Dialog>

			<AuxiliaryButton
				prefixIcon={
					<ChevronRightIcon
						color={theme.colors.grey["900"]}
						width={18}
						height={16}
					/>
				}
				css={styles.backButton}
				onClick={() => {
					const navigatePath = isPrevPathHomeRoute
						? homeDashboardPathLocale
						: -1;
					navigate(navigatePath as string);
				}}
			>
				<Typography variant="captionS">
					{isPrevPathHomeRoute ? t("backToHome") : t("backToCustomerList")}
				</Typography>
			</AuxiliaryButton>

			<Breadcrumb css={styles.breadcrumb}>
				<Typography
					variant="captionS"
					css={css({
						cursor: "pointer",
					})}
					onClick={() => navigate(generateAppPath(AppPaths.salesAdvisor))}
				>
					{t("home")}
				</Typography>
				<Typography
					variant="captionS"
					css={css({
						cursor: "pointer",
					})}
					onClick={() =>
						navigate(generateAppPath(AppPaths.salesAdvisorCustomerList))
					}
				>
					{t("customerList")}
				</Typography>
				<Typography variant="captionS">{t("customerDetails")}</Typography>
			</Breadcrumb>
			<React.Suspense fallback={<CustomerProfileSkeleton />}>
				<Typography variant="h3">{userInfo.name}</Typography>
				<Tabs.Root
					css={styles.tabRoot}
					dir={theme.direction}
					defaultValue="all"
				>
					<Tabs.List css={styles.tabList}>
						<Tabs.Trigger css={styles.tabTrigger} value="all">
							{t("leadDetails")}
						</Tabs.Trigger>
						{customerHold && (
							<Tabs.Trigger css={styles.tabTrigger} value="leadActions">
								{t("leadActions")}
							</Tabs.Trigger>
						)}
						{history && (
							<Tabs.Trigger css={styles.tabTrigger} value="history">
								{t("history")}
							</Tabs.Trigger>
						)}
					</Tabs.List>
					<Tabs.Content value="all">
						<Row gutter={isTablet ? 48 : profileLayoutChange ? 48 : 0}>
							<Col span={isTablet ? (profileLayoutChange ? 24 : 16) : 24}>
								<CustomerDetails
									isNafathVerified={userInfo.nafathVerified}
									phoneNumber={userInfo.telephoneNumber}
									email={userInfo.mail}
									registryId={registryId}
								/>
								<InfoCard.Title>{t("leadDetails")}</InfoCard.Title>
								<InfoCard.Card>
									{!loadingLeads ? (
										leads?.map((lead, idx) => (
											<LeadCard
												key={lead.leadNumber}
												title={tc("pages.salesAdvisor.customerList.lead", {
													number: lead.leadNumber,
												})}
												notes={notesByLeadId[lead.id]}
												ownerRegistryId={lead.ownerRegistryId}
												financeMethod={lead.financeType as string}
												budget={lead.budgetEN}
												rank={lead.rank}
												status={lead.status as LeadStatus}
												salaryRange={lead.salaryRangeEN}
												interestedCommunities={[
													isAr
														? lead.communityOfInterestAR
														: lead.communityOfInterestEN,
												]}
												isSakaniBeneficiary={lead.isSakaniBeneficiary}
												preferredNumberOfBedrooms={
													lead.preferredNumberOfBedrooms
												}
												preferredUnitType={lead.preferredUnitType}
												onAddNote={(noteText) =>
													createNote({ leadId: lead.id, noteText })
												}
												isCreatingNote={isCreatingNote}
												createNoteError={createNoteError[lead.id]}
												id={lead.leadNumber}
												expended={expanded}
												setExpanded={setExpanded}
												index={idx}
												bankableStatus={lead.bankableStatus}
												financeArranged={lead.financeArranged}
												financeRep={lead.financeRep}
												financeRepEmail={lead.financeRepEmail}
												financeRepMobile={lead.financeRepMobile}
												followUpList={lead.followUpList}
												homeFinanceClearance={lead.homeFinanceClearance}
												homeFinanceProvider={lead.homeFinanceProvider}
												maxFinInstallment={lead.maxFinInstallment}
												maxFinanceAmount={lead.maxFinanceAmount}
												maxFinancePeriod={lead.maxFinancePeriod}
												salesMethod={lead.salesMethod as string}
												suitableFinanceInstallment={
													lead.suitableFinanceInstallment
												}
												suitableFinancePeriod={lead.suitableFinancePeriod}
												registryId={registryId}
												retireReason={lead.retireReason as string}
												unitPreferences={lead.unitPreferences}
												owner={lead.owner}
												onHoldUnit={({ leadNumber }) => {
													setHoldAtLeadLevel(true);
													setHoldUnitDefaults({
														communityName:
															toTitleCase(lead?.communityOfInterestEN) ?? "",
														customerName: userInfo?.name ?? "",
														leadNumber: leadNumber,
														phoneNumber: userInfo?.telephoneNumber ?? "",
														project: lead?.project ?? "",
														unitNumber: "",
													});
													setSaDialog(true);
												}}
												communityEn={lead?.communityOfInterestEN}
											/>
										))
									) : (
										<>
											<ProfileItemSkeleton />
											<ProfileItemSkeleton />
										</>
									)}
								</InfoCard.Card>
								{(unitRecommendation || favoriteList) && <CustomerInsights />}
								{!customerHold && (
									<div css={styles.searchUnitWrapper}>
										<SearchByUnitSection
											data={projects}
											isLoading={isProjectsLoading || loadingLeads}
											label={tc("common.holdUnit")}
											handleClick={(data) => {
												setHoldUnitDefaults({
													...data,
													communityName: toTitleCase(data?.communityName) ?? "",
													customerName: userInfo?.name ?? "",
													phoneNumber: userInfo?.telephoneNumber ?? "",
												});
												searchUnit({
													...data,
													projectName: data.project,
												});
												setSaDialog(true);
											}}
										/>
									</div>
								)}
								{profileLayoutChange ? (
									<>
										{salesRegisters?.length ? (
											<>
												<InfoCard.Title>
													{t("customerProperties")}
												</InfoCard.Title>
												<PropertyList
													salesRegisters={salesRegisters}
													registryId={registryId}
													collapsible
												/>
											</>
										) : null}
									</>
								) : !isTablet && !loadingSalesRegisters ? (
									salesRegisters?.length ? (
										<>
											<InfoCard.Title>{t("customerProperties")}</InfoCard.Title>
											<PropertyList
												salesRegisters={salesRegisters}
												registryId={registryId}
												collapsible
											/>
										</>
									) : (
										<>
											<InfoCard.Title>{t("recommendProperty")}</InfoCard.Title>
											<MasterplansSwiper
												masterplans={masterPlanDataWithProjects}
											/>
										</>
									)
								) : null}
								{!favoriteList && (
									<FavouriteList favouriteUnits={userInfo?.favoriteUnitCodes} />
								)}
							</Col>
							{isTablet && !profileLayoutChange && !loadingSalesRegisters ? (
								<Col span={8}>
									{salesRegisters?.length ? (
										<PropertyList
											salesRegisters={salesRegisters}
											registryId={registryId}
										/>
									) : (
										<MasterplanSection
											masterplans={masterPlanDataWithProjects}
										/>
									)}
								</Col>
							) : null}
						</Row>
					</Tabs.Content>
					<Tabs.Content value="history">
						<HistoryCard registryId={registryId} />
					</Tabs.Content>
					<Tabs.Content value="leadActions">
						{customerHold && (
							<div css={styles.searchUnitWrapper}>
								<SearchByUnitSection
									data={projects}
									isLoading={isProjectsLoading || loadingLeads}
									label={tc("common.holdUnit")}
									leads={holdEligibleLeads}
									showLeadSelector={true}
									getUnitCodeInHandleClick={true}
									handleClick={(data) => {
										if (
											holdAvailabilityCheck &&
											data?.unitStatus !== UNIT_STATUS.AVAILABLE
										) {
											setUnitStatusError(true);
										}

										setHoldUnitDefaults({
											...data,
											communityName: toTitleCase(data.communityName) ?? "",
											customerName: userInfo?.name ?? "",
											leadNumber: data.leadNumber,
											phoneNumber: userInfo?.telephoneNumber ?? "",
											project: data.project,
											unitNumber: data.unitNumber,
										});
										setSaDialog(true);
										if (data.unitId) {
											setUnitCode(data.unitId as string);
										} else {
											searchUnit({
												...data,
												projectName: data.project,
											});
										}
									}}
									withPrice={!!holdUnitPrice}
								/>
							</div>
						)}
					</Tabs.Content>
				</Tabs.Root>
			</React.Suspense>
		</Container>
	);
};
