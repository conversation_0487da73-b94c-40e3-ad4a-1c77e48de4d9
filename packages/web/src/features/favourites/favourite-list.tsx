import * as React from "react";
import { FavouriteCard, FavouriteCardProps } from "./favourite-card";
import { css } from "@emotion/react";
import { AppTheme, tabletMQ } from "@/theme";
import { useScreenSize } from "@/hooks/media-query-hooks";
import { Divider } from "@/components/divider";
import {
	useFavouritePropertiesQuery,
	useMutateFavourite,
} from "@roshn/shared/es/features/favourites";
import { getPropertyNote } from "@/pages/property-group/property-group.mapper";
import { FavouriteListSkeleton } from "./favourite-list-skeleton";

const styles = {
	title: (theme: AppTheme) =>
		css({
			marginBlock: theme.spaces.xl3,
		}),
	wrapper: (theme: AppTheme) =>
		css({
			display: "flex",
			flexDirection: "column",
			[tabletMQ(theme)]: {
				gap: theme.spaces.md,
			},
		}),
};

const FavouriteCardContainer = ({
	unitCode,
	allowEdit,
	latestHandoverDateFlag = false,
}: {
	allowEdit?: boolean;
	unitCode: string;
	latestHandoverDateFlag?: boolean;
}) => {
	const { data: property } = useFavouritePropertiesQuery(unitCode);
	const { isTablet } = useScreenSize();

	const { removeFavourite } = useMutateFavourite();

	const handleRemoveFavourite = React.useCallback(() => {
		removeFavourite(unitCode);
	}, [unitCode]);

	const props = React.useMemo<FavouriteCardProps | undefined>(() => {
		if (!property) {
			return;
		}
		return {
			bathroom: property.numberOfBathrooms,
			orientation: property.metadata?.ORIENTATION ?? "",
			bedroom: property.numberOfBedrooms,
			city: property.metadata?.city ?? "",
			communityName: property.communityName,
			estDeliveryDate: property.deliveryTime,
			facade: property.metadata?.ELEVATION_TYPE ?? "",
			note: getPropertyNote({
				driverRoom:
					property.metadata?.DRIVER_ROOM?.toLowerCase() === "yes" ? 1 : 0,
				maidRoom: property.metadata?.MAID_ROOM?.toLowerCase() === "yes" ? 1 : 0,
			}),
			previewImage: property.metadata?.exteriorPreviewImage,
			price: property.price,
			squareSpace: property.plotArea,
			typology: property.typologyGroup,
			unitNumber: property.unitNumber,
			unitType: property.unitType,
			unitTypeClassification: property.metadata.UNIT_CLASSIFICATION,
		};
	}, [unitCode, property]);

	if (!props) {
		return null;
	}

	return (
		<>
			<FavouriteCard
				{...props}
				allowEdit={allowEdit}
				onToggleFavourite={handleRemoveFavourite}
				latestHandoverDateFlag={latestHandoverDateFlag}
			/>
			{!isTablet && <Divider />}
		</>
	);
};

type FavouriteListProps = {
	allowEdit?: boolean;
	unitCodes?: string[];
	latestHandoverDateFlag?: boolean;
};

export const FavouriteList = ({
	unitCodes,
	allowEdit = true,
	latestHandoverDateFlag = false,
}: FavouriteListProps) => {
	return (
		<React.Suspense fallback={<FavouriteListSkeleton />}>
			<div css={styles.wrapper}>
				{unitCodes?.map((code: string) => (
					<FavouriteCardContainer
						unitCode={code}
						key={code}
						allowEdit={allowEdit}
						latestHandoverDateFlag={latestHandoverDateFlag}
					/>
				))}
			</div>
		</React.Suspense>
	);
};
