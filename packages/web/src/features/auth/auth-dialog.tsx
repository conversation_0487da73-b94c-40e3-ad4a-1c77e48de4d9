import * as React from "react";
import { AppState, useAppDispatch } from "@/store";
import { useSelector } from "react-redux";
import * as shared from "@roshn/shared";
import { useFeatureFlag } from "@/features/feature-flags";
import { RDSOtpForm } from "./otp-form";
import { useFreshCallback, useEffectOnceWhen } from "rooks";
import { AccountService, ServerErrorCode, useInjection } from "@roshn/shared";
import { useTranslation } from "react-i18next";
import {
	NationalIdFormValue,
	RDSNationalIdForm,
	VerifyNationalIdForm,
} from "../nafath";
import { PasswordForm, PasswordFormValue } from "./password-form";
import { ResetPasswordForm } from "./reset-password";
import { useGoogleReCaptcha } from "react-google-recaptcha-v3";
import {
	trackClickContinue,
	trackSignUpSuccess,
	trackAuthErrorSessionTimeout,
	trackAuthErrorCaptchaVerificationFailed,
	trackAuthErrorLoginFailure,
	trackAuthErrorMaximumRetryExceeded,
	trackAuthErrorUnknownRequestFailure,
	trackAuthErrorUserLockedOut,
	trackAuthErrorGeneric,
	trackAuthErrorMaximumResendExceeded,
	trackAuthErrorUserLockedOutPermanently,
} from "./redux/action";
import { generateAppPath } from "@/routes";
import { AppPaths } from "@/routes/app-paths";
import { useDetectKeyboardOpen } from "@/hooks/use-detect-keyboard-open";
import humanizeDuration from "humanize-duration";
import { AuthClientManager } from "@/services/auth-client-manager";
import { useProfileCollectorTrigger } from "../account/hooks/use-profile-collector-trigger";
import { useBrokerageContext } from "@/pages/property-purchase/brokerage-context";
import { PhoneNumberForm } from "./phone-number-form";
import { ProfileForm, ProfileFormValue } from "./profile-form";
import { authModalSlice } from "@/store/global-modal-slices";
import { trackLoginAccount } from "@/features/main-menu/redux/action";
import { createInfobipProfile } from "@/utils/create-infobip-profile";
// Import the Infobip interface
import "@/types/infobip";
import { RDSDialog } from "@/components/rds-dialog";
import { generateLoadingLogo } from "../loading";
import { COMMUNITY_NAMES } from "@/constants";
import { RDSButton } from "@roshn/ui-kit";
import { useState } from "react";

export type AuthDialogProps = {
	onSuccess?: () => void;
};

type AuthDialogContentProps = {
	onClose?: () => void;
	isOpen: boolean;
	logo: React.ReactNode;
	showNafathVerification?: boolean;
};

type SubmitPhoneNumberArgs = {
	country: string;
	phoneNumber: string;
};
const frErrorPrefix = "features.signUp.frErrMsgs";

const FRErrorToAnalyticsActionsMap = {
	[ServerErrorCode.CAPTCHA_VERIFICATION_FAILED]:
		trackAuthErrorCaptchaVerificationFailed,
	[ServerErrorCode.LOGIN_FAILURE]: trackAuthErrorLoginFailure,
	[ServerErrorCode.MAXIMUM_RETRY_EXCEEDED]: trackAuthErrorMaximumRetryExceeded,
	[ServerErrorCode.MAXIMUM_RESEND_EXCEEDED]:
		trackAuthErrorMaximumResendExceeded,
	[ServerErrorCode.SESSION_HAS_TIMED_OUT]: trackAuthErrorSessionTimeout,
	[ServerErrorCode.UNKNOWN_REQUEST_FAILURE]:
		trackAuthErrorUnknownRequestFailure,
	[ServerErrorCode.USER_LOCKED_OUT]: trackAuthErrorUserLockedOut,
	[ServerErrorCode.USER_LOCKED_OUT_PERMANENTLY]:
		trackAuthErrorUserLockedOutPermanently,
};

const useLocalizedFRError = (errCode?: ServerErrorCode) => {
	const { t, i18n } = useTranslation(undefined, {
		keyPrefix: frErrorPrefix,
	});

	const translationParams = React.useMemo(() => {
		let params: Record<string, string> = {};

		if (errCode === "USER_LOCKED_OUT") {
			const lockOutDuration = humanizeDuration(
				+import.meta.env.VITE_FR_LOCKOUT_DURATION,
				{
					fallbacks: ["en"],
					language: i18n.language,
					maxDecimalPoints: 0,
				},
			);

			params = {
				...params,
				lockOutDuration,
			};
		}

		return params;
	}, [errCode]);

	return errCode
		? t(errCode, {
				...translationParams,
			})
		: undefined;
};

// Helper function to mask phone number - show only last 5 digits
const maskPhoneNumber = (phoneNumber: string | undefined | null): string => {
	if (!phoneNumber || phoneNumber.length < 5) {
		return 'XXXXX';
	}
	const visibleDigits = phoneNumber.slice(-5);
	const maskedLength = phoneNumber.length - 5;
	return 'X'.repeat(maskedLength) + visibleDigits;
};

export const useAuthDialog = ({ onSuccess }: { onSuccess?: () => void }) => {
	const dispatch = useAppDispatch();

	const authClientManager =
		shared.useInjection<AuthClientManager>(AuthClientManager);
	const accountService = useInjection<AccountService>(AccountService);

	const { executeRecaptcha } = useGoogleReCaptcha();

	const [handlingSuccess, setHandlingSuccess] = React.useState(false);
	const isSignUp = React.useRef(false);

	/**
	 * Update data layer with user information after login
	 * @returns {Promise<void>}
	 */
	const updateDataLayerOnLogin = async (): Promise<void> => {
		dispatch(trackLoginAccount());
		createInfobipProfile();
	};

	const setLoginState = useFreshCallback(async () => {
		setHandlingSuccess(true);
		await authClientManager.signInUser();
		await accountService.getFullProfile();
		updateDataLayerOnLogin();
		if (isSignUp.current) {
			dispatch(trackSignUpSuccess());
		}
		onSuccess?.();
	});

	const didResetPassword = React.useRef(false);
	const [openPostResetPassword, setOpenPostResetPassword] =
		React.useState(false);

	const profileCollectorTrigger = useProfileCollectorTrigger();

	const handleResetPwdLater = useFreshCallback(() => {
		profileCollectorTrigger.skipUntilReload();
		setLoginState();
	});

	const handleResetPwdVerify = useFreshCallback(() => {
		profileCollectorTrigger.skipSuggestUntilReload();
		setLoginState();
	});

	const handleSuccess = useFreshCallback(async () => {
		if (didResetPassword.current) {
			setOpenPostResetPassword(true);
			return;
		}

		setLoginState();
	});

	const journey = shared.useSignInSignUp({
		onSuccess: handleSuccess,
	});

	const { renderStep, next, submitting, start } = journey;

	const loading = submitting || handlingSuccess;

	const stage =
		renderStep && shared.isHasNextStep(renderStep)
			? renderStep.getStage()
			: undefined;

	React.useEffect(() => {
		if (stage === shared.AuthStage.Profile) {
			isSignUp.current = true;
		}
		if (stage === shared.AuthStage.ResetPassword) {
			didResetPassword.current = true;
		}
	}, [stage]);

	useEffectOnceWhen(async () => {
		await start();
	}, true);

	const handlePhoneNumberFormSubmit = useFreshCallback(
		async (values: SubmitPhoneNumberArgs) => {
			// Recaptcha V3 callback
			if (!executeRecaptcha) {
				// TODO handling reCaptcha V3 failure
				return;
			}

			const token = await executeRecaptcha("login");
			await journey.submitPhoneNumber({
				phoneNumber: values.phoneNumber,
				recaptchaToken: token,
			});
		},
	);

	const handleProfileFormSubmit = useFreshCallback(
		async (values: ProfileFormValue) => {
			dispatch(trackClickContinue());
			await journey.submitProfile(values);
		},
	);

	const handleForgotPassword = useFreshCallback(async () => {
		const passwordStep = renderStep as shared.SignUpPasswordStep;

		passwordStep.callbacks[1].setInputValue(1);

		await next();
	});

	const handleNinQuestionFormSubmit = useFreshCallback(
		async (national: NationalIdFormValue) => {
			const ninStep = renderStep as shared.SignUpNinQuestionStep;

			ninStep.callbacks[0].setInputValue(national.nationalId);

			await next();
		},
	);

	const handleResetPasswordFormSubmit = useFreshCallback(
		async (values: PasswordFormValue) => {
			const passwordStep = renderStep as shared.SignUpResetPasswordStep;

			passwordStep.callbacks[0].setInputValue(values.password);

			await next();
		},
	);

	const handleSubmitPasswordForm = useFreshCallback(
		async (values: PasswordFormValue) => {
			await journey.submitPassword(values);
			const nextStep = await next();
			if (nextStep?.type === "LoginSuccess") {
				await setLoginState();
			}
		},
	);

	if (journey?.serverErrorCode) {
		try {
			const actionEvent =
				FRErrorToAnalyticsActionsMap[journey?.serverErrorCode] ||
				trackAuthErrorGeneric;
			dispatch(actionEvent());
		} catch (err) {
			//noop
		}
	}

	const serverErrorMessage = useLocalizedFRError(journey.serverErrorCode);

	return {
		didResetPassword: didResetPassword.current,
		handleForgotPassword,
		handleNinQuestionFormSubmit,
		handlePhoneNumberFormSubmit,
		handleProfileFormSubmit,
		handleResetPasswordFormSubmit,
		handleResetPwdLater,
		handleResetPwdVerify,
		handleSubmitPasswordForm,
		handlingSuccess,
		isSignUp: isSignUp.current,
		journey,
		loading,
		openPostResetPassword,
		serverErrorMessage,
	} as const;
};

export const AuthDialogContent = ({
	isOpen = false,
	onClose,
	logo,
	showNafathVerification,
}: AuthDialogContentProps) => {
	const { t } = useTranslation();

	const {
		journey,
		openPostResetPassword,
		handleResetPwdLater,
		handleResetPwdVerify,
		loading,
		handlePhoneNumberFormSubmit,
		handleProfileFormSubmit,
		handleForgotPassword,
		handlingSuccess,
		handleNinQuestionFormSubmit,
		handleResetPasswordFormSubmit,
		handleSubmitPasswordForm,
		isSignUp,
		serverErrorMessage,
	} = useAuthDialog({
		onSuccess: onClose,
	});

	const {
		renderStep,
		hasServerError,
		failedPolicies,
		submittedPhoneNumber,
		submitOtp,
		otpErrorMessage,
		resendOtp,
		submitPassword,
	} = journey;

	const handleTermsClick = useFreshCallback(
		(e: React.MouseEvent<HTMLAnchorElement, MouseEvent>) => {
			e.preventDefault();
			window.open(generateAppPath(AppPaths.termsConditions), "_blank");
		},
	);

	const [loginWithDifferentCountryPhoneNumber] = useFeatureFlag(
		"AllowDifferentCountryPhoneNumber",
	);
	const [allowPremiumResident] = useFeatureFlag("AllowPremiumResident");
	const [stepSubmit, setStepSubmit] = useState<(() => void) | null>(null);
	const [stepState, setStepState] = useState<{
		loading: boolean | undefined;
		disabled: boolean | undefined;
	}>({
		loading: false,
		disabled: false,
	});
	const [isLater, setIsLater] = useState(false);
	const [isLoading, setIsLoading] = useState(false);

	let content = null;
	let title: string | undefined;
	let description: string | undefined;
	const stage =
		renderStep && shared.isHasNextStep(renderStep) && renderStep.getStage();

	const brokerageContext = useBrokerageContext();

	if (openPostResetPassword) {
		content = undefined;
		title = t("features.signUp.postResetPassword.title");
		description = t("features.signUp.postResetPassword.description");

		content = (
			<RDSDialog
				isOpen={isOpen}
				onClose={() => {}}
				showContent={false}
				showHeader={true}
				showDescription={description ? true : false}
				description={description}
				showOverlay={isOpen}
				dialogHeaderProps={{
					assetProps: {
						children: logo,
						size: "96px",
					},
					hasAsset: true,
					label: title ?? "",
					leadIcon: false,
					leadIconProps: undefined,
					trailIcon: true,
					trailIconProps: {
						onClick: () => onClose?.(),
					},
					type: "centred",
				}}
				showFooter={true}
				buttonsGroup={{
					direction: "vertical",
					buttons: [
						<RDSButton
							type="submit"
							text={t("features.signUp.postResetPassword.suggestVerify")}
							onClick={() => {
								setIsLoading(true);
								handleResetPwdVerify();
							}}
							loading={isLoading && !isLater}
							disabled={isLoading}
							isFullWidth={true}
							variant="primary"
							data-testid="continue"
							size="lg"
							css={{ maxWidth: "480px" }}
						/>,
						<RDSButton
							type="submit"
							text={t("features.signUp.postResetPassword.later")}
							onClick={() => {
								setIsLater(true);
								setIsLoading(true);
								handleResetPwdLater();
							}}
							loading={stepState.loading && isLater}
							disabled={false}
							isFullWidth={true}
							variant="secondary"
							data-testid="continue-later"
							size="lg"
							css={{ maxWidth: "480px" }}
						/>,
					],
				}}
			/>
		);
	} else if (!stage || stage === shared.AuthStage.PhoneNumber) {
		title = t("features.signUp.form1.welcome");
		description = t("features.signUp.form1.greeting");
		content = (
			<RDSDialog
				isOpen={isOpen}
				onClose={() => {}}
				showContent={true}
				showHeader={true}
				showDescription={description ? true : false}
				description={description}
				showOverlay={isOpen}
				dialogHeaderProps={{
					assetProps: {
						children: logo,
						size: "96px",
					},
					hasAsset: true,
					label: title ?? "",
					leadIcon: false,
					leadIconProps: undefined,
					trailIcon: true,
					trailIconProps: {
						onClick: () => onClose?.(),
					},
					type: "centred",
				}}
				content={
					<PhoneNumberForm
						loading={loading}
						onSubmit={handlePhoneNumberFormSubmit}
						title={t("features.signUp.form1.welcome")}
						message={t("features.signUp.form1.greeting")}
						onTermsClick={handleTermsClick}
						serverError={serverErrorMessage}
						disableSelectingCountry={
							!loginWithDifferentCountryPhoneNumber ||
							(brokerageContext as any)?.brokerageFormName
						}
						setStepSubmitHandler={(config) => {
							setStepSubmit(() => config.submit);
							setStepState({
								loading: config.loading,
								disabled: config.disabled,
							});
						}}
					/>
				}
				showFooter={true}
				buttonsGroup={{
					direction: "vertical",
					buttons: [
						<RDSButton
							type="submit"
							text={t("features.signUp.form1.continue")}
							onClick={() => stepSubmit?.()}
							loading={stepState.loading}
							disabled={stepState.disabled}
							isFullWidth={true}
							variant="primary"
							data-testid="continue"
							size="lg"
						/>,
					],
				}}
			/>
		);
	} else if (
		stage === shared.AuthStage.Otp ||
		stage === shared.AuthStage.Resend
	) {
		title = t("features.signUp.form1.welcome") || undefined;
		description = undefined;
		content = (
			<RDSDialog
				isOpen={isOpen}
				onClose={() => {}}
				showContent={true}
				showHeader={true}
				showDescription={description ? true : false}
				description={description}
				showOverlay={isOpen}
				dialogHeaderProps={{
					assetProps: {
						children: logo,
						size: "96px",
					},
					hasAsset: true,
					label: title ?? "",
					leadIcon: false,
					leadIconProps: undefined,
					trailIcon: true,
					trailIconProps: {
						onClick: () => onClose?.(),
					},
					type: "centred",
				}}
				content={
					<RDSOtpForm
						expiredAt={journey.otpExpiredAt}
						onResend={resendOtp}
						hasServerError={!!otpErrorMessage}
						onSubmit={submitOtp}
						title={t("features.signUp.form1.welcome") || undefined}
						phoneNumberTranslationKey="features.signUp.form2.description"
						phoneNumber={submittedPhoneNumber}
						loading={loading}
						setStepSubmitHandler={(config) => {
							setStepSubmit(() => config.submit);
							setStepState({
								loading: config.loading,
								disabled: config.disabled,
							});
						}}
					/>
				}
				showFooter={true}
				buttonsGroup={{
					direction: "vertical",
					buttons: [
						<RDSButton
							type="submit"
							text={t("features.signUp.form2.confirmCode")}
							onClick={() => stepSubmit?.()}
							loading={stepState.loading}
							disabled={stepState.disabled}
							isFullWidth={true}
							variant="primary"
							data-testid="continue"
							size="lg"
						/>,
					],
				}}
			/>
		);
	} else if (stage === shared.AuthStage.Password) {
		title = t("features.signUp.passwordForm.title");
		description = t("features.signUp.passwordForm.message");
		content = (
			<RDSDialog
				isOpen={isOpen}
				onClose={() => {}}
				showContent={true}
				showHeader={true}
				showDescription={description ? true : false}
				description={description}
				showOverlay={isOpen}
				dialogHeaderProps={{
					assetProps: {
						children: logo,
						size: "96px",
					},
					hasAsset: true,
					label: title ?? "",
					leadIcon: false,
					leadIconProps: undefined,
					trailIcon: true,
					trailIconProps: {
						onClick: () => onClose?.(),
					},
					type: "centred",
				}}
				content={
					<PasswordForm
						onSubmit={handleSubmitPasswordForm}
						onForgotPassword={handleForgotPassword}
						loading={loading}
						serverError={
							!failedPolicies && hasServerError
								? t("features.signUp.passwordForm.incorrectPassword")
								: failedPolicies?.[0]
						}
						setStepSubmitHandler={(config) => {
							setStepSubmit(() => config.submit);
							setStepState({
								loading: config.loading,
								disabled: config.disabled,
							});
						}}
					/>
				}
				showFooter={true}
				buttonsGroup={{
					direction: "vertical",
					buttons: [
						<RDSButton
							type="submit"
							text={t("common.confirm")}
							onClick={() => stepSubmit?.()}
							loading={stepState.loading}
							disabled={stepState.disabled}
							isFullWidth={true}
							variant="primary"
							data-testid="continue"
							size="lg"
						/>,
					],
				}}
			/>
		);
	} else if (stage === shared.AuthStage.NinQuestion) {
		// const step = renderStep as shared.SignUpNinQuestionStep;
		title =
			isSignUp && showNafathVerification
				? t("features.account.nationalIdForm.title")
				: t("features.account.nationalIdVerifyForm.title");

		description =
			isSignUp && showNafathVerification
				? t("features.account.nationalIdForm.description")
				: t("features.account.nationalIdVerifyForm.verifyDescription");

		content = (
			<RDSDialog
				isOpen={isOpen}
				onClose={() => {}}
				showContent={true}
				showHeader={true}
				showDescription={description ? true : false}
				description={description}
				showOverlay={isOpen}
				dialogHeaderProps={{
					assetProps: {
						children: logo,
						size: "96px",
					},
					hasAsset: true,
					label: title ?? "",
					leadIcon: false,
					leadIconProps: undefined,
					trailIcon: true,
					trailIconProps: {
						onClick: () => onClose?.(),
					},
					type: "centred",
				}}
				content={
					isSignUp && showNafathVerification ? (
						<RDSNationalIdForm
							onSubmit={handleNinQuestionFormSubmit}
							loading={loading}
							allowPremiumResident={allowPremiumResident}
							setStepSubmitHandler={(config) => {
								setStepSubmit(() => config.submit);
								setStepState({
									loading: config.loading,
									disabled: config.disabled,
								});
							}}
							// isInvalid={ninStep.callbacks[1].getOutput}
						/>
					) : (
						<VerifyNationalIdForm
							onSubmit={handleNinQuestionFormSubmit}
							loading={loading}
							allowPremiumResident={allowPremiumResident}
							setStepSubmitHandler={(config) => {
								setStepSubmit(() => config.submit);
								setStepState({
									loading: config.loading,
									disabled: config.disabled,
								});
							}}
						/>
					)
				}
				showFooter={true}
				buttonsGroup={{
					direction: "vertical",
					buttons: [
						<RDSButton
							type="submit"
							text={t("common.confirm")}
							onClick={() => stepSubmit?.()}
							loading={stepState.loading}
							disabled={stepState.disabled}
							isFullWidth={true}
							variant="primary"
							data-testid="continue"
							size="lg"
						/>,
					],
				}}
			/>
		);
	} else if (stage === shared.AuthStage.ResetPassword) {
		title = t("features.signUp.resetPassword.title");
		description = undefined;

		content = (
			<RDSDialog
				isOpen={isOpen}
				onClose={() => {}}
				showContent={true}
				showHeader={true}
				showDescription={description ? true : false}
				description={description}
				showOverlay={isOpen}
				dialogHeaderProps={{
					assetProps: {
						children: logo,
						size: "96px",
					},
					hasAsset: true,
					label: title ?? "",
					leadIcon: false,
					leadIconProps: undefined,
					trailIcon: true,
					trailIconProps: {
						onClick: () => onClose?.(),
					},
					type: "centred",
				}}
				content={
					<ResetPasswordForm
						onSubmit={handleResetPasswordFormSubmit}
						loading={loading}
						setStepSubmitHandler={(config) => {
							setStepSubmit(() => config.submit);
							setStepState({
								loading: config.loading,
								disabled: config.disabled,
							});
						}}
					/>
				}
				showFooter={true}
				buttonsGroup={{
					direction: "vertical",
					buttons: [
						<RDSButton
							type="submit"
							text={t("common.confirm")}
							onClick={() => stepSubmit?.()}
							loading={stepState.loading}
							disabled={stepState.disabled}
							isFullWidth={true}
							variant="primary"
							data-testid="continue"
							size="lg"
						/>,
					],
				}}
			/>
		);
	} else if (stage === shared.AuthStage.Profile) {
		title = t("features.signUp.form3.welcome");
		description = t("features.signUp.form3.description");

		content = (
			<RDSDialog
				isOpen={isOpen}
				onClose={() => {}}
				showContent={true}
				showHeader={true}
				showDescription={description ? true : false}
				description={description}
				showOverlay={isOpen}
				dialogHeaderProps={{
					assetProps: {
						children: logo,
						size: "96px",
					},
					hasAsset: true,
					label: title ?? "",
					leadIcon: false,
					leadIconProps: undefined,
					trailIcon: true,
					trailIconProps: {
						onClick: () => onClose?.(),
					},
					type: "centred",
				}}
				content={
					<ProfileForm
						hasServerError={hasServerError}
						onSubmit={handleProfileFormSubmit}
						loading={loading}
						setStepSubmitHandler={(config) => {
							setStepSubmit(() => config.submit);
							setStepState({
								loading: config.loading,
								disabled: config.disabled,
							});
						}}
					/>
				}
				showFooter={true}
				buttonsGroup={{
					direction: "vertical",
					buttons: [
						<RDSButton
							type="submit"
							text={t("features.signUp.form3.createAccount")}
							onClick={() => stepSubmit?.()}
							loading={stepState.loading}
							disabled={stepState.disabled}
							isFullWidth={true}
							variant="primary"
							data-testid="continue"
							size="lg"
						/>,
					],
				}}
			/>
		);
	}

	/**
	 * If we are handling success, we will show the previous content
	 */
	const prevNotNullContent = React.useRef(content);
	if (content) {
		prevNotNullContent.current = content;
	}
	if (!content && handlingSuccess) {
		content = prevNotNullContent.current;
	}

	React.useEffect(() => {
		if (
			!showNafathVerification &&
			stage === shared.AuthStage.NinQuestion &&
			isSignUp
		) {
			onClose?.();
		}
	}, [stage]);
	return <>{content}</>;
};

export const AuthDialog = () => {
	const RoshnLogo = generateLoadingLogo(COMMUNITY_NAMES.ROSHN); // this is a ReactNode
	const dispatch = useAppDispatch();
	const isOpen = useSelector((state: AppState) => state.authModal.open);
	const showNafathVerification = useSelector(
		(state: AppState) => state.authModal.showNafathVerification,
	);

	const handleClose = useFreshCallback(() => {
		dispatch(authModalSlice.actions.closeModal());
	});
	const isKeyboardOpen = useDetectKeyboardOpen();

	if (!isOpen) {
		return;
	}
	return (
		<>
			<AuthDialogContent
				isOpen={isOpen}
				onClose={handleClose}
				logo={<RoshnLogo />}
				showNafathVerification={showNafathVerification}
			/>
		</>
	);
};
