import { css } from "@emotion/react";
import { motion } from "framer-motion";
import { AppTheme, tabletMQ } from "@/theme";
import { Property } from "@/types/property-type";
import { generateClasses } from "@/utils/generate-classes";
import { PropertyInformation } from "./components/property-information";
import { PropertyExplore } from "./components/explorer-property";
import { PropertyAmenities } from "./components/amenities";
import { PropertyPaymentPlan } from "./components/payment-plan";
import { NeighborhoodExplore } from "./components/explore-neighborhood";
import { Container } from "@/components/container";
import { usePropertyDetail } from "@roshn/shared";
import { PropertyInfo } from "../property/property-info";
import { PropertyAttribute } from "../property/property-attributes";

export const Classes = generateClasses("discovery", [
	"center",
	"section",
	"sectionHeader",
]);

const styles = {
	propertyAttributeWrapper: (theme: AppTheme) =>
		css({
			display: "none",
			[tabletMQ(theme)]: {
				alignItems: "center",
				borderBlock: `1px solid ${theme.colors.grey[200]}`,
				display: "flex",
				justifyContent: "center",
				marginBottom: theme.spaces.lg,
				paddingBlock: theme.spaces.lg,
			},
		}),
	propertyInfo: (theme: AppTheme) =>
		css({
			[tabletMQ(theme)]: {
				display: "none",
			},
		}),
	wrapper: (theme: AppTheme) =>
		css({
			paddingBottom: 100,

			[tabletMQ(theme)]: {
				marginInlineStart: "auto",
				[`& .${Classes.center}`]: {
					textAlign: "center",
				},
			},
			[`.${Classes.section}`]: {
				marginBottom: theme.spaces.xl6,
				position: "relative",
			},
			[`.${Classes.sectionHeader}`]: {
				p: {
					...theme.typography.subtitleXL,
				},
			},
		}),
};

export type PropertyDiscoveryProps = Readonly<{
	data: Property;
	onEnterExplore3D?: () => void;
	onEnterInformation?: () => void;
	onFindMyHomeClick?: () => void;
	unit?: ReturnType<typeof usePropertyDetail>["data"];
	latestHandoverDateFlag?: boolean;
}>;

export function PropertyDiscovery({
	onEnterInformation,
	onEnterExplore3D,
	onFindMyHomeClick,
	data,
	unit,
	latestHandoverDateFlag = false,
}: PropertyDiscoveryProps) {
	return (
		<div css={styles.wrapper}>
			<Container className={Classes.section} css={styles.propertyInfo}>
				<PropertyInfo data={data} unit={unit} />
			</Container>
			<Container>
				<PropertyAttribute
					css={styles.propertyAttributeWrapper}
					attributes={{
						bathrooms: data.information.bathroom ?? 0,
						bedrooms: data.information.bedroom ?? 0,
						note: data.information.note ?? "",
						plotArea: data.information.plotArea ?? "0",
					}}
					unit={unit}
					latestHandoverDateFlag={latestHandoverDateFlag}
				/>
			</Container>
			<motion.div
				onViewportEnter={onEnterInformation}
				viewport={{ amount: "all" }}
			>
				<PropertyInformation data={data} unit={unit} />
			</motion.div>
			<motion.div
				onViewportEnter={onEnterExplore3D}
				viewport={{ amount: "all" }}
			>
				<PropertyExplore data={data} facade={unit?.facade} />
			</motion.div>
			<PropertyAmenities data={data} />
			{/* <PropertyPaymentPlan data={data} unit={unit} /> */}
			<NeighborhoodExplore
				onFindMyHomeClick={onFindMyHomeClick}
				isPropertyDetails={!!unit}
			/>
		</div>
	);
}
