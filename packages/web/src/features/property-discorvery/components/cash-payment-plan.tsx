import { Typography } from "@/components/typography";
import { AppTheme } from "@/theme";
import { InstallmentPlan } from "@/types/property-type";
import { css } from "@emotion/react";
import * as React from "react";
import { useTranslation } from "react-i18next";
import { InfoIcon } from "@/components/icons";
import { generateClasses } from "@/utils/generate-classes";
import { CashPaymentMilestone } from "@/features/cash-payment-milestone/cash-payment-milestone";

const Classes = generateClasses("Info", ["icon"]);

const styles = {
	header: (theme: AppTheme) =>
		css({
			fontWeight: theme.typography.fontWeightMedium,
		}),
	infoNote: (theme: AppTheme) =>
		css({
			[`& p`]: {
				margin: 0,
			},
			color: theme.colors.text.primary,
			display: "flex",
			gap: theme.spaces.sm,

			[`& .${Classes.icon}`]: {
				flex: "0 0 24px",
			},

			justifyContent: "start",
		}),
	paymeyPlan: (theme: AppTheme) =>
		css({
			borderRadius: "16px",
			padding: theme.spaces.md,
		}),
};

export function CashPaymentPlan({
	paymentPlan,
	showPrice,
}: {
	paymentPlan?: InstallmentPlan;
	showPrice: boolean;
}) {
	const { t } = useTranslation();

	if (!paymentPlan) return null;

	return (
		<>
			<Typography variant="h6" css={styles.header}>
				{t("features.cash.milestones")}
			</Typography>
			<Typography variant="captionM">
				{t("features.propertyReservation.paymentPlan.description2")}
			</Typography>
			<CashPaymentMilestone
				plan={paymentPlan.plan}
				planFromReservation={undefined}
				showPrice={showPrice}
				isFromReservation={false}
				css={styles.paymeyPlan}
			/>
			<div css={styles.infoNote}>
				<InfoIcon className={Classes.icon} />
				<Typography variant="bodyS">
					{t("features.cash.noteCalculatorReservation")}
				</Typography>
			</div>
		</>
	);
}
