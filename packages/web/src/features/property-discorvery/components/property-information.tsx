import { DownloadIcon } from "@/components/icons";
import { Typography } from "@/components/typography";
import { AppTheme } from "@/theme";
import { Property } from "@/types/property-type";
import { css } from "@emotion/react";
import { useTranslation } from "react-i18next";
import { Container } from "@/components/container/container";
import { Classes } from "../property-discovery";
import * as Collapsible from "@/components/collapsible";
import { useState } from "react";
import { usePropertyDetail } from "@roshn/shared";
import { RDSButton, RDSLink as Link } from "@roshn/ui-kit";

const styles = {
	downloadIcon: (theme: AppTheme) =>
		css({
			marginRight: theme.spaces.sm,
		}),
	infoDownload: css({
		[`& a:hover`]: {
			textDecoration: "none",
		},
		alignItems: "center",
		display: "flex",
		justifyContent: "center",
	}),
	infoItem: (theme: AppTheme) =>
		css({
			alignItems: "center",
			display: "flex",
			justifyContent: "space-between",
			marginBottom: theme.spaces.md,
		}),
	infoLabel: (theme: AppTheme) =>
		css({
			color: theme.colors.text.secondary,
			textTransform: "uppercase",
		}),
	infoList: css({
		[`& li`]: {
			listStyleType: "none",
		},
		padding: 0,
	}),
	infoNote: (theme: AppTheme) =>
		css({
			color: theme.colors.text.secondary,
			marginBottom: theme.spaces.lg,
		}),
	infoValue: (theme: AppTheme) =>
		css({
			color: theme.colors.text.primary,
		}),
};

type PropertyInformationProps = Readonly<{
	data: Property;
	unit?: ReturnType<typeof usePropertyDetail>["data"];
}>;

export function PropertyInformation({ data, unit }: PropertyInformationProps) {
	const { t } = useTranslation();

	const [expanded, setExpanded] = useState(true);

	return (
		<Container className={Classes.section}>
			<Collapsible.Root open={expanded} onOpenChange={setExpanded}>
				<Collapsible.Trigger className={Classes.sectionHeader}>
					{t("features.propertyDiscovery.propertyInformation")}
				</Collapsible.Trigger>
				<Collapsible.Content>
					<ul css={styles.infoList}>
						<li css={styles.infoItem}>
							<Typography variant="subtitleS" css={styles.infoLabel}>
								{t("features.propertyDiscovery.plotArea")}
							</Typography>
							<Typography css={styles.infoValue}>
								{`${unit?.plotArea ?? data.information?.plotArea ?? "N/A"} ${(unit?.plotArea ?? data.information?.plotArea) ? t("common.sqm") : ""}`.trim()}
							</Typography>
						</li>
						<li css={styles.infoItem}>
							<Typography variant="subtitleS" css={styles.infoLabel}>
								{t("features.propertyDiscovery.grossFloorArea")}
							</Typography>
							<Typography css={styles.infoValue}>
								{`${unit?.grossFloorArea ?? data.information?.grossFloorArea ?? "N/A"} ${(unit?.grossFloorArea ?? data.information?.grossFloorArea) ? t("common.sqm") : ""}`.trim()}
							</Typography>
						</li>

						{unit?.orientation ? (
							<li css={styles.infoItem}>
								<Typography variant="subtitleS" css={styles.infoLabel}>
									{t("features.communities.propertyCard.orientationTitle")}
								</Typography>
								<Typography css={styles.infoValue}>
									{unit?.orientation ? t(`features.communities.propertyCard.orientationSize.${unit.orientation}`) : "N/A"}
								</Typography>
							</li>
						) : (
							""
						)}
						<li css={styles.infoItem}>
							<Typography variant="subtitleS" css={styles.infoLabel}>
								{t("features.propertyDiscovery.bedroom")}
							</Typography>
							<Typography css={styles.infoValue}>
								{unit?.bedroom ? unit.bedroom : data.information?.bedroom ?? "N/A"}
							</Typography>
						</li>

						<li css={styles.infoItem}>
							<Typography variant="subtitleS" css={styles.infoLabel}>
								{t("features.propertyDiscovery.bathroom")}
							</Typography>
							<Typography css={styles.infoValue}>
								{unit?.bathroom ? unit.bathroom : data.information?.bathroom ?? "N/A"}
							</Typography>
						</li>

						<li css={styles.infoItem}>
							<Typography variant="subtitleS" css={styles.infoLabel}>
								{t("features.propertyDiscovery.roofTerrace")}
							</Typography>
							<Typography css={styles.infoValue}>
								{data.information?.roofTerrace
									? t("common.yes")
									: t("common.no")}
							</Typography>
						</li>

						<li css={styles.infoItem}>
							<Typography variant="subtitleS" css={styles.infoLabel}>
								{t("features.propertyDiscovery.additionalRooms")}
							</Typography>
							<Typography css={styles.infoValue}>
								{unit?.additionalRooms ? unit.additionalRooms : data.information?.additionalRooms ?? "N/A"}
							</Typography>
						</li>

						<li css={styles.infoNote}>
							<Typography variant="captionM">
								{t(data.information?.note ?? "")}
							</Typography>
						</li>

						{data.floorPlanLink && (
							<li css={styles.infoDownload}>
								<Link
									download
									target="_blank"
									underline="none"
									rel="noreferrer"
									href={data.floorPlanLink}
									css={{ marginInline: "auto" }}
								>
									<RDSButton
										leadIcon={<DownloadIcon css={styles.downloadIcon} />}
										variant="secondary"
										text={t("features.propertyDiscovery.downLoadFloorPlans")}
									/>
								</Link>
							</li>
						)}
					</ul>
				</Collapsible.Content>
			</Collapsible.Root>
		</Container>
	);
}
