import * as React from "react";
import { desktopMQ, mobileMQ } from "@/theme";
import { css, Theme, useTheme } from "@emotion/react";
import { useTranslation } from "react-i18next";
import { spin } from "@/theme/animations/keyframes";
import {
	PropertiesNoResult,
	PropertiesResultSkeleton,
} from "./properties-result-skeleton";
import { replaceAny } from "../../utils";
import {
	useListViewProperties,
	useMapViewProperties,
} from "../../hooks/use-search-property";
import { FilterUnitsArg } from "../../filter-schema";
import { PropertyItemV4 } from "./property-itemV4";
import { PropertiesNoResultV4 } from "./properties-result-skeletonV4";
import { useSearchParams } from "@remix-run/react";
import {
	RDSEmptyState,
	RDSPagination,
	RDSSegmentControl,
	RDSTypography,
} from "@roshn/ui-kit";
import { LocationIcon, MenuIcon } from "@/components/icons";
import { MapViewPage } from "@/pages/map-view";

export const styles = {
	btnDiv: () =>
		css({
			alignItems: "center",
			display: "flex",
			flexDirection: "column",
			gap: "1.5rem",
			justifyContent: "center",
			marginTop: "2.5rem",
		}),
	button: () =>
		css({
			padding: "1rem 3.375rem",
			textTransform: "lowercase",
		}),
	buttonStyle: () =>
		css({
			padding: "0",
		}),
	ctrlBtn: () =>
		css({
			gap: "0.625rem",
			padding: "1rem 0.75rem",
		}),
	errorStateContainer: css({
		alignItems: "center",
		display: "flex",
		height: "40dvh",
		justifyContent: "center",
	}),
	header: (theme: Theme) =>
		css({
			...theme?.rds?.typographies.label.emphasis.lg,
			textTransform: "uppercase",
			[`@media (min-width: ${theme?.rds?.breakPoints.tablet})`]: {
				...theme?.rds?.typographies.heading.emphasis.h4,
			},
			whiteSpace: "nowrap",
		}),
	headerContainer: (theme: Theme) =>
		css({
			alignItems: "center",
			display: "flex",
			marginInline: "auto",
			paddingBlock: theme?.rds?.spaces.md,
			width: "100%",
			gap: theme?.rds?.spaces.md,
			[mobileMQ(theme)]: {
				gap: theme?.rds?.spaces.sm,
			}
		}),
	headerContainerDivider: (theme: Theme) =>
		css({
			borderTop: `0.063rem solid ${theme.rds.brand.color.secondary[400]}`,
			width: "100%",
		}),

	loadingProperties: css({
		alignItems: "center",
		display: "flex",
		height: "40dvh",
		justifyContent: "center",
	}),
	mapContainer: (theme: Theme) =>
		css({
			"& > div > div": {
				'[dir="rtl"] &': {
					marginLeft: 0,
					marginRight: theme?.rds?.spaces.md,
				},
				height: "2.5rem",
			},
			[mobileMQ(theme)]: {
				bottom: "1rem",
				display: "flex",
				height: "4.25rem",
				insetInline: "0",
				justifyContent: "center",
				position: "fixed",
				zIndex: "99",
			},
		}),
	mapViewContainer: (theme: Theme) =>
		css({
			border: "0.063rem solid #E5E5E5",
			borderRadius: "0.5rem",
			height: "100vh",
			minHeight: "25rem",
			overflow: "hidden",
			width: "100%",
			[mobileMQ(theme)]: {
				height: "31.25rem",
			},
		}),
	mapViewPage: css({
		[`@media (max-width: 767px)`]: {
			bottom: 0,
			marginBlock: 0,
		},
		height: "100vh",
		left: 0,
		margin: 0,
		overflow: "hidden",
		padding: 0,
		position: "fixed",
		top: 0,
		width: "100vw",
	}),
	resultWrapper: (theme: Theme) =>
		css({
			a: {
				"> div": {
					height: "100%",
					maxWidth: "unset",
					minWidth: "unset",
				},
			},
			display: "grid",
			gridGap: theme.spaces.lg,
			gridTemplateColumns: "repeat(auto-fill, minmax(272px, 1fr))",
			justifyContent: "space-between",
			width: "100%",
			[desktopMQ(theme)]: {
				gridTemplateColumns: "repeat(auto-fill, minmax(320px, 1fr))",
			},
		}),
	results: (theme: Theme) =>
		css({
			"@media(max-width:768px)": {
				paddingTop: "1.5rem",
				...theme?.rds?.typographies.heading.emphasis.h4,
			},
			...theme?.rds?.typographies.heading.h3,
			alignItems: "center",
			color: theme?.rds?.color.text.ui.primary,
			display: "flex",
			gap: "0.625rem",
			paddingBottom: "1.5rem",
			textTransform: "capitalize",
		}),
	resultsSpan: (theme: Theme) =>
		css({
			...theme?.rds?.typographies.label.xl,
			textTransform: "lowercase",
		}),
	spinner: css({
		animation: `${spin} 2s infinite linear`,
	}),
	spinnerWrapper: (theme: Theme) =>
		css({
			display: "flex",
			justifyContent: "center",
			marginBlockStart: theme.spaces.lg1,
		}),
	title: (theme: Theme) =>
		css({
			marginBlockEnd: theme.spaces.md,
		}),
	viewToggleContainer: (theme: Theme) =>
		css({
			alignItems: "center",
			display: "flex",
			justifyContent: "space-between",
			marginBottom: theme?.rds?.spaces.md,
			[mobileMQ(theme)]: {
				flexDirection: "column",
				gap: theme?.rds?.spaces.sm,
			},
		}),
	wrapper: (theme: Theme) =>
		css({
			marginBlockStart: theme.spaces.xs,
		}),
};

const PropertyList = ({ items }: any) => (
	<div css={styles.resultWrapper}>
		{items?.map((item: any) => (
			<PropertyItemV4 key={item.unitCode} data={item} />
		))}
	</div>
);

type PropertiesResultProps = {
	enableBrokerageFeature?: boolean;
	latestHandoverDateFlag?: boolean;
	paramsState: FilterUnitsArg;
};

export const PropertiesResultV4 = ({
	paramsState,
	latestHandoverDateFlag,
	enableBrokerageFeature,
}: PropertiesResultProps) => {
	const theme = useTheme();
	const { t } = useTranslation(undefined, {
		keyPrefix: "features.propertyFinder",
	});
	const [searchParams, setSearchParams] = useSearchParams({
		page: "1",
		selectedTab: "buy",
		view: "list",
	});

	const currentView = (searchParams.get("view") as "list" | "map") || "list";

	const pageParam = parseInt(searchParams.get("page") || "1", 10);
	const currentPage = pageParam < 1 ? 1 : pageParam;
	const selectedTab = searchParams.get("selectedTab") || "buy";

	const prevParamsRef = React.useRef(paramsState);

	React.useEffect(() => {
		const currentSearch = searchParams.get("search") || "";
		const newSearch = paramsState?.search || "";
		const prevSearch = prevParamsRef.current?.search || "";

		const searchChanged = newSearch !== prevSearch;
		const urlOutOfSync = currentSearch !== newSearch;

		if (searchChanged && urlOutOfSync) {
			setSearchParams(
				(prev) => {
					const newParams = new URLSearchParams(prev);

					if (newSearch.trim() === "") {
						newParams.delete("search");
					} else {
						newParams.set("search", newSearch);
					}

					newParams.set("page", "1");
					return newParams;
				},
				{ replace: true },
			);
		}

		prevParamsRef.current = paramsState;
	}, [paramsState?.search, searchParams, setSearchParams]);

	React.useEffect(() => {
		const currentParams = new URLSearchParams(searchParams);
		let needsUpdate = false;
		const newParams = new URLSearchParams(currentParams);

		const handleArrayParam = (
			paramName: string,
			values: any[],
			urlKey?: string,
		) => {
			const urlParamName = urlKey || paramName;
			if (values && values.length > 0 && !values.includes("any")) {
				const filteredValues = values.filter((v) => v !== "any");
				if (filteredValues.length > 0) {
					const valueStr = filteredValues.join(",");
					if (currentParams.get(urlParamName) !== valueStr) {
						newParams.set(urlParamName, valueStr);
						needsUpdate = true;
					}
				} else if (currentParams.has(urlParamName)) {
					newParams.delete(urlParamName);
					needsUpdate = true;
				}
			} else if (currentParams.has(urlParamName)) {
				newParams.delete(urlParamName);
				needsUpdate = true;
			}
		};

		const handleSingleParam = (paramName: string, value: any) => {
			if (value && value !== "any" && !value.includes?.("any")) {
				const valueStr = Array.isArray(value) ? value[0] : value;
				if (currentParams.get(paramName) !== String(valueStr)) {
					newParams.set(paramName, String(valueStr));
					needsUpdate = true;
				}
			} else if (currentParams.has(paramName)) {
				newParams.delete(paramName);
				needsUpdate = true;
			}
		};

		handleArrayParam("bedrooms", paramsState.bedrooms);
		handleArrayParam("bathrooms", paramsState.bathrooms);
		handleArrayParam("propertyType", paramsState.propertyType);

		handleArrayParam("communities", paramsState.communities);

		handleArrayParam("amenities", paramsState.otherAmenities);

		handleSingleParam("furnishing", paramsState.furnishing);

		handleSingleParam("propertyAge", paramsState.buildingAge);

		handleArrayParam("developers", paramsState.builderName);

		if (
			paramsState.plotArea &&
			paramsState.plotArea.length > 0 &&
			!paramsState.plotArea.includes("any")
		) {
			const plotAreaValue = paramsState.plotArea[0];
			if (currentParams.get("plotArea") !== String(plotAreaValue)) {
				newParams.set("plotArea", String(plotAreaValue));
				needsUpdate = true;
			}
		} else if (currentParams.has("plotArea")) {
			newParams.delete("plotArea");
			needsUpdate = true;
		}

		if (
			paramsState.furnishing &&
			paramsState.furnishing.length > 0 &&
			!paramsState.furnishing.includes("any")
		) {
			const furnishingValue = paramsState.furnishing[0];
			if (
				furnishingValue !== "any" &&
				currentParams.get("furnishing") !== String(furnishingValue)
			) {
				newParams.set("furnishing", String(furnishingValue));
				needsUpdate = true;
			}
		} else if (currentParams.has("furnishing")) {
			newParams.delete("furnishing");
			needsUpdate = true;
		}

		if (
			paramsState.buildingAge &&
			paramsState.buildingAge.length > 0 &&
			!paramsState.buildingAge.includes("any")
		) {
			const propertyAgeValue = paramsState.buildingAge[0];
			if (
				propertyAgeValue !== "any" &&
				currentParams.get("propertyAge") !== String(propertyAgeValue)
			) {
				newParams.set("propertyAge", String(propertyAgeValue));
				needsUpdate = true;
			}
		} else if (currentParams.has("propertyAge")) {
			newParams.delete("propertyAge");
			needsUpdate = true;
		}

		if (
			paramsState.sortByPrice &&
			typeof paramsState.sortByPrice === "object"
		) {
			const { minPrice, maxPrice } = paramsState.sortByPrice;
			const currentMin = currentParams.get("minPrice");
			const currentMax = currentParams.get("maxPrice");

			if (currentMin !== String(minPrice) || currentMax !== String(maxPrice)) {
				newParams.set("minPrice", String(minPrice));
				newParams.set("maxPrice", String(maxPrice));
				needsUpdate = true;
			}
		} else {
			if (currentParams.has("minPrice") || currentParams.has("maxPrice")) {
				newParams.delete("minPrice");
				newParams.delete("maxPrice");
				needsUpdate = true;
			}
		}

		if (needsUpdate) {
			if (currentParams.get("page") !== "1") {
				newParams.set("page", "1");
			}
			setSearchParams(newParams, { replace: true });
		}
	}, [
		paramsState.bedrooms,
		paramsState.bathrooms,
		paramsState.propertyType,
		paramsState.communities,
		paramsState.otherAmenities,
		paramsState.furnishing,
		paramsState.buildingAge,
		paramsState.builderName,
		paramsState.plotArea,
		paramsState.sortByPrice,
		paramsState.listingType,
		searchParams,
		setSearchParams,
	]);

	const stableParamsState = React.useMemo(
		() => replaceAny(paramsState),
		[paramsState],
	);

	const listViewQuery = useListViewProperties(stableParamsState, currentPage);
	const mapViewQuery = useMapViewProperties(stableParamsState);

	const data = currentView === "map" ? mapViewQuery.data : listViewQuery.data;
	const isLoading =
		currentView === "map" ? mapViewQuery.isLoading : listViewQuery.isLoading;
	const error =
		currentView === "map" ? mapViewQuery.error : listViewQuery.error;

	React.useEffect(() => {
		const needsPageCorrection = pageParam < 1;
		const currentUrlSelectedTab = searchParams.get("selectedTab");
		const needsTabCorrection =
			currentUrlSelectedTab !== "buy" && currentUrlSelectedTab !== "rent";
		const currentUrlView = searchParams.get("view");
		const needsViewCorrection =
			currentUrlView !== "list" && currentUrlView !== "map";

		if (needsPageCorrection || needsTabCorrection || needsViewCorrection) {
			setSearchParams(
				(prev) => {
					const newParams = new URLSearchParams(prev);
					if (needsPageCorrection) {
						newParams.set("page", "1");
					}
					if (needsTabCorrection) {
						newParams.set("selectedTab", "buy");
					}
					if (needsViewCorrection) {
						newParams.set("view", "list");
					}
					return newParams;
				},
				{ replace: true },
			);
		}
	}, [searchParams, pageParam, setSearchParams]);

	const totalData = React.useMemo(() => {
		if (currentView === "map") {
			return mapViewQuery.data?.pageParams[0]?.total ?? 0;
		}
		return listViewQuery.data?.pageParams[0]?.total ?? 0;
	}, [currentView, mapViewQuery.data, listViewQuery.data]);

	const totalPages = Math.ceil(totalData / 12);

	const handleViewChange = (value: string) => {
		const newView = value as "list" | "map";

		setSearchParams(
			(prev) => {
				const newParams = new URLSearchParams(prev);
				newParams.set("view", newView);

				if (newView === "list") {
					newParams.set("page", "1");
				}

				return newParams;
			},
			{ replace: false },
		);
	};

	if (isLoading) {
		if (currentView === "list") {
			return <PropertiesResultSkeleton />;
		} else {
			return (
				<div css={styles.loadingProperties}>
					<RDSTypography fontName={theme?.rds?.typographies.label.emphasis.lg}>
						{t("loadingProperties")}
					</RDSTypography>
				</div>
			);
		}
	}

	if (error) {
		console.error("Error loading properties:", error);
		return (
			<div css={styles.errorStateContainer}>
				<RDSEmptyState
					size="sm"
					heading={t("listingErrorHeading")}
					appearance="warning"
					body={t("listingErrorDescription")}
				/>
			</div>
		);
	}

	const displayNoResult = (currentView: string) => {
		if (enableBrokerageFeature) {
			return (
				<>
					<PropertiesNoResultV4 data={data} currentView={currentView} />
				</>
			);
		} else {
			return <PropertiesNoResult />;
		}
	};
	if (data === undefined || data?.pages?.length < 1) {
		return displayNoResult(currentView);
	}

	if (currentView === "map") {
		return (
			<div css={styles.mapViewPage}>
				<MapViewPage
					isEmbedded={true}
					properties={data?.pages || []}
					paramsState={stableParamsState}
				/>
			</div>
		);
	}
	return (
		<div css={styles.wrapper}>
			{paramsState?.search && (
				<RDSTypography css={styles.results}>
					{t("results")}:{" "}
					<RDSTypography
						tag="span"
						css={styles.resultsSpan}
					>{`${totalData} ${t("properties")}`}</RDSTypography>
				</RDSTypography>
			)}

			<div css={styles.headerContainer}>
				<RDSTypography
					color={theme?.rds?.color.text.ui.primary}
					css={styles.header}
				>
					{t("homesForYou")}
				</RDSTypography>
				<div css={styles.headerContainerDivider}></div>
				<div css={styles.mapContainer}>
					<RDSSegmentControl
						items={[
							{
								label: t("listView"),
								leadIcon: <MenuIcon />,
								value: "list",
							},
							{
								label: t("mapView"),
								leadIcon: <LocationIcon />,
								value: "map",
							},
						]}
						defaultValue="list"
						value={currentView}
						onTabChange={handleViewChange}
						size="lg"
					/>
				</div>
			</div>

			<PropertyList
				items={data?.pages}
				latestHandoverDateFlag={latestHandoverDateFlag}
			/>

			<div css={{ marginTop: "2.5rem" }}>
				{totalPages && totalPages > 1 && currentView === "list" && (
					<RDSPagination
						pageCount={totalPages}
						activePage={currentPage}
						onPageChange={(page) => {
							window.scrollTo({ behavior: "smooth", left: 0, top: 0 });
							setSearchParams((prev) => {
								const newParams = new URLSearchParams(prev);
								newParams.set("page", page.toString());
								newParams.set("view", currentView);
								newParams.set("selectedTab", selectedTab);
								return newParams;
							});
						}}
					/>
				)}
			</div>
		</div>
	);
};
