import { useCallback, useEffect, useState, useRef, useMemo } from "react";
import { styles as sharedStyles } from "./styles";
import { FilterBarProps } from "./filter-bar/filter-bar";
import { Merge } from "type-fest";
import * as _ from "lodash-es";
import { DefaultParams, FilterUnitsArg } from "../filter-schema";
import RoshnPattern from "@roshn/ui-kit/components/legacy/atoms/RoshnPattern/RoshnPattern.js";
import {
	RDSBadge,
	RDSIconButton,
	RDSTabGroup,
	RDSTagInteractive,
	RDSSearchInput,
	RDSSelectSlotDropdown,
	RDSRadioGroup,
	RDSRadio,
	RDSSwitch,
	RDSButton,
	RDSSideDrawer,
	RDSCheckboxGroup,
	RDSTextInput,
	RDSSlider,
	RDSRangeSlider,
	RDSLinkButton,
	RDSSelect,
	Image,
} from "@roshn/ui-kit";
import {
	useTheme,
	css,
	ThemeProvider as EmotionThemeProvider,
	Theme,
} from "@emotion/react";
import { PropertiesResultV4 } from "./result/properties-result-v4";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { AppState } from "@/store";
import { AuthService, useInjection } from "@roshn/shared";
import FilterDropdownMobile from "./result/more-filter-mobile";
import { FilterIcon } from "@/components/icons";
import { useParams, useSearchParams } from "@remix-run/react";
import { getThemeBare } from "@roshn/ui-kit/themes/index.js";
import { useGetDevelopers } from "../hooks/use-search-property";
import { DeveloperValue } from "../types";
import { createPortal } from "react-dom";
import { getSearchParamsState } from "../utils";
import { setSearchParam } from "@/store/searchParams-slice";
import { formatMoney } from "@/utils/intl";

const styles = {
	containerWrapper: (isTopHeight: any) =>
		css({
			position: "sticky",
			zIndex: "999",
			top: isTopHeight,
			width: "100%",
			transition: "all 0.3s ease-in-out",
			scrollBehavior: "smooth",
		}),
	divider: (theme: Theme) =>
		css({
			borderBottomColor: theme.colors.checkbox.disabled,
			marginInline: theme.spaces.xs3,
		}),
	buttonWrapper: () =>
		css({
			position: "relative",
			display: "inline-block",
		}),
	badge: () =>
		css({
			position: "absolute",
			top: "-0.25rem",
			right: 0,
			'[dir="rtl"]': {
				right: "unset",
				left: 0,
			},
		}),
	badgeTag: () =>
		css({
			width: "100%",
			display: "flex",
			alignItems: "center",
			gap: 12,
			overflowX: "auto",
			"> div ": {
				minWidth: "unset",
				whiteSpace: "nowrap",
				marginTop: "0.75rem",
			},
			"&::-webkit-scrollbar": {
				display: "none",
			},
		}),
	moreFiltersButton: (theme: Theme) =>
		css({
			":hover": {
				backgroundColor: theme?.rds?.color.background.ui.secondary.hover,
				color: theme?.rds?.color.text.brand.secondary.hover,
				"& svg *": {
					stroke: theme?.rds?.color.icon.brand.secondary.default,
				},
			},
			":active": {
				backgroundColor: theme?.rds?.color.background.ui.secondary.pressed,
				color: theme?.rds?.color.text.brand.secondary.pressed,
				"& svg *": {
					stroke: theme?.rds?.color.icon.brand.secondary.default,
				},
			},
		}),
	iconButton: (theme: Theme) =>
		css({
			top: 0,
			display: "flex",
			alignItems: "center",
			background: "#fff",
			border: `1px solid #000`,
			height: "2.5rem",
			width: "2.5rem",
			maxWidth: "2.5rem",
			maxHeight: "2.5rem",
			"& svg": {
				"& path": {
					stroke: `${theme?.rds?.colors.black} !important`,
				},
			},
			":hover": {
				background: "inherit",
				color: `${theme?.rds?.colors.black} !important`,
			},
		}),
	imageContainer: (theme: Theme, urlCurrentView?: string) =>
		css({
			display: "flex",
			flexDirection: "column",
			alignItems: "flex-start",
			gap: "1rem",
			alignSelf: "stretch",
			background: "rgba(249, 249, 249, 0.48)",
			position: urlCurrentView === "list" ? "sticky" : "fixed",
			top: "0",
			zIndex: "999",
			width: "100%",
			transition: "padding 0.3s ease-in-out",
			border: "1px solid #E5E4E6",
			backdropFilter: "blur(1.25rem)",
			padding: "6.5rem 2rem 1rem",
			[`@media (min-width: ${theme?.rds?.breakPoints.tablet})`]: {
				padding: "8.5rem 2rem 1rem",
			},
			[`@media  (min-width: 768px) and (max-width: 1023px)`]: {
				padding: "7.5rem 2rem 1rem",
			},
			[`@media (max-width: 768px)`]: {
				border: urlCurrentView === "list" ? "1px solid #E5E4E6" : "none",
				background:
				urlCurrentView === "list" ? "rgba(249, 249, 249, 0.48)" : "transparent",
				backdropFilter: urlCurrentView === "list" ? "blur(1.25rem)" : "blur(0)",
			},
		}),
	filter: (theme: Theme) =>
		css({
			display: "flex",
			flexDirection: "column",
			gap: theme.spaces.sm,
			marginBlock: theme.spaces.xs,
			padding: 0,
			width: "100%",
			transition: "margin 0.3s ease-in-out, padding 0.3s ease-in-out",
			marginTop: "-0.625rem",
			maxWidth: "100%",
			margin: "0 auto",
			[`@media (min-width: ${theme?.rds?.breakPoints.tablet})`]: {
				maxWidth: "85%",
			},
			[`@media (min-width: ${theme?.rds?.breakPoints.desktop})`]: {
				maxWidth: "60.5rem",
			},
			[`@media (min-width: ${theme?.rds?.breakPoints.largeDesktop})`]: {
				maxWidth: "70.25rem",
			},
		}),
	filterContainer: (theme: Theme) =>
		css({
			display: "flex",
			alignItems: "flex-end",
			gap: theme?.rds?.spaces.xs2,
			width: "100%",
			flexWrap: "wrap",
			padding: 0,

			[`@media (min-width: 577px)`]: {
				gap: theme?.rds?.spaces.xs2,
				flexDirection: "row",
				justifyContent: "flex-start",
			},

			[`@media (max-width: 767px)`]: {
				gap: theme?.rds?.spaces.xs4,
				flexDirection: "row",
				flexWrap: "nowrap",
			},

			[`@media (min-width: 768px)`]: {
				gap: theme?.rds?.spaces.sm,
			},

			[`@media (min-width: 1025px)`]: {
				flexWrap: "nowrap",
			},

			[`@media (min-width: 767px) and (max-width: 1024px)`]: {
				gap: theme?.rds?.spaces.xs4,
			},
		}),
	filterWrapper: () =>
		css({
			display: "flex",
			flexDirection: "column",
			background: "transparent",
			transition: "background 0.3s ease",
			width: "100%",
			overflowX: "clip",

			[`@media (min-width: 577px)`]: {
				display: "flex",
				flexDirection: "row",
				alignItems: "flex-end",
				flexWrap: "wrap",
			},

			[`@media (max-width: 767px)`]: {
				margin: 0,
			},

			"&::-webkit-scrollbar-thumb": {
				backgroundColor: "#2e343875",
				borderRadius: "0.75rem",
			},
			"&::-webkit-scrollbar-track": {
				backgroundColor: "transparent",
				borderRadius: "0.75rem",
			},
			"&::-webkit-scrollbar": {
				display: "block",
				height: "0.25rem",
			},
		}),
	searchContainer: () =>
		css({
			display: "flex",
			flexDirection: "column",
			alignItems: "flex-start",
			width: "100%",
			flexGrow: 1,
			flexShrink: 1,
			flexBasis: "100%",

			[`@media (min-width: 577px)`]: {
				width: "100%",
				flexBasis: "12.5rem",
				minWidth: "12.5rem",
			},

			[`@media (min-width: 752px) and (max-width: 768px)`]: {
				minWidth: "14.063rem",
			},

			[`@media (min-width: 1200px)`]: {
				width: "100%",
				flexBasis: "15.625rem",
			},

			[`@media (min-width: 767px) and (max-width: 1024px)`]: {
				flexBasis: "15.625rem",
				minWidth: "15.625rem",
			},

			"&:hover": {
				background: "none",
			},

			"> div": {
				width: "100%",
				input: {
					width: "100%",
				},
				'[data-state="default"]': {
					width: "100%",
					border: "1px solid #383731",
					"&:hover": {
						background: "#fff",
						input: {
							background: "#fff",
						},
					},
				},
				svg: {
					minWidth: 16,
				},
			},
		}),
	checkboxContainer: (theme: Theme) =>
		css({
			flexGrow: 1,
			flexShrink: 1,
			flexBasis: "8.75rem",

			display: "block",

			[`@media (max-width: 768px)`]: {
				width: "100%",
				maxWidth: "none",
			},

			"> div": {
				width: "100%",
				input: {
					width: "100%",
					"::placeholder": {
						color: theme.rds.brand.color.secondary[950],
					},
				},
				"> div": {
				"&:last-of-type": {
					"> div": {
						"&:last-of-type": {
					overflowX: "auto"
						}
					}
				},
			}
			},
			[`@media (min-width: 767px) and (max-width: 1024px)`]: {
				"> div": {
					"> div": {
						width: "100%",
						"> div": {
							right: 0,
							"> div": {
								"> div": {
									minWidth: "unset",
								},
							},
						},
					},
				},
			},
			[`@media (max-width: 767px)`]: {
				display: "none",
			},
		}),
	selectContainer: (theme: Theme) =>
		css({
			flexGrow: 1,
			flexShrink: 1,
			flexBasis: "8.75rem",

			display: "block",
			[`@media (min-width: 1024px) and (max-width: 1280px)`]: {
				"> div": {
					"> div": {
						"> div": {
							"> div": {
								"> div": {
									minWidth: "13.75rem",
								},
							},
						},
					},
				},
			},
			[`@media (min-width: 767px) and (max-width: 1024px)`]: {
				"> div": {
					width: "100%",
					"> div": {
						width: "100%",
						"> div": {
							right: 0,
							"> div": {
								"> div": {
									minWidth: "unset",
								},
							},
						},
					},
				},
			},

			[`@media (max-width: 992px)`]: {
				width: "100%",
				maxWidth: "none",
				marginTop: theme?.rds?.spaces.xs2,
			},

			"> div": {
				width: "100%",
				input: {
					width: "100%",
				},
			},
			[`@media (max-width: 767px)`]: {
				display: "none",
			},
		}),
	selectboxContainer: (theme: Theme) =>
		css({
			flexGrow: 1,
			flexShrink: 1,
			flexBasis: "10rem",

			display: "block",

			[`@media (max-width: 992px)`]: {
				width: "100%",
				maxWidth: "none",
				marginTop: theme?.rds?.spaces.xs2,
			},

			[`@media (min-width: 767px) and (max-width: 1024px)`]: {
				"> div": {
					width: "100%",
					"> div": {
						width: "100%",
						"> div": {
							right: 0,
							"> div": {
								"> div": {
									minWidth: "unset",
								},
							},
						},
					},
				},
			},

			"> div": {
				width: "100%",
				input: {
					width: "100%",
				},
				'[data-disabled="false"]': {
					display: "none",
				},
				"& > div > div > div > div > div > div > p": {
					margin: 0,
				},
			},
			[`@media (max-width: 767px)`]: {
				display: "none",
			},
		}),
	moreFiltersContainer: (theme: Theme) =>
		css({
			display: "flex",
			alignItems: "center",
			justifyContent: "center",
			flexShrink: 0,
			flexGrow: 0,
			flexBasis: "auto",
			"& button": {
				border: "1px solid rgb(21.8% 21.5% 19.1%)",
				outline: "none",
				textTransform: "unset",
			},

			[`@media (min-width: 577px)`]: {
				marginTop: theme?.rds?.spaces.xs2,
			},

			[`@media (max-width: 767px)`]: {
				justifyContent: "center",
				marginTop: theme?.rds?.spaces.sm,
				"& button": {
					fontSize: 0,
					gap: 0,
					'&[type="button"]': {
						minWidth: "unset",
					},
				},
			},

			[`@media (min-width: 767px) and (max-width: 1024px)`]: {
				justifyContent: "center",
			},
		}),
	listingPrice: (theme: Theme) =>
		css({
			fontSize: theme.spaces.lg,
			color: "#212121",
			fontWeight: "700",
		}),
	tabsContainer: () =>
		css({
			"> div": {
				display: "flex",
				alignItems: "center",
				"> div": {
					flex: 1,
					padding: "1rem 1.5rem",
					fontSize: "0.875rem",
					lineHeight: "1rem",
					"> div": {
						justifyContent: "center",
					},
				},
				[`@media (min-width: 767px) and (max-width: 1024px)`]: {
					flexWrap: "wrap",
				},
			},
		}),
	priceDrop: () =>
		css({
			display: "flex",
			flexDirection: "column",
			gap: "1rem",
			maxWidth: 220,
			p: {
				margin: 0,
			},
			[`@media (min-width: 767px) and (max-width: 1024px)`]: {
				maxWidth: "100%",
			},
		}),
	subsectionShowAll: () =>
		css({
			display: "flex",
			flexDirection: "column",
			gap: "1rem",
			borderBottom: "1px solid #E3E3DF",
			paddingBottom: "1rem",
		}),
	subsectionSelect: () =>
		css({
			display: "flex",
			justifyContent: "space-between",
			alignItems: "center",
			width: "100%",
			button: {
				fontSize: "0.75rem",
				lineHeight: "1rem",
				fontWeight: 400,
			},
		}),
	tagContainer: () =>
		css({
			display: "flex",
			gap: "0.5rem",
			flexWrap: "wrap",
		}),
	tagContainerOverflow: () =>
		css({
			flexWrap: "nowrap",
			overflow: "auto",
			"&::-webkit-scrollbar": {
				display: "none",
			},
			"> div": {
				padding: "0 1rem",
				minWidth: "unset",
				whiteSpace: "nowrap",
			},
		}),
	sectionContainer: () =>
		css({
			display: "flex",
			flexDirection: "column",
			gap: "1.5rem",
		}),
	sectionTabsWrapper: () =>
		css({
			display: "flex",
			flexDirection: "column",
			gap: "1.5rem",
			paddingBottom: "1.5rem",
			borderBottom: "1px solid #E3E3DF",
		}),
	sectionTabsWrapperEnd: () =>
		css({
			paddingBottom: 0,
			borderBottom: "none",
		}),
	sectionTitle: () =>
		css({
			margin: "0",
			fontSize: "1.125rem",
			lineHeight: "1.75rem",
			fontWeight: "400",
			color: "#000",
		}),
	selectAll: () =>
		css({
			cursor: "pointer",
		}),
	showAll: () =>
		css({
			cursor: "pointer",
		}),
	subsectionTitle: () =>
		css({
			margin: "0 0 1rem 0",
			fontSize: "1rem",
			lineHeight: "1.25rem",
			fontWeight: "500",
			color: "#11100F",
		}),
	subsectionTitleEnd: () =>
		css({
			margin: "0",
		}),
};

export type SearchPropertiesContainerProps = Merge<
	FilterBarProps,
	{
		enableBrokerageFeature?: boolean;
		latestHandoverDateFlag?: boolean;
		onFilterRemoved: (key: string, item: string) => void;
		currentView?: string;
	}
>;

const MIN_PRICE = 0;
const MAX_PRICE = 500000000;
const PRICE_STEP = 500000;

const toggleBodyOverflow = (isOpen: boolean) => {
	if (typeof document !== "undefined") {
		if (isOpen) {
			const scrollY = window.scrollY;
			document.body.style.position = "fixed";
			document.body.style.top = `-${scrollY}px`;
			document.body.style.width = "100%";
			document.body.style.overflow = "hidden";
		} else {
			const scrollY = document.body.style.top;
			document.body.style.position = "";
			document.body.style.top = "";
			document.body.style.width = "";
			document.body.style.overflow = "";
			if (scrollY) {
				window.scrollTo(0, parseInt(scrollY || "0") * -1);
			}
		}
	}
};

export const SearchResultContainerV4 = ({
	paramsState,
	onFilterRemoved,
	latestHandoverDateFlag,
	enableBrokerageFeature,
	currentView,
}: SearchPropertiesContainerProps) => {
	const theme = useTheme();
	const topHeight = useSelector((state: AppState) => state.topHeader.topHeight);
	const authService = useInjection<AuthService>(AuthService);
	const isLoggedIn = authService.useStore().signedIn;
	const notFiltered = _.isEqual(paramsState, DefaultParams);
	const { search: _omit, ...optionState } = paramsState;
	const dispatch = useDispatch();

	const [data, setData] = useState(() => {
		const urlParams = new URLSearchParams(window.location.search);
		const initialData: any = {};
		
		const urlCommunities = urlParams.get("communities");
		if (urlCommunities) {
			initialData.communities = urlCommunities.split(",");
		}
		
		const urlAmenities = urlParams.get("amenities");
		if (urlAmenities) {
			initialData.otherAmenities = urlAmenities.split(",");
		}
		
		const urlFurnishing = urlParams.get("furnishing");
		if (urlFurnishing) {
			initialData.furnishing = [urlFurnishing];
		}
		
		const urlPropertyAge = urlParams.get("propertyAge");
		if (urlPropertyAge) {
			initialData.propertyAge = [urlPropertyAge];
		}
		
		const urlDevelopers = urlParams.get("developers");
		if (urlDevelopers) {
			initialData.developers = urlDevelopers.split(",");
			
			initialData.developer = urlDevelopers.split(",").map(key => ({
				value: key,
				label: key
			}));
		}
		
		const urlPlotArea = urlParams.get("plotArea");
		if (urlPlotArea) {
			initialData["Plot Area"] = [urlPlotArea];
		}
		
		return Object.keys(initialData).length > 0 ? initialData : {};
	});
		const [dataCopy, setDataCopy] = useState({} as any);
		const [searchInputValue, setSearchInputValue] = useState("");
		const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
		const [searchParams, setSearchParams] = useSearchParams({
			selectedTab: "buy",
		});
	
		const urlCurrentView = (searchParams.get("view") as "list" | "map") || "list";
		const selectedTab = searchParams.get("selectedTab") || "buy";
		const setSelectedTab = useCallback((value: string) => {
			setSearchParams({ page: "1", selectedTab: value });
		}, []);
		const [currentTab, setCurrentTab] = useState("Exclusive Right To Sell");
	
		const [price, setPrice] = useState(() => {
			const urlMinPrice = searchParams.get("minPrice");
			const urlMaxPrice = searchParams.get("maxPrice");
			const minPrice = urlMinPrice ? parseInt(urlMinPrice) : MIN_PRICE;
			const maxPrice = urlMaxPrice ? parseInt(urlMaxPrice) : MAX_PRICE;
			
			return {
				maxPrice,
				minPrice,
				priceRange: [minPrice, maxPrice] as [number, number],
			};
		});
	
		const [propertyType, setPropertyType] = useState<string[]>(() => {
			const urlTypes = searchParams.get("propertyType");
			return urlTypes ? urlTypes.split(",") : [];
		});

		const [bedsAndBaths, setBedsAndBaths] = useState(() => {
			const urlBedrooms = searchParams.get("bedrooms");
			const urlBathrooms = searchParams.get("bathrooms");
			return {
				Bedrooms: urlBedrooms ? urlBedrooms.split(",") : [],
				Bathrooms: urlBathrooms ? urlBathrooms.split(",") : [],
			};
		});
	
		const [isDrawerOpen, setDrawerOpen] = useState(false);
	
		const [selectedCount, setSelectedCount] = useState(() => {
		let count = 0;
		const urlParams = new URLSearchParams(window.location.search);
		
		const communities = urlParams.get("communities");
		if (communities) count += communities.split(",").length;
		
		const amenities = urlParams.get("amenities");
		if (amenities) count += amenities.split(",").length;
		
		if (urlParams.get("furnishing")) count += 1;
		if (urlParams.get("propertyAge")) count += 1;
		if (urlParams.get("plotArea")) count += 1;
		
		const developers = urlParams.get("developers");
		if (developers) count += developers.split(",").length;
		
		return count;
	});
	
	const [showBadge, setShowBadge] = useState(() => {
		const urlParams = new URLSearchParams(window.location.search);
		let count = 0;
		
		if (urlParams.get("communities")) count += urlParams.get("communities")!.split(",").length;
		if (urlParams.get("amenities")) count += urlParams.get("amenities")!.split(",").length;
		if (urlParams.get("furnishing")) count += 1;
		if (urlParams.get("propertyAge")) count += 1;
		if (urlParams.get("plotArea")) count += 1;
		if (urlParams.get("developers")) count += urlParams.get("developers")!.split(",").length;
		
		return count > 0;
	});
		const [filterWrapperClicked, setFilterWrapperClicked] = useState(false);
		const filterWrapperRef = useRef<HTMLDivElement>(null);
		const drawerRef = useRef<HTMLDivElement>(null);
		const [filterList, setFilterList] = useState<any[]>([]);
		const [filterCount, setFilterCount] = useState<number>(0);
		const [slider, setSlider] = useState<boolean>(false);
		const [showAllAmenities, setShowAllAmenities] = useState(false);
		const [visibleAmenityCount, setVisibleAmenityCount] = useState(4);
		const { data: PropertyData } = useGetDevelopers("brokerage-properties-dev");
		const propertyData = PropertyData?.data?.BuilderName?.values;
		const DEBOUNCE_DELAY = 300;
	
		const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
			setSearchInputValue(e.target.value);
		};
		const handleClearSearch = () => {
			setSearchInputValue("");
		};
	
		const { t } = useTranslation(undefined, {
			keyPrefix: "homePagev4.brokerageFilterArgs",
		});
		const [isMobile, setIsMobile] = useState(false);
		const [propertyCategory, setPropertyCategory] = useState("residential");
		const [isMoreFiltersOpen, setIsMoreFiltersOpen] = useState(false);
	
		const [selectedCheckboxes, setSelectedCheckboxes] = useState<{
			[key: string]: string[];
		}>(() => {
			const urlCommunities = searchParams.get("communities");
			const urlAmenities = searchParams.get("amenities");
			
			return {
				[t("communities")]: urlCommunities ? urlCommunities.split(",") : [],
				[t("otherAmenities")]: urlAmenities ? urlAmenities.split(",") : [],
			};
		});
	
		const [selectedValues, setSelectedValues] = useState<{ [key: string]: any }>(() => {
			const urlFurnishing = searchParams.get("furnishing");
			const urlPropertyAge = searchParams.get("propertyAge");
			const urlDevelopers = searchParams.get("developers");
			
			const initialValues: { [key: string]: any } = {};
			
			if (urlFurnishing) {
				initialValues["furnishing"] = {
					value: urlFurnishing,
					label: urlFurnishing, 
				};
			}
			
			if (urlPropertyAge) {
				initialValues["propertyAge"] = {
					value: urlPropertyAge,
					label: urlPropertyAge,
				};
			}
			
			if (urlDevelopers) {
				const developerKeys = urlDevelopers.split(",");
				initialValues["developer"] = developerKeys.map(key => ({
					value: key,
					label: key, 
				}));
			}
			
			return initialValues;
		});
	
		const [selectedRadios, setSelectedRadios] = useState<{
			[key: string]: string;
		}>({});
	
		const [textInputValues, setTextInputValues] = useState<{
			[key: string]: string;
		}>(() => {
			const urlPlotArea = searchParams.get("plotArea");
			return {
				"Plot Area": urlPlotArea || "",
			};
		});
	
		const [sliderValue, setSliderValue] = useState<number>(() => {
			const urlPlotArea = searchParams.get("plotArea");
			return urlPlotArea ? parseInt(urlPlotArea) : 0;
		});
	
		const [showSoldProperties, setShowSoldProperties] = useState(false);
		const [resetTrigger, setResetTrigger] = useState(false);
	
		const [selectedDevelopers, setSelectedDevelopers] = useState(() => {
			const urlDevelopers = searchParams.get("developers");
			if (urlDevelopers) {
				return urlDevelopers.split(",").map(key => ({
					value: key,
					label: key, 
				}));
			}
			return [];
		});
	
	const { locale } = useParams();

	const unformatNumber = (str: string) => str.replace(/,/g, "");
	const formatNumber = (num: number) => new Intl.NumberFormat().format(num);
	const initialValue = [0, 500000000];

	useEffect(() => {
		const externalSearch = paramsState.search || "";
		if (externalSearch !== searchInputValue) {
			setSearchInputValue(externalSearch);
		}
		if (externalSearch !== debouncedSearchTerm) {
			setDebouncedSearchTerm(externalSearch);
		}

		if (paramsState.propertyType && JSON.stringify(paramsState.propertyType) !== JSON.stringify(propertyType)) {
			setPropertyType(paramsState.propertyType);
		}

		if (paramsState.bedrooms && JSON.stringify(paramsState.bedrooms) !== JSON.stringify(bedsAndBaths.Bedrooms)) {
			setBedsAndBaths(prev => ({ ...prev, Bedrooms: paramsState.bedrooms }));
		}
		if (paramsState.bathrooms && JSON.stringify(paramsState.bathrooms) !== JSON.stringify(bedsAndBaths.Bathrooms)) {
			setBedsAndBaths(prev => ({ ...prev, Bathrooms: paramsState.bathrooms }));
		}

		if (paramsState.sortByPrice && typeof paramsState.sortByPrice === "object") {
			const { minPrice: newMin, maxPrice: newMax } = paramsState.sortByPrice;
			if (newMin !== price.minPrice || newMax !== price.maxPrice) {
				setPrice({
					minPrice: newMin,
					maxPrice: newMax,
					priceRange: [newMin, newMax],
				});
			}
		}

	}, [paramsState]);
	
	useEffect(() => {
		const params = getSearchParamsState(searchParams);
		dispatch(setSearchParam(params));
	}, [searchParams]);
	
	useEffect(() => {
		const externalSearch =
			paramsState.search === undefined ? "" : paramsState.search;
		if (externalSearch !== searchInputValue) {
			setSearchInputValue(externalSearch);
		}
		if (externalSearch !== debouncedSearchTerm) {
			setDebouncedSearchTerm(externalSearch);
		}
	}, [paramsState.search]);

	const resetPageToFirst = useCallback(() => {
		setSearchParams(
			(prev) => {
				if (prev.get("page") !== "1") {
					const newParams = new URLSearchParams(prev);
					newParams.set("page", "1");
					return newParams;
				}
				return prev;
			},
			{ replace: true },
		);
	}, [setSearchParams]);

	useEffect(() => {
		toggleBodyOverflow(isMoreFiltersOpen);

		return () => {
			if (isMoreFiltersOpen) {
				toggleBodyOverflow(false);
			}
		};
	}, [isMoreFiltersOpen]);

	useEffect(() => {
		return () => {
			toggleBodyOverflow(false);
		};
	}, []);

	const debouncedUpdateSearch = useMemo(
		() =>
			_.debounce((newSearchTerm: string, currentDebouncedTerm: string) => {
				if (newSearchTerm !== currentDebouncedTerm) {
					setDebouncedSearchTerm(newSearchTerm);
					resetPageToFirst();
				}
			}, DEBOUNCE_DELAY),
		[DEBOUNCE_DELAY, setDebouncedSearchTerm, resetPageToFirst],
	);

	useEffect(() => {
		debouncedUpdateSearch(searchInputValue, debouncedSearchTerm);
		return () => {
			debouncedUpdateSearch.cancel();
		};
	}, [searchInputValue, debouncedSearchTerm, debouncedUpdateSearch]);

	useEffect(() => {
		function handleResize() {
			setIsMobile(window.innerWidth <= 768);
		}

		window.addEventListener("resize", handleResize);

		handleResize();

		return () => {
			window.removeEventListener("resize", handleResize);
		};
	}, []);

	useEffect(() => {
		function handleClickOutside(event: MouseEvent) {
			const target = event.target as Node;
			if (
				isMoreFiltersOpen &&
				drawerRef.current &&
				!drawerRef.current.contains(target)
			) {
				setIsMoreFiltersOpen(false);
			}
		}

		document.addEventListener("mousedown", handleClickOutside);
		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, [isMoreFiltersOpen]);

	const listingTypeDropdownArgs = {
		label: t("listingType"),
		placeholder: selectedTab === "buy" ? t("buy") : t("rent"),
		iconSize: 20,
		dropdownOpenByDefault: false,
		showButton: false,
		inputStyles: {
			backgroundColor: "rgba(255, 255, 255, 0.90)",
			borderRadius: "8px",
			border: "1px solid #383731",
		},
		content: (
			<RDSRadioGroup
				value={selectedTab}
				onValueChange={(value) => {
					setSelectedTab(value);
				}}
				name="listing-type-radio-group"
			>
				<RDSRadio value="buy" label={t("buy")} />
				<RDSRadio value="rent" label={t("rent")} />
			</RDSRadioGroup>
		),
	};

	useEffect(() => {
		if (selectedTab === "buy") {
			setCurrentTab("Exclusive Right To Sell");
		} else if (selectedTab === "rent") {
			setCurrentTab("Exclusive Right To Lease");
		} else {
			setCurrentTab("Exclusive Right To Sell");
		}
	}, [selectedTab]);

	const inputArgs = {
		inputType: "search",
		label: t("where"),
		onChange: handleSearchInputChange,
		onClearSearchInput: handleClearSearch,
		placeholder: t("cityCommunity"),
		value: searchInputValue,
	};

	const getDeveloperOptions = (
		propertyData: DeveloperValue[] | undefined,
		t: (key: string) => string,
		locale: string = "en",
	) => {
		const options =
			propertyData?.map((dev) => ({
				value: dev.key,
				label: dev.translations?.[locale] || dev.key,
				type: "multiSelect",
				leadIcon: dev.icon ? (
					<Image src={dev.icon} alt={dev.key} width={24} height={24} />
				) : null,
			})) ?? [];

		return [
			{
				value: "any",
				label: t("any"),
				type: "multiSelect",
			},
			...options,
		];
	};

	const developersOptions = getDeveloperOptions(propertyData, t, locale);

	const propertyTypeMap: Record<string, string> = {
		Villa: t("villa"),
		Townhouse: t("townhouse"),
		Apartment: t("apartment"),
		Duplex: t("duplex"),
	};

	const residentialOptions = [
		{ value: "any", label: t("any") },
		{ value: "Villa", label: t("villa") },
		{ value: "Townhouse", label: t("townhouse") },
		{ value: "Apartment", label: t("apartment") },
		{ value: "Duplex", label: t("duplex") },
	];

	const commercialOptions = [
		{ value: "any", label: t("any") },
		{ value: "Villa", label: t("villa") },
		{ value: "Townhouse", label: t("townhouse") },
		{ value: "Apartment", label: t("apartment") },
		{ value: "Duplex", label: t("duplex") },
	];

	const getCurrentPropertyOptions = () => {
		return propertyCategory === "residential"
			? residentialOptions
			: commercialOptions;
	};

	const handlePropertyTypeSelection = (value: string) => {
		let newPropertyTypes;

		if (value === "any") {
			newPropertyTypes = [];
		} else {
			const currentPropertyTypes = propertyType || [];
			if (currentPropertyTypes.includes(value)) {
				newPropertyTypes = currentPropertyTypes.filter(
					(item) => item !== value,
				);
			} else {
				newPropertyTypes = [
					...currentPropertyTypes.filter((item) => item !== "any"),
					value,
				];
			}
		}

		setPropertyType(newPropertyTypes);
		resetPageToFirst();
	};

	const handlePropertyCategoryChange = (category: string) => {
		setPropertyCategory(category);
		setPropertyType([]);
		resetPageToFirst();
	};

	const handlePropertyTypeReset = () => {
		setPropertyType([]);
		setPropertyCategory("residential");
		resetPageToFirst();
	};

	const propertyTabsArgs = {
		platform: isMobile ? "mobile" : "desktop",
		level: "2",
		defaultValue: propertyCategory,
		activeTabIndex: propertyCategory === "residential" ? 0 : 1,
		size: "sm",
		isParent: false,
		tabContent: [
			{
				label: t("residential"),
				value: "residential",
				isDisabled: false,
				isParent: false,
				size: "sm",
				state: propertyCategory === "residential" ? "active" : "default",
				assetProps: {
					heading: "residential",
				},
				badgeProps: {
					type: "important",
					content: "text",
					label: "badge",
				},
				rdsBadge: false,
				onClick: () => handlePropertyCategoryChange("residential"),
			},
			{
				label: t("commercial"),
				value: "commercial",
				isDisabled: false,
				isParent: false,
				size: "sm",
				state: propertyCategory === "commercial" ? "active" : "inactive",
				assetProps: {
					heading: "commercial",
				},
				badgeProps: {
					type: "important",
					content: "text",
					label: "badge",
				},
				rdsBadge: false,
				onClick: () => handlePropertyCategoryChange("commercial"),
			},
		],
	};

	const selectedLabelValues = (propertyType || []).map((val) => {
		return propertyTypeMap[val] || val;
	});

	const propertyCategoryLabel = t(propertyCategory);

	const dynamicPlaceholder =
		selectedLabelValues.length > 0
			? `${propertyCategoryLabel} (${selectedLabelValues.join(", ")})`
			: propertyCategoryLabel;
	const localizedPropertyTypes = (() => {
		const translatedLabels = (propertyType || []).filter((val)=> val !== "any").map((val) => {
			return propertyTypeMap[val] || val;
		});

		const categoryLabel = t(propertyCategory);

		return translatedLabels.length > 0
			? `${categoryLabel} (${translatedLabels.join(", ")})`
			: categoryLabel;
	})();

	const Filter1Args = {
		label: t("propertyType"),
		disabled: false,
		placeholder: dynamicPlaceholder,
		iconSize: 20,
		dropdownOpenByDefault: false,
		inputStyles: { backgroundColor: "#f3f4f6", borderRadius: "8px" },
		buttonLabel: t("reset"),
		showButton: true,

		selectedValues: {
			PropertyTypes: [localizedPropertyTypes],
		},
		valueDisplayConfig: {
			PropertyTypes: {
				separator: ", ",
			},
		},

		content: (
			<div
				style={{
					display: "flex",
					flexDirection: "column",
					gap: "1.5rem",
				}}
			>
				<div>
					<h4
						style={{
							margin: "0 0 1rem 0",
							fontSize: "1rem",
							lineHeight: "1.25rem",
							fontWeight: "500",
							color: "#000",
						}}
					>
						{t("propertyType")}
					</h4>
					<div css={styles.tabsContainer}>
						<RDSTabGroup {...propertyTabsArgs} />
					</div>
				</div>

				<div>
					<div
						style={{
							display: "flex",
							gap: "0.5rem",
							flexWrap: "wrap",
							maxWidth: "261px",
						}}
					>
						{getCurrentPropertyOptions().map((option) => (
							<RDSTagInteractive
								key={`property-${option.value}`}
								label={option.label}
								size="md"
								state={
									(option.value === "any" && propertyType?.length === 0) ||
									propertyType?.includes(option.value)
										? "active"
										: "default"
								}
								disabled={false}
								onClick={() => handlePropertyTypeSelection(option.value)}
							/>
						))}
					</div>
				</div>
			</div>
		),

		onReset: handlePropertyTypeReset,
		onChange: () => {
			resetPageToFirst();
		},
	};

	const bedroomOptions = [
		{ value: "any", label: t("any") },
		{ value: "1", label: "1" },
		{ value: "2", label: "2" },
		{ value: "3", label: "3" },
		{ value: "4", label: "4" },
		{ value: "5+", label: "5+" },
	];

	const bathroomOptions = [
		{ value: "any", label: t("any") },
		{ value: "1", label: "1" },
		{ value: "2", label: "2" },
		{ value: "3", label: "3" },
		{ value: "4", label: "4" },
		{ value: "5+", label: "5+" },
	];

	const handleBedroomSelection = (value: string) => {
		let newBedrooms: any[];

		if (value === "any") {
			newBedrooms = [];
		} else {
			const currentBedrooms = bedsAndBaths.Bedrooms || [];
			if (currentBedrooms.includes(value)) {
				newBedrooms = currentBedrooms.filter((item) => item !== value);
			} else {
				newBedrooms = [
					...currentBedrooms.filter((item) => item !== "any"),
					value,
				];
			}
		}

		setBedsAndBaths((prev) => ({
			...prev,
			Bedrooms: newBedrooms,
		}));
		resetPageToFirst();
	};

	const handleBathroomSelection = (value: string) => {
		let newBathrooms;

		if (value === "any") {
			newBathrooms = [];
		} else {
			const currentBathrooms = bedsAndBaths.Bathrooms || [];
			if (currentBathrooms.includes(value)) {
				newBathrooms = currentBathrooms.filter((item) => item !== value);
			} else {
				newBathrooms = [
					...currentBathrooms.filter((item) => item !== "any"),
					value,
				];
			}
		}

		setBedsAndBaths((prev) => ({
			...prev,
			Bathrooms: newBathrooms,
		}));
		resetPageToFirst();
	};

	const handleReset = () => {
		setBedsAndBaths({
			Bedrooms: [],
			Bathrooms: [],
		});
		resetPageToFirst();
	};

	const Filter2Args = {
		label: t("bedsAndBaths"),
		disabled: false,
		placeholder: t("addNumbers"),
		iconSize: 20,
		dropdownOpenByDefault: false,
		inputStyles: { backgroundColor: "#f3f4f6", borderRadius: "8px" },
		buttonLabel: t("reset"),
		showButton: true,

		selectedValues: {
			Bedrooms: bedsAndBaths.Bedrooms || [],
			Bathrooms: bedsAndBaths.Bathrooms || [],
		},
		valueDisplayConfig: {
			Bedrooms: {
				suffix: ` ${t("beds")}`,
				separator: ", ",
			},
			Bathrooms: {
				suffix: ` ${t("baths")}`,
				separator: ", ",
			},
		},

		content: (
			<div
				style={{
					display: "flex",
					flexDirection: "column",
					gap: "1rem",
				}}
			>
				<div>
					<h4
						style={{
							margin: "0 0 1rem 0",
							fontSize: "1rem",
							lineHeight: "1.25rem",
							fontWeight: "500",
							color: "#000",
						}}
					>
						{t("bedrooms")}
					</h4>
					<div
						style={{
							display: "flex",
							gap: "0.5rem",
							flexWrap: "wrap",
						}}
					>
						{bedroomOptions.map((option) => (
							<RDSTagInteractive
								key={`bedroom-${option.value}`}
								label={option.label}
								size="md"
								state={
									(option.value === "any" &&
										bedsAndBaths.Bedrooms?.length === 0) ||
									bedsAndBaths.Bedrooms?.includes(option.value)
										? "active"
										: "default"
								}
								disabled={false}
								onClick={() => handleBedroomSelection(option.value)}
							/>
						))}
					</div>
				</div>
				<div>
					<h4
						style={{
							margin: "0 0 1rem 0",
							fontSize: "1rem",
							lineHeight: "1.25rem",
							fontWeight: "500",
							color: "#000",
						}}
					>
						{t("bathrooms")}
					</h4>
					<div
						style={{
							display: "flex",
							gap: "0.5rem",
							flexWrap: "wrap",
						}}
					>
						{bathroomOptions.map((option) => (
							<RDSTagInteractive
								key={`bathroom-${option.value}`}
								label={option.label}
								size="md"
								state={
									(option.value === "any" &&
										bedsAndBaths.Bathrooms?.length === 0) ||
									bedsAndBaths.Bathrooms?.includes(option.value)
										? "active"
										: "default"
								}
								disabled={false}
								onClick={() => handleBathroomSelection(option.value)}
							/>
						))}
					</div>
				</div>
			</div>
		),
		onReset: handleReset,
		onChange: () => {
			resetPageToFirst();
		},
	};

	const renderMinPriceInput = (price, setPrice, resetPageToFirst) => (
		<RDSTextInput
			label={t("minimum")}
			placeholder={t("minimum")}
			leadIcon=""
			value={
				isNaN(Number(price.minPrice)) ? "" : price.minPrice.toLocaleString()
			}
			onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
				const value = parseInt(e.target.value.replace(/,/g, ""));
				if (isNaN(value)) {
					const newState = {
						...price,
						minPrice: 0,
						priceRange: [0, Math.max(0, price.maxPrice)] as [number, number],
					};
					setPrice(newState);
					return;
				}
				if (value > MAX_PRICE || value < MIN_PRICE || isNaN(value)) return;

				const newState = {
					...price,
					minPrice: value,
					priceRange: [value, Math.max(value, price.maxPrice)] as [
						number,
						number,
					],
				};
				setPrice(newState);
				resetPageToFirst();
			}}
		/>
	);

	const renderMaxPriceInput = (
		price,
		setPrice,
		resetPageToFirst: () => void,
	) => (
		<RDSTextInput
			label={t("maximum")}
			placeholder={t("maximum")}
			leadIcon=""
			value={
				isNaN(Number(price.maxPrice)) ? "" : price.maxPrice.toLocaleString()
			}
			onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
				const value = parseInt(e.target.value.replace(/,/g, ""));
				if (value > MAX_PRICE || value < MIN_PRICE || isNaN(value)) return;
				if (isNaN(value)) {
					const newState = {
						...price,
						maxPrice: 0,
						priceRange: [Math.min(0, price.minPrice), 0] as [number, number],
					};
					setPrice(newState);
					return;
				}

				const newState = {
					...price,
					maxPrice: value,
					priceRange: [Math.min(value, price.minPrice), value] as [
						number,
						number,
					],
				};
				setPrice(newState);
				resetPageToFirst();
			}}
		/>
	);

	const Filter3Args = {
		label: t("price"),
		placeholder: t("addRange"),
		iconSize: 20,
		dropdownOpenByDefault: false,
		rtl: theme.direction === "rtl",
		inputStyles: {
			backgroundColor: "#f3f4f6",
			borderRadius: "8px",
			minWidth: "220px",
		},
		buttonLabel: t("reset"),
		showButton: true,

		selectedValues: {
			PriceRange:
				price.minPrice === MIN_PRICE && price.maxPrice === MAX_PRICE
					? []
					: [
							`${" " + price.minPrice.toLocaleString()} - ${" " + price.maxPrice.toLocaleString()}`,
						],
		},
		valueDisplayConfig: {
			PriceRange: {
				separator: "",
			},
		},

		content: (
			<div css={styles.priceDrop}>
				<RDSRangeSlider
					value={price.priceRange}
					step={PRICE_STEP}
					max={MAX_PRICE}
					min={MIN_PRICE}
					showLabels={true}
					showHeader={true}
					label={t("priceRange")}
					showLabel={true}
					showCaption={false}
					isRequired={false}
					infoIcon={false}
					caption="Caption"
					captionIcon={false}
					dotsOnTrack={5}
					onValueChange={(range: [number, number]) => {
						const [minVal, maxVal] = range;
						const newState = {
							minPrice: minVal,
							maxPrice: maxVal,
							priceRange: [minVal, maxVal] as [number, number],
						};
						setPrice(newState);
						resetPageToFirst();
					}}
					currencySymbol=""
				/>
				{theme.direction === "rtl" ? (
					<>
						{renderMaxPriceInput(price, setPrice, resetPageToFirst)}
						{renderMinPriceInput(price, setPrice, resetPageToFirst)}
					</>
				) : (
					<>
						{renderMinPriceInput(price, setPrice, resetPageToFirst)}
						{renderMaxPriceInput(price, setPrice, resetPageToFirst)}
					</>
				)}
			</div>
		),

		onReset: () => {
			const resetState = {
				minPrice: MIN_PRICE,
				maxPrice: MAX_PRICE,
				priceRange: [MIN_PRICE, MAX_PRICE] as [number, number],
			};
			setPrice(resetState);
			resetPageToFirst();
		},

		onChange: useCallback(
			(value: any) => {
				if (
					value.priceRange[0] !== price.priceRange[0] ||
					value.priceRange[1] !== price.priceRange[1]
				) {
					setPrice(value);
				}
				resetPageToFirst();
			},
			[price.priceRange],
		),
	};

	const filterKeyMap: Record<string, string> = {
		[t("availability")]: "availability",
		[t("propertyAge")]: "propertyAge",
		[t("communities")]: "communities",
		[t("otherAmenities")]: "otherAmenities",
		[t("furnishing")]: "furnishing",
		[t("plotArea")]: "Plot Area",
		"Property Age": "propertyAge",
		"عمر العقار": "propertyAge",
		[t("propertyType")]: "propertyType",
		[t("developer")]: "developers",
		developers: "developers",
	};
	const communityValueMap: Record<string, string> = {
		[t("sedra")]: "sedra",
		[t("warefa")]: "warefa",
		[t("alarous")]: "alarous",
	};

	const furnishingValueMap: Record<string, string> = {
		[t("furnished")]: "Furnished",
		[t("semiFurnished")]: "Semi-Furnished",
		[t("unfurnished")]: "Unfurnished",
	};
	const propertyAgeValueMap: Record<string, string> = {
		[t("0-1Year")]: "0-1 Year",
		[t("1-5Years")]: "1-5 Years",
		[t("5-10Years")]: "5-10 Years",
		[t("10+Years")]: "10+ Years",
		"0-1 Year": "0-1 Year",
		"1-5 Years": "1-5 Years",
		"5-10 Years": "5-10 Years",
		"10+ Years": "10+ Years",
	};
	const amenitiesArray = [
		{ value: "any", label: t("any") },
		{ value: "mustHaveAC", label: t("mustHaveAC") },
		{ value: "kitchenInstalled", label: t("kitchenInstalled") },
		{ value: "cabinets", label: t("cabinets") },
		{ value: "schoolNearby", label: t("schoolNearby") },
		{ value: "pool", label: t("pool") },
	];

	const handleCheckboxChange = (label: string, selectedItems: string[]) => {
		setSelectedCheckboxes((prev) => ({
			...prev,
			[label]: selectedItems,
		}));
	};

	const handleRadioChange = (name: string, value: string) => {
		setSelectedRadios((prev) => ({
			...prev,
			[name]: value,
		}));
	};

	const handleSelectChange = (label: string, selectedValue: any) => {
		setSelectedValues((prev) => ({
			...prev,
			[label]: selectedValue,
		}));
	};

	const handleTextInputChange = (key: string, value: string) => {
		setTextInputValues((prev) => ({
			...prev,
			[key]: value,
		}));
	};

	const handleSliderChange = (val: number) => {
		setSliderValue(val);
		handleTextInputChange("Plot Area", val.toString());
	};

	const applyMoreFilters = () => {
		const allSelectedFilters = {
			...selectedCheckboxes,
			...selectedValues,
			...selectedRadios,
			...textInputValues,
			showSoldProperties,
			developers: selectedDevelopers.map((dev) => dev.value),
		};

		const checkboxCount = Object.values(selectedCheckboxes).reduce(
			(acc, items) => acc + items.length,
			0,
		);

		const selectCountWithoutDevelopers = Object.entries(selectedValues).filter(
			([key, value]) =>
				key !== "developer" &&
				value !== null &&
				value !== undefined &&
				value !== "",
		).length;

		const radioCount = Object.values(selectedRadios).filter(Boolean).length;
		const textInputCount = Object.values(textInputValues).filter(
			(value) => value?.trim() !== "",
		).length;
		const switchCount = showSoldProperties ? 1 : 0;
		const developersCount = selectedDevelopers.length;

		const totalSelected =
			checkboxCount +
			selectCountWithoutDevelopers +
			radioCount +
			textInputCount +
			switchCount +
			developersCount;

		setSelectedCount(totalSelected);
		setShowBadge(totalSelected > 0);

		const normalizedValue: Record<string, any> = {};

		Object.entries(allSelectedFilters).forEach(([key, val]) => {
			const normalizedKey = filterKeyMap[key] ?? key;

			if (normalizedKey === "developers" && Array.isArray(val)) {
				normalizedValue[normalizedKey] = val.map((dev) => dev.key);
			} else if (normalizedKey === "communities" && Array.isArray(val)) {
				normalizedValue[normalizedKey] = val.map(
					(v: string) => communityValueMap[v] ?? v,
				);
			} else if (
				normalizedKey === "furnishing" &&
				val &&
				typeof val === "object" &&
				"value" in val
			) {
				normalizedValue[normalizedKey] = [
					furnishingValueMap[val.value] ?? val.value,
				];
			} else if (
				normalizedKey === "propertyAge" &&
				val &&
				typeof val === "object" &&
				"value" in val
			) {
				normalizedValue[normalizedKey] = [
					propertyAgeValueMap[val.value] ?? val.value,
				];
			} else if (
				val &&
				typeof val === "object" &&
				!Array.isArray(val) &&
				"value" in val
			) {
				normalizedValue[normalizedKey] = [val.value];
			} else {
				normalizedValue[normalizedKey] = val;
			}
		});
		setData(normalizedValue);
		setIsMoreFiltersOpen(false);
		resetPageToFirst();
	};

	const handleDeveloperChange = (selectedOptions: any) => {
		const filteredOptions = (selectedOptions || []).filter(
			(option: any) => option.value !== "any",
		);

		const safeDevelopers = filteredOptions.map((option: any) => ({
			value: option.value,
			label: option.label,
		}));

		if (
			safeDevelopers.length === 0 &&
			selectedOptions.some((opt: any) => opt.value === "any")
		) {
			setSelectedDevelopers([]);
			setSelectedValues((prev) => ({ ...prev, developer: [] }));
			setSelectedCount((prevCount) =>
				Math.max(0, prevCount - selectedDevelopers.length),
			);
		} else {
			const previousCount = selectedDevelopers.length;

			setSelectedDevelopers(safeDevelopers);
			setSelectedValues((prev) => ({
				...prev,
				developer: safeDevelopers,
			}));
			setSelectedCount(
				(prevCount) => prevCount - previousCount + safeDevelopers.length,
			);
		}
		if (moreFiltersRef.current) {
			moreFiltersRef.current.scrollTop = moreFiltersRef.current?.scrollHeight;
		}
	};

	const resetAllMoreFilters = () => {
		const emptyState = {};
		setSelectedCheckboxes(emptyState);
		setSelectedValues(emptyState);
		setSelectedRadios(emptyState);
		setTextInputValues(emptyState);
		setSliderValue(0);
		setShowSoldProperties(false);
		setSelectedDevelopers([]);
		setResetTrigger((prev) => !prev);
		setData({});
		setIsMoreFiltersOpen(false);
		setSelectedCount(0);
		setShowBadge(false);
		resetPageToFirst();
	};

	const isFurnishingTagActive = (optionValue: string): boolean => {
		const currentSelection = selectedValues["furnishing"];

		if (optionValue === "any") {
			return !currentSelection;
		}

		if (typeof currentSelection === "string") {
			return currentSelection === optionValue;
		}

		if (typeof currentSelection === "object" && currentSelection?.value) {
			return currentSelection.value === optionValue;
		}

		return false;
	};

	const isPropertyAgeTagActive = (optionValue: string): boolean => {
		const currentSelection = selectedValues["propertyAge"];

		if (optionValue === "any") {
			return !currentSelection;
		}

		if (typeof currentSelection === "string") {
			return currentSelection === optionValue;
		}

		if (typeof currentSelection === "object" && currentSelection?.value) {
			return currentSelection.value === optionValue;
		}

		return false;
	};

	const isAmenityTagActive = (optionValue: string): boolean => {
		const currentSelections = selectedCheckboxes[t("otherAmenities")] || [];

		if (optionValue === "any") {
			return currentSelections.length === 0;
		}

		return currentSelections.includes(optionValue);
	};

	const handleFurnishingTagClick = (option: {
		value: string;
		label: string;
	}) => {
		const currentSelection = selectedValues["furnishing"];
		const currentValue =
			typeof currentSelection === "object"
				? currentSelection?.value
				: currentSelection;

		if (option.value === "any" || currentValue === option.value) {
			handleSelectChange("furnishing", null);
		} else {
			handleSelectChange("furnishing", {
				value: option.value,
				label: option.label,
			});
		}
	};

	const handlePropertyAgeTagClick = (option: {
		value: string;
		label: string;
	}) => {
		const currentSelection = selectedValues["propertyAge"];
		const currentValue =
			typeof currentSelection === "object"
				? currentSelection?.value
				: currentSelection;

		if (option.value === "any" || currentValue === option.value) {
			handleSelectChange("propertyAge", null);
		} else {
			handleSelectChange("propertyAge", {
				value: option.value,
				label: option.label,
			});
		}
	};

	const handleAmenityTagClick = (option: { value: string; label: string }) => {
		const currentSelections = selectedCheckboxes[t("otherAmenities")] || [];

		if (option.value === "any") {
			handleCheckboxChange(t("otherAmenities"), []);
		} else {
			if (currentSelections.includes(option.value)) {
				const newSelections = currentSelections.filter(
					(item) => item !== option.value,
				);
				handleCheckboxChange(t("otherAmenities"), newSelections);
			} else {
				const newSelections = [...currentSelections, option.value];
				handleCheckboxChange(t("otherAmenities"), newSelections);
			}
		}
	};

	const handleSelectAllAmenities = () => {
		const newSelections = amenitiesArray
			.filter((option) => option.value !== "any")
			.map((option) => option.value);
		handleCheckboxChange(t("otherAmenities"), newSelections);
	};

	const handleShowAllAmenities = () => {
		setShowAllAmenities(!showAllAmenities);
		setVisibleAmenityCount(showAllAmenities ? 4 : amenitiesArray.length);
	};

	const handleShowLessAmenities = () => {
		setShowAllAmenities(!showAllAmenities);
		setVisibleAmenityCount(showAllAmenities ? 4 : amenitiesArray.length);
	};

	const moreFiltersRef = useRef<HTMLDivElement>(null);

	const moreFiltersSideDrawerArgs = {
		isOpen: isMoreFiltersOpen,
		showOverlay: true,
		header: true,
		footer: true,
		showDescription: false,
		rdsHeader: {
			leadIcon: false,
			trailIcon: true,
			label: t("moreFilters"),
			type: "left",
			hasAsset: false,
			leadIconProps: {},
			trailIconProps: {
				onClick: () => setIsMoreFiltersOpen(false),
			},
			assetProps: {},
		},
		content: (
			<div ref={moreFiltersRef} css={styles.sectionContainer()}>
				<div css={styles.sectionTabsWrapper()}>
					<h3 css={styles.sectionTitle()}>{t("propertyFeatures")}</h3>
					<div>
						<h4 css={styles.subsectionTitle()}>{t("furnishing")}</h4>
						<div css={styles.tagContainer()}>
							{[
								{ value: "any", label: t("any") },
								{ value: t("furnished"), label: t("furnished") },
								{ value: t("semiFurnished"), label: t("semiFurnished") },
								{ value: t("unfurnished"), label: t("unfurnished") },
							].map((option) => (
								<RDSTagInteractive
									key={`furnishing-${option.value}`}
									label={option.label}
									size="md"
									state={
										isFurnishingTagActive(option.value) ? "active" : "default"
									}
									disabled={false}
									onClick={() => handleFurnishingTagClick(option)}
								/>
							))}
						</div>
					</div>
				</div>

				<div css={styles.subsectionShowAll}>
					<div css={styles.subsectionSelect}>
						<h4 css={styles.sectionTitle()}>{t("otherAmenities")}</h4>
						<RDSLinkButton
							css={styles.selectAll}
							text={t("selectAll")}
							onClick={handleSelectAllAmenities}
						/>
					</div>
					<div css={styles.tagContainer()}>
						{amenitiesArray.slice(0, visibleAmenityCount).map((option) => (
							<RDSTagInteractive
								key={`amenity-${option.value}`}
								label={option.label}
								size="md"
								state={isAmenityTagActive(option.value) ? "active" : "default"}
								disabled={false}
								onClick={() => handleAmenityTagClick(option)}
							/>
						))}
					</div>
					<div>
						<RDSLinkButton
							css={styles.showAll}
							text={
								showAllAmenities
									? t("showLess")
									: t("showAll") + " (" + amenitiesArray.length + ")"
							}
							onClick={
								!showAllAmenities
									? handleShowAllAmenities
									: handleShowLessAmenities
							}
						/>
					</div>
				</div>

				<div
					css={[styles.sectionTabsWrapper(), styles.sectionTabsWrapperEnd()]}
				>
					<h3 css={styles.sectionTitle()}>{t("additionalDetails")}</h3>

					<div css={styles.sectionTabsWrapper()}>
						<h4 css={[styles.subsectionTitle(), styles.subsectionTitleEnd()]}>
							{t("plotArea")}
						</h4>

						<div>
							<RDSTextInput
								key="Plot Area"
								placeholder={t("selectRange")}
								value={formatNumber(
									Number(textInputValues["Plot Area"] || "0"),
								)}
								onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
									const val = unformatNumber(e.target.value);
									if (/^\d*$/.test(val)) {
										const num = parseInt(val || "0", 10);
										setSliderValue(num);
										handleTextInputChange("Plot Area", val);
									}
								}}
								rtl={theme?.rds?.direction === "rtl"}
							/>
						</div>

						<RDSSlider
							value={sliderValue}
							step={10}
							max={100000}
							unit={t("sqm")}
							rtl={theme?.rds?.direction === "rtl"}
							onValueChange={handleSliderChange}
						/>
					</div>

					<div
						css={[styles.sectionTabsWrapper(), styles.sectionTabsWrapperEnd()]}
					>
						<h4 css={[styles.subsectionTitle(), styles.subsectionTitleEnd()]}>
							{t("propertyAge")}
						</h4>
						<div css={styles.tagContainer()}>
							{[
								{ value: "any", label: t("any") },
								{ value: "0-1 Year", label: `0-1 ${t("years")}` },
								{ value: "1-5 Years", label: `1-5 ${t("years")}` },
								{ value: "5-10 Years", label: `5-10 ${t("years")}` },
								{ value: "10+ Years", label: `10+ ${t("years")}` },
							].map((option) => (
								<RDSTagInteractive
									key={`age-${option.value}`}
									label={option.label}
									size="md"
									state={
										isPropertyAgeTagActive(option.value) ? "active" : "default"
									}
									disabled={false}
									onClick={() => handlePropertyAgeTagClick(option)}
								/>
							))}
						</div>
					</div>

					<RDSSelect
						label={t("developer")}
						placeholder={t("selectDeveloper")}
						options={developersOptions}
						value={selectedValues["developer"] || []}
						onChange={handleDeveloperChange}
						isClearable={true}
						isSearchable={true}
						isMulti={true}
						closeMenuOnSelect={false}
						hideSelectedOptions={false}
						menuPosition="absolute"
						menuShouldScrollIntoView={true}
						menuPlacement="top"
					/>
				</div>
			</div>
		),
		buttonsGroup: {
			buttons: [
				<RDSButton
					variant="primary"
					text={t("apply")}
					onClick={applyMoreFilters}
				/>,
				<RDSButton
					variant="tertiary"
					text={t("resetAll")}
					onClick={resetAllMoreFilters}
				/>,
			],
			direction: "vertical",
		},
		onClose: () => setIsMoreFiltersOpen(false),
	};

	useEffect(() => {
		if (selectedCount == 0) {
			setShowBadge(false);
		}
	}, [selectedCount]);

	const handleRemoveFilter = (key: string, value: string) => {
		setFilterCount((prev) => prev - 1);

		if (filterList.length === 0) return;

		const filterItem = { ...filterList[0] };
		let needsUpdate = false;
		let shouldResetCompletely = false;

		if (key == "Plot Area") {
			setSlider(true);
		}

		if (Array.isArray(filterItem[key])) {
			filterItem[key] = filterItem[key].filter((item) => item !== value);
			if (filterItem[key].length === 0) {
				delete filterItem[key];
			}
			needsUpdate = true;
			setSelectedCount((prev) => Math.max(0, prev - 1));
			if (selectedCount <= 1) {
				setShowBadge(false);
			}
		} else if (
			key === "price" ||
			key === "priceRange" ||
			key === "minPrice" ||
			key === "maxPrice"
		) {
			filterItem.minPrice = MIN_PRICE;
			filterItem.maxPrice = MAX_PRICE;
			filterItem.priceRange = [MIN_PRICE, MAX_PRICE];
			delete filterItem.priceRange;
			needsUpdate = true;
		} else if (
			typeof filterItem[key] === "object" &&
			filterItem[key] !== null
		) {
			if (filterItem[key].value === value || filterItem[key].label === value) {
				delete filterItem[key];
				needsUpdate = true;
			}
		} else if (filterItem[key] === value) {
			delete filterItem[key];
			needsUpdate = true;
		}

		const remainingFilters = Object.keys(filterItem).filter(
			(k) => !k.startsWith("__"),
		);
		if (remainingFilters.length === 0) {
			shouldResetCompletely = true;
		}

		if (needsUpdate) {
			if (shouldResetCompletely) {
				const emptyState = {
					__resetClicked: true,
				};

				setFilterList([emptyState]);
				setData({});
				setDataCopy({});

				setPropertyType([]);
				setBedsAndBaths({
					Bedrooms: [],
					Bathrooms: [],
				});
				setPrice({
					minPrice: MIN_PRICE,
					maxPrice: MAX_PRICE,
					priceRange: [MIN_PRICE, MAX_PRICE],
				});

				setSelectedCount(0);
				setShowBadge(false);

				if (FilterMobileArgs.onChange) {
					FilterMobileArgs.onChange(emptyState);
				}
			} else {
				filterItem.__tagRemoved = true;
				filterItem.__applyClicked = true;

				setFilterList([filterItem]);

				setData((prevData: any) => {
					const newData = { ...prevData };

					if (key in newData) {
						if (Array.isArray(newData[key])) {
							newData[key] = newData[key].filter((item) => item !== value);
							if (newData[key].length === 0) {
								delete newData[key];
							}
						} else {
							delete newData[key];
						}
					}

					return newData;
				});

				if (key === "propertyType" || key === "Property Type") {
					setPropertyType(filterItem[key] || []);
				} else if (key === "Bedrooms") {
					setBedsAndBaths((prev) => ({
						...prev,
						Bedrooms: filterItem[key] || [],
					}));
				} else if (key === "Bathrooms") {
					setBedsAndBaths((prev) => ({
						...prev,
						Bathrooms: filterItem[key] || [],
					}));
				} else if (
					key === "price" ||
					key === "priceRange" ||
					key === "minPrice" ||
					key === "maxPrice"
				) {
					setPrice({
						minPrice: MIN_PRICE,
						maxPrice: MAX_PRICE,
						priceRange: [MIN_PRICE, MAX_PRICE],
					});
				}

				if (FilterMobileArgs.onChange) {
					FilterMobileArgs.onChange(filterItem);
				}
			}
			resetPageToFirst();
		}
	};
	const handleFilterRemoval = (key: string, value: string) => {
		if (filterList.length === 0) return;

		const filterItem = { ...filterList[0] };
		let needsUpdate = false;
		let shouldResetCompletely = false;

		if (Array.isArray(filterItem[key])) {
			filterItem[key] = filterItem[key].filter((item) => item !== value);
			if (filterItem[key].length === 0) {
				delete filterItem[key];
			}
			needsUpdate = true;
		} else if (key === "price" || key === "priceRange") {
			filterItem.minPrice = initialValue[0];
			filterItem.maxPrice = initialValue[1];
			filterItem.priceRange = initialValue;
			needsUpdate = true;
		} else if (
			typeof filterItem[key] === "object" &&
			filterItem[key] !== null
		) {
			if (filterItem[key].value === value || filterItem[key].label === value) {
				delete filterItem[key];
				setSelectedCount((prev) => Math.max(0, prev - 1));
				needsUpdate = true;
			}
		} else if (filterItem[key] === value) {
			delete filterItem[key];
			setSelectedCount((prev) => Math.max(0, prev - 1));
			needsUpdate = true;
		}

		const remainingFilters = Object.keys(filterItem).filter(
			(k) => !k.startsWith("__"),
		);
		if (remainingFilters.length === 0) {
			shouldResetCompletely = true;
		}

		if (needsUpdate) {
			if (shouldResetCompletely) {
				const emptyState = {
					__resetClicked: true,
				};

				setFilterList([emptyState]);

				handleRemoveFilter(key, value);
			} else {
				setFilterList([filterItem]);

				handleRemoveFilter(key, value);
			}
			resetPageToFirst();
		}
	};
	const updateRelatedStates = (filters: any) => {
		if (filters[t("propertyType")] || filters["Property Type"]) {
			setPropertyType(
				filters[t("propertyType")] || filters["Property Type"] || [],
			);
		} else if (filters.__resetClicked || filters.__tagRemoved) {
			setPropertyType([]);
		}

		const updatedBedsAndBaths = { ...bedsAndBaths };

		if (filters["Bedrooms"] !== undefined) {
			updatedBedsAndBaths.Bedrooms = filters["Bedrooms"] || [];
		}
		if (filters["Bathrooms"] !== undefined) {
			updatedBedsAndBaths.Bathrooms = filters["Bathrooms"] || [];
		}

		if (filters[t("bedrooms")] !== undefined) {
			updatedBedsAndBaths.Bedrooms = filters[t("bedrooms")] || [];
		}
		if (filters[t("bathrooms")] !== undefined) {
			updatedBedsAndBaths.Bathrooms = filters[t("bathrooms")] || [];
		}

		if (
			(filters.__resetClicked || filters.__tagRemoved) &&
			!filters["Bedrooms"] &&
			!filters["Bathrooms"] &&
			!filters[t("bedrooms")] &&
			!filters[t("bathrooms")]
		) {
			updatedBedsAndBaths.Bedrooms = [];
			updatedBedsAndBaths.Bathrooms = [];
		}

		setBedsAndBaths(updatedBedsAndBaths);

		if (
			(filters.minPrice !== undefined && filters.maxPrice !== undefined) ||
			filters.priceRange
		) {
			setPrice({
				minPrice: filters.minPrice || MIN_PRICE,
				maxPrice: filters.maxPrice || MAX_PRICE,
				priceRange: filters.priceRange || [
					filters.minPrice || MIN_PRICE,
					filters.maxPrice || MAX_PRICE,
				],
			});
		} else if (filters.__resetClicked || filters.__tagRemoved) {
			setPrice({
				minPrice: MIN_PRICE,
				maxPrice: MAX_PRICE,
				priceRange: [MIN_PRICE, MAX_PRICE],
			});
		}
		resetPageToFirst();
	};

	const FilterMobileArgs = {
		headerLabel: t("filters"),
		filterList,
		setFilterList,
		handleRemoveFilter,
		filterCount,
		isDrawerOpen,
		selectedCount,
		setSelectedCount,
		setDrawerOpen,
		showBadge,
		setShowBadge,
		currencySymbol: "",
		propertyTypeLabel: t("propertyType"),
		propertyTypeOptions: [
			t("villa"),
			t("townhouse"),
			t("apartment"),
			t("duplex"),
		],
		slider,
		setSlider,
		selectedTab,
		setSelectedTab,
		developersOptions,

		bedroomLabel: t("bedrooms"),
		bedroomOptions: ["1", "2", "3", "4", "5+"],

		bathroomLabel: t("bathrooms"),
		bathroomOptions: ["1", "2", "3", "4", "5+"],

		RangeSliderProps: [
			{
				value: price.priceRange,
				step: PRICE_STEP,
				max: MAX_PRICE,
				min: MIN_PRICE,
				showLabels: true,
				showHeader: true,
				label: t("priceRange"),
				showLabel: true,
				showCaption: false,
				isRequired: false,
				infoIcon: false,
				caption: "Caption",
				captionIcon: false,
				dotsOnTrack: 5,
				rtl: theme?.rds?.direction === "rtl",
				currencySymbol: "",
			},
		],
		checkboxGroupProps: [
			{
				label: t("communities"),
				checkboxLabels: [t("sedra"), t("warefa"), t("alarous")],
			},
			{
				label: t("otherAmenities"),
				checkboxLabels: [
					t("mustHaveAC"),
					t("kitchenInstalled"),
					t("cabinets"),
					t("schoolNearby"),
					t("pool"),
				],
			},
		],
		RDSSelectProps: [
			{
				label: "Furnishing",
				placeholder: t("selectFurnishingStatus"),
				options: [
					{ value: "Furnished", label: t("furnished") },
					{ value: "Semi-furnished", label: t("semiFurnished") },
					{ value: "Unfurnished", label: t("unfurnished") },
				],
			},
			{
				label: "Property Age",
				placeholder: t("selectPropertyAge"),
				options: [
					{ value: "0-1 Year", label: "0-1 Year" },
					{ value: "1-5 Years", label: "1-5 Years" },
					{ value: "5-10 Years", label: "5-10 Years" },
					{ value: "10+ Years", label: "10+ Years" },
				],
			},
		],
		radioGroupProps: [
			{
				label: t("availability"),
				options: [
					{ value: t("showAllProperties"), label: t("showAllProperties") },
					{
						value: t("onlyShowAvailableProperties"),
						label: t("onlyShowAvailableProperties"),
					},
				],
				name: t("availability"),
			},
		],
		textInputProps: [
			{
				key: "Plot Area",
				label: t("plotArea"),
				placeholder: t("selectRange"),
			},
		],
		sliderProps: {
			value: 0,
			step: 10,
			max: 100000,
			unit: t("sqm"),
		},
		onChange: (value: any) => {
			const normalizedValue: Record<string, any> = {};

			Object.entries(value).forEach(([key, val]) => {
				const normalizedKey = filterKeyMap[key] ?? key;

				if (normalizedKey === "communities" && Array.isArray(val)) {
					normalizedValue[normalizedKey] = val.map((v: any) => {
						const label = typeof v === "string" ? v : v.value;
						const foundKey = Object.keys(communityValueMap).find(
							(k) => k.toLowerCase() === label.toLowerCase(),
						);
						return foundKey ? communityValueMap[foundKey] : label;
					});
					return;
				}

				if (normalizedKey === "developers" && Array.isArray(val)) {
					normalizedValue[normalizedKey] = val.map((v: any) =>
						typeof v === "object" && v.value ? v.value : v,
					);
					return;
				}

				if (normalizedKey === "propertyType") {
					let raw = val;

					if (Array.isArray(val)) {
						raw = val.map((v) => {
							const label = typeof v === "object" && v?.value ? v.value : v;
							return propertyTypeMap[label] ?? label;
						});
					} else if (val && typeof val === "object" && "value" in val) {
						raw = [propertyTypeMap[val.value] ?? val.value];
					} else {
						raw = [propertyTypeMap[val] ?? val];
					}

					normalizedValue[normalizedKey] = raw;
					return;
				}
				if (val && typeof val === "object" && "value" in val) {
					normalizedValue[normalizedKey] = [val.value];
				} else {
					normalizedValue[normalizedKey] = val;
				}
			});

			normalizedValue._applyClicked = true;

			setData(normalizedValue);
			updateRelatedStates(normalizedValue);
			resetPageToFirst();
		},
		rtl: theme?.rds?.direction === "rtl",
	};

	const actualParamsState = useMemo((): FilterUnitsArg => {
		const ANY_ARRAY = ["any"];
		const priceValue =
			price.maxPrice !== MAX_PRICE || price.minPrice !== MIN_PRICE
				? price
				: "any";
		const getValueOrAny = (arr?: any[]) => (arr?.length ? [...arr] : ANY_ARRAY);
		const getPlotAreaValue = (value?: any) => {
			if (!value) return ANY_ARRAY;
			if (Array.isArray(value)) {
				return value.length ? [...value] : ANY_ARRAY;
			}
			if (typeof value === "string" && value.trim() !== "") {
				return [value];
			}
			return ANY_ARRAY;
		};
		const getDeveloperValues = (arr?: any[]) => {
			if (!arr || !Array.isArray(arr) || arr.length === 0) return ["any"];
			return arr.map((d) => d.value).filter(Boolean);
		};
		const body: FilterUnitsArg = {
			bathrooms: getValueOrAny(bedsAndBaths?.["Bathrooms"]),
			bedrooms: getValueOrAny(bedsAndBaths?.["Bedrooms"]),
			communities: getValueOrAny(data?.["communities"]),
			search: debouncedSearchTerm || "",
			sortByPrice: priceValue,
			propertyType: getValueOrAny(propertyType),
			plotArea: getPlotAreaValue(data?.["Plot Area"]),
			builderName: getDeveloperValues(data?.["developer"]),
			otherAmenities: getValueOrAny(data?.["Other Amenities"]),
			furnishing: getValueOrAny(data?.["furnishing"]),
			listingType: currentTab || ANY_ARRAY,
			standardStatus: data["Availability"] ? "Active" : ANY_ARRAY,
			availability: data["availability"] ?? undefined,
			buildingAge: getValueOrAny(
				data?.["propertyAge"] || dataCopy?.["propertyAge"],
			),
			unitTypes: [],
		};

		if (isMobile) {
			body.bathrooms = getValueOrAny(
				data?.["Bathrooms"] ||
					dataCopy?.["Bathrooms"] ||
					bedsAndBaths?.["Bathrooms"],
			);
			body.bedrooms = getValueOrAny(
				data?.["Bedrooms"] ||
					dataCopy?.["Bedrooms"] ||
					bedsAndBaths?.["Bedrooms"],
			);
			body.communities = getValueOrAny(
				data?.["Communities"] ||
					data?.["communities"] ||
					dataCopy?.["Communities"] ||
					dataCopy?.["communities"],
			);
			body.builderName = getDeveloperValues(
				data?.["developer"] || dataCopy?.["developer"],
			);
			body.sortByPrice =
				data?.minPrice !== undefined && data?.maxPrice !== undefined
					? {
							minPrice: data.minPrice,
							maxPrice: data.maxPrice,
							priceRange: data.priceRange,
						}
					: dataCopy?.minPrice !== undefined && dataCopy?.maxPrice !== undefined
						? {
								minPrice: dataCopy.minPrice,
								maxPrice: dataCopy.maxPrice,
								priceRange: dataCopy.priceRange,
							}
						: price.maxPrice !== MAX_PRICE || price.minPrice !== MIN_PRICE
							? price
							: "any";
			body.propertyType = getValueOrAny(
				data?.["propertyType"] || dataCopy?.["propertyType"] || propertyType,
			);
			body.plotArea = getPlotAreaValue(
				data?.["Plot Area"] || dataCopy?.["Plot Area"],
			);
			body.builderName = getValueOrAny(
				data?.["developers"] || dataCopy?.["developers"],
			);
			body.otherAmenities = getValueOrAny(
				data?.["Other Amenities"] || dataCopy?.["Other Amenities"],
			);
			body.furnishing = getValueOrAny(
				data?.["furnishing"] || dataCopy?.["furnishing"],
			);
			body.buildingAge = getValueOrAny(
				data?.["propertyAge"] || dataCopy?.["propertyAge"],
			);
			body.availability =
				data?.["availability"] ?? dataCopy?.["availability"] ?? undefined;
			body.listingType = currentTab || ["any"];
		}
		return body;
	}, [
		bedsAndBaths,
		data,
		debouncedSearchTerm,
		price,
		propertyType,
		currentTab,
		isMobile,
		dataCopy,
	]);

	const isRebrandedFlag = import.meta.env.VITE_FF_REBRANDED === "TRUE";
	const isTopHeight =
		isLoggedIn && topHeight && topHeight !== "55px" ? topHeight : 0;

	const FilterSideDrawer = () => {
		if (!isMoreFiltersOpen) return null;

		return createPortal(
			<>
				<div ref={drawerRef}>
					<RDSSideDrawer {...moreFiltersSideDrawerArgs} />
				</div>
			</>,
			document.body,
		);
	};

	return (
		<div>
			<div css={styles.containerWrapper(theme, isTopHeight)}>
				{FilterSideDrawer()}
				<EmotionThemeProvider
					theme={getThemeBare({
						direction: theme.direction,
						name: "light",
					})}
				>
					<div css={styles.imageContainer(theme, urlCurrentView)}>
						<div css={styles.filter}>
							<div
								ref={filterWrapperRef}
								css={styles.filterWrapper()}
								onClick={() => setFilterWrapperClicked(true)}
							>
								<div css={styles.filterContainer(theme)}>
									<div css={styles.searchContainer()}>
										<RDSSearchInput {...inputArgs} />
									</div>
									<div css={styles.checkboxContainer(theme)}>
										<RDSSelectSlotDropdown {...listingTypeDropdownArgs} />
									</div>
									<div css={styles.checkboxContainer(theme)}>
										<RDSSelectSlotDropdown {...Filter1Args} />
									</div>
									<div css={styles.selectContainer(theme)}>
										<RDSSelectSlotDropdown {...Filter2Args} />
									</div>
									<div css={styles.selectboxContainer(theme)}>
										<RDSSelectSlotDropdown {...Filter3Args} />
									</div>
									<div css={styles.moreFiltersContainer(theme)}>
										{!isMobile ? (
											<div css={styles.buttonWrapper}>
												<RDSButton
													leadIcon={<FilterIcon />}
													variant="secondary"
													text={t("moreFilters")}
													onClick={() => setIsMoreFiltersOpen(true)}
													css={styles.moreFiltersButton}
												/>
												<div css={styles.badge}>
													{showBadge && (
														<RDSBadge
															appearance="danger"
															content="numerical"
															label={`${selectedCount}`}
															leadIcon={null}
															trailIcon={null}
														/>
													)}
												</div>
											</div>
										) : (
											<>
												<div css={styles.buttonWrapper}>
													<RDSIconButton
														icon={<FilterIcon />}
														size="sm"
														variant="primary"
														css={styles.iconButton}
														onClick={() => setDrawerOpen(true)}
													/>
													<div css={styles.badge}>
														{showBadge && (
															<RDSBadge
																appearance="danger"
																content="numerical"
																label={`${selectedCount}`}
																leadIcon={null}
																trailIcon={null}
															/>
														)}
													</div>
												</div>
												<FilterDropdownMobile {...FilterMobileArgs} />
											</>
										)}
									</div>
								</div>
								{isMobile && (
									<div css={styles.badgeTag}>
										{filterList.map((filterItem, i) =>
											Object.entries(filterItem).map(
												([key, value]: [string, any]) => {
													if (
														key === "__applyClicked" ||
														key === "__resetClicked" ||
														key === "developers"
													)
														return null;

													if (key === "developer" && Array.isArray(value)) {
														return value.map((devObj) => {
															const keyValue =
																typeof devObj === "object"
																	? devObj.value
																	: devObj;
															const displayLabel =
																typeof devObj === "object"
																	? devObj.label
																	: devObj;

															return (
																<RDSTagInteractive
																	key={`developer-${keyValue}`}
																	dismissible={true}
																	label={displayLabel}
																	disabled={false}
																	size="md"
																	onDismiss={() =>
																		handleRemoveFilter("developers", keyValue)
																	}
																/>
															);
														});
													}

													if (key === "Property Type") {
														const keyValue = residentialOptions.find(
															(item) => item.value === value[0],
														);
														return (
															<RDSTagInteractive
																key={`developer-${keyValue}`}
																dismissible={true}
																label={keyValue?.label}
																disabled={false}
																size="md"
																onDismiss={() =>
																	handleRemoveFilter(
																		"Property Type",
																		keyValue?.value,
																	)
																}
															/>
														);
													}

													if (key === "Plot Area") {
														if (Array.isArray(value)) {
															return value.map((val, j) => (
																<RDSTagInteractive
																	key={`${i}-${val}-${j}`}
																	dismissible={true}
																	label={`${val}  ${t("sqm")}`}
																	disabled={false}
																	size="md"
																	onDismiss={() => handleRemoveFilter(key, val)}
																/>
															));
														} else if (
															typeof value === "string" ||
															typeof value === "number"
														) {
															return (
																<RDSTagInteractive
																	key={`${i}-${key}`}
																	dismissible={true}
																	label={`${value} ${t("sqm")}`}
																	disabled={false}
																	size="md"
																	onDismiss={() =>
																		handleRemoveFilter(key, value)
																	}
																/>
															);
														}
														return null;
													}

													if (
														(key === "minPrice" || key === "maxPrice") &&
														filterItem.minPrice !== undefined &&
														filterItem.maxPrice !== undefined
													) {
														if (key === "minPrice") {
															if (
																filterItem.minPrice === initialValue[0] &&
																filterItem.maxPrice === initialValue[1]
															) {
																return null;
															}

															return (
																<RDSTagInteractive
																	key={`${i}-price-range`}
																	dismissible={true}
																	label={`${formatMoney(Number(filterItem.minPrice), { numberOfDigits: 0 })} - ${formatMoney(Number(filterItem.maxPrice), { numberOfDigits: 0 })}`}
																	disabled={false}
																	size="md"
																	onDismiss={() =>
																		handleRemoveFilter("price", "range")
																	}
																/>
															);
														}
														return null;
													}

													if (key === "priceRange") {
														return null;
													}

													if (Array.isArray(value)) {
														return value.map((val, j) => {
															const label =
																key === t("bedrooms")
																	? `${val} ${t("beds")}`
																	: key === t("bathrooms")
																		? `${val} ${t("baths")}`
																		: val;

															return (
																<RDSTagInteractive
																	key={`${i}-${val}-${j}`}
																	dismissible={true}
																	label={label}
																	disabled={false}
																	size="md"
																	onDismiss={() => handleRemoveFilter(key, val)}
																/>
															);
														});
													}

													if (
														typeof value === "object" &&
														value !== null &&
														!Array.isArray(value)
													) {
														if (value.label) {
															return (
																<RDSTagInteractive
																	key={`${i}-${key}`}
																	dismissible={true}
																	label={value.label}
																	disabled={false}
																	size="md"
																	onDismiss={() =>
																		handleFilterRemoval(
																			key,
																			value.value || value.label,
																		)
																	}
																/>
															);
														}
														return null;
													}

													if (
														typeof value === "string" ||
														typeof value === "number"
													) {
														return (
															<RDSTagInteractive
																key={`${i}-${key}`}
																dismissible={true}
																label={`${value}`}
																disabled={false}
																size="md"
																onDismiss={() => handleRemoveFilter(key, value)}
															/>
														);
													}

													return null;
												},
											),
										)}
									</div>
								)}
							</div>
						</div>
					</div>
				</EmotionThemeProvider>
			</div>
			<div css={[sharedStyles.root(theme, currentView)]}>
				<PropertiesResultV4
					paramsState={actualParamsState}
					latestHandoverDateFlag={latestHandoverDateFlag}
					enableBrokerageFeature={enableBrokerageFeature}
				/>
				{!isRebrandedFlag && <RoshnPattern />}
			</div>
		</div>
	);
};
