import * as Collapsible from "@/components/collapsible";
import { Divider } from "@/components/divider";
import { Typography } from "@/components/typography";
import { Classes } from "@/features/property-discorvery";
import {
	LeadStatus,
	Note,
	SALES_METHOD,
	SalesMethodMap,
	useFetchCustomer,
	useIsCustomerListFetching,
	useIsLeadAssignedToAdvisor,
	useSaProject,
	useUpdateLead,
} from "@/services/sales-advisor";
import {
	getPropertyPreferrences,
	toTitleCase,
} from "@/services/sales-advisor/helpers";
import { useStatusErrors } from "@/services/sales-advisor/hooks/use-status-errors";
import { AppTheme, desktopMQ } from "@/theme";
import { css, useTheme } from "@emotion/react";
import * as React from "react";
import { useTranslation } from "react-i18next";
import Skeleton from "react-loading-skeleton";
import { ErrorBanner } from "../notification-hub/error-banner";
import * as InfoCard from "./card";
import { Dialog } from "./dialog";
import { DiscardContent } from "./discard-content";
import { EditConfirmationContent } from "./edit-confirm-content";
import { EditContent, EditFormValues } from "./edit-content";
import { LeadNotesForm } from "./lead-note-form";
import { LeadNotes } from "./lead-notes";
import { OwnershipContent } from "./ownership-content";
import { RDSButton } from "@roshn/ui-kit";
import { useSAFeatureFlagApi, SAFeatureFlags, useFeatureFlag } from "@/features/feature-flags";

const styles = {
	actionButton: (theme: AppTheme) =>
		css({
			...theme.typography.captionS,
			height: theme.spaces.xl,
		}),
	actionButtonWrapper: () =>
		css({
			display: "flex",
			gap: "1rem",
			justifyContent: "flex-end",
			marginBottom: "16px",
			width: "100%",
		}),

	collapsBottomLine: (theme: AppTheme) =>
		css({
			borderBottom: `1px solid ${theme.colors.grey[200]}`,
			marginBottom: theme.spaces.md,
		}),

	divider: (theme: AppTheme) =>
		css({
			marginBlock: theme.spaces.md,
		}),

	fieldLabel: css({
		minWidth: "300px",
	}),
	fieldTitle: (theme: AppTheme) =>
		css({
			marginBlockEnd: theme.spaces.md,
			marginBlockStart: theme.spaces.lg1,
		}),

	fieldWrapper: (theme: AppTheme) =>
		css({
			alignItems: "center",
			display: "flex",
			marginBlockEnd: theme.spaces.md,
		}),
	sakaniTag: (theme: AppTheme) =>
		css({
			[desktopMQ(theme)]: {
				"& > *": {
					insetBlockStart: theme.spaces.md,
					insetInlineEnd: theme.spaces.md,
					position: "absolute",
				},
				position: "relative",
			},
		}),
	status: () =>
		css({
			display: "flex",
			justifyContent: "space-between",
		}),
};

export enum EditModalState {
	Confirmation = "confirmation",
	Discard = "discard",
	Edit = "edit",
}

export type LeadCardProps = {
	bankableStatus: string;
	budget?: string;
	communityEn: string;
	createNoteError?: any;
	expand?: boolean;
	expended?: string;
	financeArranged: string;
	financeMethod?: string;
	financeRep: string;
	financeRepEmail: string;
	financeRepMobile: string;
	followUpList: string;
	homeFinanceClearance: string;
	homeFinanceProvider: string;
	id: string;
	index: number;
	interestedCommunities: any[];
	isCreatingNote: boolean;
	isSakaniBeneficiary?: boolean;
	maxFinInstallment: string;
	maxFinanceAmount: string;
	maxFinancePeriod: string;
	notes?: Note[];
	onAddNote?: (noteText: string) => void;
	onHoldUnit: (value: { leadNumber: string }) => void;
	owner: string;
	ownerRegistryId: string;
	preferredNumberOfBedrooms?: string;
	preferredUnitType?: string;
	prevEditModalState?: string;
	rank?: string;
	registryId: string;
	retireReason: string;
	salaryRange?: string;
	salesMethod: string;
	setExpanded: React.Dispatch<React.SetStateAction<string | undefined>>;
	status: LeadStatus;
	suitableFinanceInstallment: string;
	suitableFinancePeriod: string;
	title: string;
	unitPreferences?: string;
};

const Field = ({
	label,
	value,
	hidden = true,
	isLoading = false,
}: {
	hidden?: boolean | string | undefined;
	isLoading?: boolean;
	label: string;
	value: string | undefined;
}) => {
	return (
		<>
			{hidden && (
				<div css={styles.fieldWrapper}>
					<Typography css={styles.fieldLabel}>{label}</Typography>
					{isLoading ? (
						<Skeleton height={20} width={100} />
					) : (
						<Typography isBold>{value ?? "-"}</Typography>
					)}
				</div>
			)}
		</>
	);
};

export const LeadCard = ({
	isSakaniBeneficiary,
	financeMethod,
	interestedCommunities,
	preferredNumberOfBedrooms,
	preferredUnitType,
	salaryRange,
	budget,
	rank,
	title,
	status,
	notes,
	onAddNote,
	isCreatingNote,
	createNoteError,
	expand = false,
	index,
	expended,
	id,
	setExpanded,
	financeArranged,
	bankableStatus,
	followUpList,
	homeFinanceClearance,
	homeFinanceProvider,
	maxFinInstallment,
	maxFinanceAmount,
	maxFinancePeriod,
	salesMethod,
	suitableFinanceInstallment,
	suitableFinancePeriod,
	registryId,
	retireReason,
	unitPreferences,
	owner: ownerName,
	ownerRegistryId,
	onHoldUnit,
	communityEn,
}: LeadCardProps) => {
	const [transferOwnership] = useFeatureFlag("TransferOwnership");
	const { data: editLead } = useSAFeatureFlagApi(SAFeatureFlags.EditLead);
	const { data: leadHold } = useSAFeatureFlagApi(SAFeatureFlags.LeadCardHold);

	const { direction } = useTheme();

	const { t } = useTranslation();

	const { isCommunityEligibleForHold } = useSaProject();

	const isSaAllowedToHold = isCommunityEligibleForHold(
		communityEn?.toUpperCase(),
	);

	const [editModalState, setEditModalState] = React.useState(
		EditModalState.Edit,
	);
	const prevEditModalStateRef = React.useRef<any>();
	const [ownerSuccess, setOwnerSuccess] = React.useState(false);

	const [owner, setOwner] = React.useState<{
		categories?: string;
		ownerRegistryId: string;
		ownerResourcePartyId?: string;
		ownership?: string;
	} | null>({ ownerRegistryId: ownerRegistryId, ownership: ownerName });

	const [leadModValue, setLeadModValues] = React.useState<{
		[key: string]: any;
	}>({});

	const getLeadConfig = (fromProps: boolean = true) =>
		fromProps
			? {
					budget: budget,
					community: toTitleCase(interestedCommunities[0]),
					followUpList: followUpList,
					payment: SalesMethodMap[financeMethod as keyof typeof SalesMethodMap],
					propertyPreference: {
						archetype: toTitleCase(preferredUnitType),
						bedroom: preferredNumberOfBedrooms,
					},
					rank: toTitleCase(rank),
					retireLead: status === LeadStatus.Retired ? "YES" : "NO",
					retirementReason: retireReason,
					sakaniBeneficiary: isSakaniBeneficiary ? "YES" : "NO",
					salaryRange: salaryRange,
					status: toTitleCase(status),
					unitPreference: unitPreferences,
				}
			: {};

	const [leadEdit, setLeadEdit] = React.useState(false);
	const [leadConfig, setLeadConfig] = React.useState(getLeadConfig());
	const [initialValues, setInitialValues] = React.useState(getLeadConfig(true));

	React.useEffect(() => {
		setLeadConfig(getLeadConfig());
	}, []);

	const [ownershipEdit, setOwnershipEdit] = React.useState(false);

	const preferrences = getPropertyPreferrences({
		preferredNumberOfBedrooms: preferredNumberOfBedrooms,
		preferredUnitType: preferredUnitType,
		t,
	});

	const { isLeadAssignedToAdvisor } = useIsLeadAssignedToAdvisor();

	const isLeadAssignedToCurrentSalesAdvisor = isLeadAssignedToAdvisor(
		owner?.ownerRegistryId ?? "",
	);

	const isEditLeadRestricted = (val?: string) =>
		val ? val === LeadStatus.Retired || val === LeadStatus.Converted : false;

	const hideEditLead =
		isEditLeadRestricted(status) || !isLeadAssignedToCurrentSalesAdvisor;

	const isConvertedOrRetiredErrorDes = useStatusErrors({
		status,
		translationPrefix: "pages.salesAdvisor.customerProfile",
	});

	const showHold =
		isSaAllowedToHold && leadHold && isLeadAssignedToCurrentSalesAdvisor;

	const { mutate, isError, isSuccess, reset, errorDescription, isLoading } =
		useUpdateLead(id, registryId);

	React.useEffect(() => {
		if (index === 0) {
			setExpanded(id);
		}
	}, []);

	React.useEffect(() => {
		prevEditModalStateRef.current = editModalState;
	}, [editModalState]);

	const prevEditModalState = prevEditModalStateRef.current;

	React.useEffect(() => {
		if (!isSuccess && !leadEdit) {
			setLeadConfig(getLeadConfig());
		}
	}, [isSuccess, leadEdit]);

	React.useEffect(() => {
		if (isSuccess) {
			setInitialValues(leadConfig);
		}
	}, [isSuccess]);

	const customerIsFetching = useIsCustomerListFetching();

	const leadInfoConfig = [
		{
			label: t("pages.salesAdvisor.customerProfile.leadStatus"),
			value: t(`pages.salesAdvisor.customerProfile.${status?.toLowerCase()}`),
		},
		{
			label: t("pages.salesAdvisor.customerProfile.leadRank"),
			value: t(
				`pages.salesAdvisor.customerList.filters.${rank?.toLowerCase()}`,
			),
		},
		{
			hidden: isSakaniBeneficiary,
			label: t("pages.salesAdvisor.customerProfile.sakaniBeneficiary"),
			value: t("common.yes"),
		},
		{
			label: t("pages.salesAdvisor.customerProfile.budget"),
			value: budget ? budget?.slice(0, -3) + "" : "-",
		},
		{
			hidden: salaryRange,
			label: t("pages.salesAdvisor.customerProfile.salaryRange"),
			value: salaryRange ? salaryRange.slice(0, -3) + "" : "-",
		},
		{
			label: t("pages.salesAdvisor.customerProfile.financeMethod"),
			value: financeMethod
				? t(`pages.salesAdvisor.customerList.filters.${financeMethod}`)
				: "-",
		},
		{
			label: t("pages.salesAdvisor.customerProfile.interestedCommunities"),
			value: interestedCommunities
				?.map((item) =>
					item
						? t(
								`pages.communities.communityName.${item.toLowerCase()}`,
								item.toUpperCase(),
							)
						: "-",
				)
				.join(" / "),
		},
		{
			label: t("pages.salesAdvisor.customerProfile.propertyPreferences"),
			value: preferrences,
		},
		{
			label: t("pages.salesAdvisor.customerProfile.typologyPreference"),
			value: unitPreferences,
		},
		{
			label: t("pages.salesAdvisor.customerProfile.addFollowUpList"),
			value: followUpList === "YES" ? t("common.yes") : t("common.no"),
		},
	];

	const financeDetailConfig = [
		{
			label: t("pages.salesAdvisor.customerProfile.financeArranged"),
			value: financeArranged,
		},
		{
			label: t("pages.salesAdvisor.customerProfile.homeFinanceProvider"),
			value: homeFinanceProvider,
		},
		{
			label: t("pages.salesAdvisor.customerProfile.bankableStatus"),
			value: bankableStatus,
		},
		{
			label: t("pages.salesAdvisor.customerProfile.maxFinanceAmount"),
			value: maxFinanceAmount,
		},
		{
			label: t("pages.salesAdvisor.customerProfile.maxFinInstallment"),
			value: maxFinInstallment,
		},
		{
			label: t("pages.salesAdvisor.customerProfile.maxFinancePeriod"),
			value: maxFinancePeriod,
		},
		{
			label: t("pages.salesAdvisor.customerProfile.suitableFinanceInstallment"),
			value: suitableFinanceInstallment,
		},

		{
			label: t("pages.salesAdvisor.customerProfile.suitableFinancePeriod"),
			value: suitableFinancePeriod,
		},
		{
			label: t("pages.salesAdvisor.customerProfile.salesMethod"),
			value: SALES_METHOD[salesMethod]
				? t(
						`pages.salesAdvisor.customerProfile.financeTypes.${SALES_METHOD[salesMethod]}`,
						SALES_METHOD[salesMethod],
					)
				: salesMethod,
		},
		{
			label: t("pages.salesAdvisor.customerProfile.homeFinanceClearance"),
			value: homeFinanceClearance,
		},
	];

	return (
		<>
			<ErrorBanner
				type="success"
				description={t("pages.salesAdvisor.customerProfile.ownershipSuccess")}
				trigger={ownerSuccess}
				top="120"
			/>
			<ErrorBanner
				type="success"
				trigger={isSuccess}
				top="120"
				description={t("pages.salesAdvisor.customerProfile.successMessage")}
			/>
			<Collapsible.Root
				dir={direction}
				open={expand || expended === id}
				onOpenChange={() => {
					expended !== id ? setExpanded(id) : setExpanded(undefined);
				}}
			>
				<Collapsible.Trigger
					css={{ marginBottom: 16, marginTop: 16 }}
					className={Classes.sectionHeader}
				>
					<Typography isBold>{title}</Typography>
				</Collapsible.Trigger>
				<div css={styles.collapsBottomLine}></div>
				<Collapsible.Content>
					<Dialog open={leadEdit} onClose={() => setLeadEdit(false)}>
						{editModalState === EditModalState.Confirmation && (
							<EditConfirmationContent
								mutate={mutate}
								leadModValue={leadModValue}
								title={title}
								setEditModalState={setEditModalState}
								setLeadEdit={setLeadEdit}
								isLoading={isLoading}
							/>
						)}

						{editModalState === EditModalState.Discard && (
							<DiscardContent
								onDiscardConfirm={() => {
									setLeadEdit(false);
									setEditModalState(EditModalState.Edit);
								}}
								onDiscardGoBack={() => setEditModalState(EditModalState.Edit)}
							/>
						)}
						{editModalState === EditModalState.Edit && (
							<EditContent
								setLeadConfig={setLeadConfig}
								title={title}
								setEditModalState={setEditModalState}
								setLeadEdit={setLeadEdit}
								defaultValues={leadConfig as EditFormValues}
								initialValues={initialValues}
								reset={reset}
								prevEditModalState={prevEditModalState}
								setLeadModValues={setLeadModValues}
								isDisabled={!!isConvertedOrRetiredErrorDes}
								errorDescription={
									isConvertedOrRetiredErrorDes ? errorDescription : null
								}
								isError={isError || !!isConvertedOrRetiredErrorDes}
							/>
						)}
					</Dialog>
					<Dialog open={ownershipEdit} onClose={() => setOwnershipEdit(false)}>
						<OwnershipContent
							ownershipEdit={ownershipEdit}
							setOwnershipEdit={setOwnershipEdit}
							leadNumber={id}
							onSuccessfulUpdate={(currentOwner: any) => {
								setOwnerSuccess(true);
								setOwner(
									(prev) =>
										({
											...prev,
											ownerRegistryId: currentOwner?.ownerRegistryId,
											ownerResourcePartyId: currentOwner?.ownerResourcePartyId,
											ownership: currentOwner.owner,
										}) as any,
								);
							}}
						/>
					</Dialog>
					<div css={styles.actionButtonWrapper}>
						{editLead && !hideEditLead && (
							<RDSButton
								variant="secondary"
								onClick={() => setLeadEdit(true)}
								css={styles.actionButton}
								text={t("common.edit")}
								disabled={customerIsFetching}
							/>
						)}
						{showHold && (
							<RDSButton
								variant="secondary"
								onClick={() => onHoldUnit({ leadNumber: id })}
								css={styles.actionButton}
								text={t("common.hold")}
							/>
						)}
					</div>
					{leadInfoConfig.map((props, idx) => (
						<Field
							key={idx}
							isLoading={customerIsFetching && isSuccess}
							{...props}
						/>
					))}
					<div css={styles.fieldTitle}>
						<Typography isBold>
							{t("pages.salesAdvisor.customerProfile.financeDetails")}
						</Typography>
					</div>
					{financeDetailConfig.map((props, idx) => (
						<Field key={idx} {...props} />
					))}
					<Divider css={styles.divider} />
					{transferOwnership && (
						<>
							{!hideEditLead && (
								<div css={styles.actionButtonWrapper}>
									<RDSButton
										variant="secondary"
										onClick={() => setOwnershipEdit(true)}
										css={styles.actionButton}
										text={t(
											"pages.salesAdvisor.customerProfile.transferOwnership",
										)}
									/>
								</div>
							)}
							<Field
								label={t("pages.salesAdvisor.customerProfile.owner")}
								value={owner?.ownership}
								hidden={owner?.ownership}
							/>
						</>
					)}
					<Divider css={styles.divider} />
					<div className={InfoCard.Classes.field}>
						<Typography variant="bodyS">
							{t("pages.salesAdvisor.customerProfile.notes")}
						</Typography>
						<LeadNotesForm
							key={notes?.length}
							onAddNote={onAddNote}
							isCreatingNote={isCreatingNote}
							error={createNoteError}
						/>
					</div>
					<LeadNotes notes={notes} />
				</Collapsible.Content>
			</Collapsible.Root>
		</>
	);
};
