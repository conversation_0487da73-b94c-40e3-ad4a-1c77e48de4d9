import { Typography } from "@/components/typography/typography";
import { AppTheme } from "@/theme";
import { TrueNavy300, TrueNavy500 } from "@/theme/palette/all-colors";
import { css, useTheme } from "@emotion/react";
import { Table } from "@radix-ui/themes";
import { useTranslation } from "react-i18next";
import { FollowUpList } from "./follow-up-list";
import { useSAFeatureFlagApi, SAFeatureFlags, useFeatureFlag } from "@/features/feature-flags";
import {
	ContactKey,
	LeadStatus,
	Sort,
	useCustomerList,
} from "@/services/sales-advisor";
import { DirectionProvider } from "@radix-ui/react-direction";
import {
	ScalaType,
	useStateParams,
} from "@/features/master-plan/use-state-params";
import { CustomerListItemSkeleton } from "@/pages/sales-advisor/customer-list/customer-list-skeleton";

import { SortIcon } from "./sort-icon";
import { CustomerSearch } from "@/services/sales-advisor/helpers";
import { useState } from "react";
import { Pagination } from "@/components/pagination/pagination";

const styles = {
	editColumn: (theme: AppTheme) =>
		css({
			"& td:last-of-type": {
				backgroundColor: theme.colors.grey[100],
				padding: `${theme.spaces.md} ${theme.spaces.xl2}`,
				position: "sticky !important",
				[theme.direction === "rtl" ? "left" : "right"]: 0,
				zIndex: 2,
			},

			"& thead tr th:last-of-type": {
				backgroundColor: TrueNavy500,
				borderEnd: "none !important",
				borderStartEndRadius: "8px",
				position: "sticky !important",
				[theme.direction === "rtl" ? "left" : "right"]: 0,
				zIndex: 2,
			},
		} as any),
	icon: css({
		cursor: "pointer",
		minWidth: "1rem",
	}),
	nameTypo: (theme: AppTheme) =>
		css({
			color: TrueNavy500,
			fontSize: theme.fontSizes.bodyS,
			fontWeight: `${theme.typography.fontWeightBold} !important`,
			textDecoration: "underline",
		}),
	separateWordHeader: (theme: AppTheme) =>
		css({
			alignItems: "center",
			display: "flex",
			gap: theme.spaces.xs3,
			justifyContent: "space-between",
			textWrap: "pretty",
		}),

	tableBody: (theme: AppTheme) =>
		css({
			border: `1px solid ${theme.colors.grey[200]}`,
			borderTop: 0,
			overflowX: "auto", // Ensure the table is scrollable horizontally
			paddingInline: theme.spaces.lg,
			scrollBehavior: "smooth", // Ensure smooth scrolling if adjusted
			// Set scroll position to the left for RTL
			[theme.direction === "rtl" ? "scrollRight" : "scrollLeft"]: 0,
		}),

	tableHeader: (theme: AppTheme) =>
		css({
			backgroundColor: TrueNavy500,
			fontSize: theme.typography.captionL.fontSize,
			fontWeight: `${theme.typography.fontWeightBold} !important`,
			height: theme.spaces.xl6,
		}),
	tableHeaderContent: (theme: AppTheme) =>
		css({
			alignItems: "center",
			display: "flex",
			gap: theme.spaces.xs3,
		}),

	tableRoot: (theme: AppTheme, isAr: boolean) =>
		css({
			"&": {
				borderRadius: "8px",
				overflow: "hidden",
			},
			"& table": {
				borderCollapse: "collapse",
				color: theme.colors.grey[900],
			},
			"& tbody tr": {
				borderBottom: `1px solid ${theme.colors.grey[300]}`,
				padding: theme.spaces.sm,
			},

			"& tbody tr td": {
				border: `1px solid ${theme.colors.grey["300"]} !important`,
				color: theme.colors.grey["900"],
				fontSize: theme.fontSizes.bodyS,
			},

			"& td, & th": {
				padding: `${theme.spaces.md1} ${theme.spaces.md}`,
				textAlign: isAr ? "right" : "left",
				whiteSpace: "nowrap",
			},

			"& th": {
				border: `1px solid ${TrueNavy300} !important`,
			},

			"& th div": {
				color: theme.colors.common.white,
				fontWeight: theme.typography.fontWeightBold,
			},

			"& thead tr th": {
				borderTop: "none !important",
				padding: "8px 16px",
			},

			"& thead tr th:first-of-type": {
				borderInlineStart: "none !important",
				borderStartStartRadius: "8px",
				borderTop: "none !important",
			},

			"& thead tr th:last-of-type": {
				backgroundColor: TrueNavy500,
				borderInlineEnd: "none !important",
				borderStartEndRadius: "8px",
			},
		} as any),
};

export const LeadTable = ({
	search,
	followUpParams,
}: {
	followUpParams: {
		interestedCommunities: string[];
		leadRank: string[];
		nextAction: string[];
		paymentMethod: string;
		unitPreferences: string[];
		unitType: string[];
	};
	search: CustomerSearch;
}) => {
	const theme = useTheme();
	const { t, i18n } = useTranslation();
	const lang = i18n.resolvedLanguage?.toUpperCase();
	const [customizeColumn] = useFeatureFlag("CustomizeColumn");
	const defaultSortParams = {
		sortField: undefined,
		sortOrder: undefined,
	};
	const isAr = i18n.resolvedLanguage === "ar";
	const [followUpSort, setFollowUpSort] = useStateParams<{
		sortField: ContactKey | undefined;
		sortOrder: Sort | undefined;
	}>({
		defaultParams: defaultSortParams,
		schema: {
			sortField: ScalaType.String,
			sortOrder: ScalaType.String,
		},
	});

	const [showCommOfInterest] = useFeatureFlag("ShowCommunityOfInterestInList");
	const [page, setPage] = useState(0);
	const { data: extraFollowUpFilters } = useSAFeatureFlagApi(
		SAFeatureFlags.ExtraFollowUpFilter,
	);
	const { data: shouldHeaderNameSplit } = useSAFeatureFlagApi(
		SAFeatureFlags.HeaderNameSplit,
	);

	const { data, fetchNextPage, isLoading } = useCustomerList({
		advanceFilter: followUpParams,
		followUpSort,
		initialFilters: {
			"Leads.FollowupList": true,
			...(extraFollowUpFilters && {
				"Leads.StatusCode": [LeadStatus.Qualified, LeadStatus.Unqualified],
			}),
		},
		page,
		search,
	});

	const commonFilterPrefix = "pages.salesAdvisor.followUpList.filter.";
	const headerItems = [
		{ dataField: "ContactName", labelField: "customerName", sortable: true },
		{ dataField: "Leads.LeadNumber", labelField: "leadNumber", sortable: true },
		{ dataField: "MobileNumber", labelField: "phoneNumber", sortable: true },
		{
			dataField: "Leads.UnitType",
			labelField: "unitPreferences",
			sortable: true,
		},
		{
			dataField: `Leads.Budget${lang}`,
			labelField: "budget",
			sortable: true,
		},
		...(showCommOfInterest
			? [
					{
						dataField: `Leads.CommunityOfInterest${lang}`,
						labelField: "communitiesOfInterest",
						sortable: true,
					},
				]
			: []),
		{
			dataField: `Leads.SalesMethod`,
			labelField: "paymentMethod",
			sortable: true,
		},
		{ dataField: `Leads.Rank`, labelField: "leadRank", sortable: true },
		{
			dataField: `Leads.NextAction`,
			labelField: "nextAction",
			sortable: false,
		},
		{
			dataField: "Leads.NextActionDateTime",
			labelField: "followUpDateAndTime",
			sortable: true,
		},
		// { dataField: "", labelField: "followUpTime", sortable: false },
		// { dataField: "", labelField: "notes", sortable: false },
	];
	const currentPage = data?.pages?.[0];

	const handleFollowUpSort = (fieldName: ContactKey) => {
		const sortState = !followUpSort.sortOrder
			? Sort.Desc
			: followUpSort.sortOrder === Sort.Desc
				? Sort.Asc
				: undefined;
		setFollowUpSort({
			sortField: sortState ? fieldName : undefined,
			sortOrder: sortState,
		});
	};
	return (
		<>
			<DirectionProvider dir={theme.direction}>
				<Table.Root
					dir={theme.direction}
					css={[
						styles.tableRoot(theme, isAr),
						customizeColumn && styles.editColumn,
					]}
				>
					<Table.Header dir={theme.direction} css={styles.tableHeader}>
						<Table.Row dir={theme.direction}>
							{headerItems.map((field, idx) => {
								return (
									<Table.ColumnHeaderCell key={`${field}_${idx}`}>
										<div
											css={
												shouldHeaderNameSplit
													? styles.separateWordHeader
													: styles.tableHeaderContent
											}
										>
											<Typography>
												{t(`${commonFilterPrefix + field.labelField}`)}
											</Typography>
											{field.dataField && field.sortable && (
												<SortIcon
													isAsc={
														followUpSort.sortField === field.dataField &&
														followUpSort.sortOrder === Sort.Asc
													}
													isSelectedField={
														followUpSort.sortField === field.dataField
													}
													onClick={() =>
														handleFollowUpSort(field.dataField as ContactKey)
													}
													css={styles.icon}
												/>
											)}
										</div>
									</Table.ColumnHeaderCell>
								);
							})}
							{customizeColumn && (
								<Table.ColumnHeaderCell>
									<Typography>{t("common.edit")}</Typography>
								</Table.ColumnHeaderCell>
							)}
						</Table.Row>
					</Table.Header>
					{isLoading ? (
						<Table.Body>
							<Table.Cell colSpan={14}>
								{Array.from({ length: 3 }).map((_, idx) => (
									<CustomerListItemSkeleton itemsPerRow={13} key={idx} />
								))}
							</Table.Cell>
						</Table.Body>
					) : (
						<Table.Body css={styles.tableBody}>
							<FollowUpList data={currentPage?.data} />
						</Table.Body>
					)}
				</Table.Root>
			</DirectionProvider>
			<Pagination
				totalPageCount={currentPage?.totalPages ?? 0}
				onSelectPage={(page) => {
					fetchNextPage({ pageParam: page });
					setPage(page);
				}}
				currentPage={page}
			/>
		</>
	);
};
