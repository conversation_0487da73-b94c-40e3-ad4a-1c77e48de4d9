import { useQuery } from "@tanstack/react-query";
import { useFeatureFlagApi } from "@/features/feature-flags";
import { useInjection, QueryKey, CommunityService } from "@roshn/shared";

export function useProjectsQuery() {
	const communityService = useInjection<CommunityService>(CommunityService);

	const { data: featureFlagAPI } = useFeatureFlagApi();
	// Default to true since shared package feature flags are deprecated
	const enableSedra2A = true;
	const enableAlmanarCommunity = true;
	const enableAldanah = true;
	const enableSedra5 = featureFlagAPI?.enableSedra5 || false;


	const { data, isLoading } = useQuery({
		enabled: !!featureFlagAPI,
		queryFn: () => communityService.getProjects(),
		queryKey: [QueryKey.COMMUNITY_PROJECTS],
		refetchOnReconnect: false,
		refetchOnWindowFocus: false,
		retry: false,
		select: (data) => {
			const { sedra, almanar, aldanah, ...otherCommunities } = data;
			const filteredSedra = sedra
			.filter((i) => enableSedra2A || i !== "sedra_2a")
			.filter((i) => enableSedra5 || i !== "sedra_5");

			return {
				...otherCommunities,
				...(enableAlmanarCommunity ? { almanar } : {}),
				...(enableAldanah ? { aldanah } : {}),
				sedra: filteredSedra,
			};
		},
		suspense: false,
	});

	return { data, isLoading };
}
