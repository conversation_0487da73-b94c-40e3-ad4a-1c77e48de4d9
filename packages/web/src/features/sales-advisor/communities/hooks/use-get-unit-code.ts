import { useMutation } from "@tanstack/react-query";
import { SAFeatureFlags, useSAFeatureFlagApi } from "@/features/feature-flags";
import { PropertyDetailService, useInjection } from "@roshn/shared";
export function useSearchUnitByUnitNumber({
	onSuccess,
	withPrice = false,
}: {
	onSuccess?: (
		data:
			| string
			| { price: string; unitCode: string; unitStatus: string }
			| undefined,
	) => void;
	withPrice?: boolean;
}) {
	const { data: holdUnitPrice } = useSAFeatureFlagApi(
		SAFeatureFlags.HoldUnitPrice,
	);

	const svc = useInjection<PropertyDetailService>(PropertyDetailService);
	const {
		mutateAsync: searchUnit,
		isLoading,
		error,
		reset,
		isSuccess,
		isError,
	} = useMutation(
		({
			communityName,
			projectName,
			unitNumber,
		}: {
			communityName: string;
			projectName: string;
			unitNumber: string;
		}) =>
			svc.getUnitCode(communityName, projectName, unitNumber, !!holdUnitPrice),
		{
			onSuccess,
		},
	);
	return { error, isError, isLoading, isSuccess, reset, searchUnit };
}
