import { InfoIcon, SearchDarkIcon } from "@/components/icons";
import { Input } from "@/components/input";
import { SelectInput } from "@/components/select";
import { Typography } from "@/components/typography";
import {
	useAllCustomersList,
	useIsLeadAssignedToAdvisor,
	useSaProject,
} from "@/services/sales-advisor";
import { useTranslatedOptions } from "@/services/sales-advisor/hooks/use-translated-options";
import { AppTheme } from "@/theme";
import { css } from "@emotion/react";
import { useFormik } from "formik";
import { useEffect, useState } from "react";
import { ControlProps, OptionProps, components } from "react-select";
import * as Yup from "yup";
import { useTranslation } from "react-i18next";
import { SAFeatureFlags, useSAFeatureFlagApi } from "@/features/feature-flags";
import {
	isLeadStatusEligibleForHold,
	toTitleCase,
} from "@/services/sales-advisor/helpers";
import { LabelWrapper } from "@/components/label-wrapper/label-wrapper";
import { RDSButton } from "@roshn/ui-kit";
import { useProjectsQuery } from "../communities";
 
export type HoldUnitFields = {
	communityName: string;
	customerName?: string;
	leadNumber?: string;
	phoneNumber?: string;
	price?: string | number;
	project: string;
	unitNumber: string;
};

type HoldUnitProps = {
	contactRegistryId?: string;
	defaultValue?: HoldUnitFields;
	disabled?: boolean;
	isUnitSearchLoading: boolean;
	onDiscard: () => void;
	onHold: (
		args: HoldUnitFields & {
			contactRegistryId: string;
		},
	) => void;
	withPrice?: boolean;
};

type Option = {
	label: string;
	subLabel: string;
	subLabelValue?: string;
	value: string;
};

const styles = {
	button: (theme: AppTheme) =>
		css({
			...theme.typography.captionM,
			width: "100%",
		}),

	buttonWrapper: (theme: AppTheme) =>
		css({
			display: "flex",
			gap: theme.spaces.xl,
			marginTop: theme.spaces.xl,
		}),

	clearIcon: css({
		cursor: "pointer",
	}),

	container: css({
		display: "flex",
		flexDirection: "column",
		width: "720px",
	}),

	customOptionsLabel: (theme: AppTheme) =>
		css({
			marginBlockEnd: theme.spaces.xs2,
		}),

	dialogHeading: (theme: AppTheme) =>
		css({
			...theme.typography.subtitleXL2,
			color: theme.colors.grey["700"],
		}),

	fieldsWrapper: (theme: AppTheme) =>
		css({
			display: "grid",
			gridColumnGap: theme.spaces.xl,
			gridRowGap: theme.spaces.md,
			gridTemplateColumns: "repeat(2, 1fr)",
			gridTemplateRows: "1fr",
		}),

	headerWrapper: (theme: AppTheme) =>
		css({
			marginBlockEnd: theme.spaces.lg,
		}),

	icon: (theme: AppTheme) =>
		css({
			color: theme.colors.grey[300],
			height: "9px",
			marginBlockStart: "4px",
			width: "12px",
		}),

	input: css({
		maxWidth: "344px",
	}),

	nafathHeading: (theme: AppTheme) =>
		css({
			color: theme.colors.grey["700"],
		}),

	nafathWrapper: css({
		alignItems: "center",
		display: "flex",
		gap: "8px",
	}),

	search: (theme: AppTheme) =>
		css({
			...theme.typography.bodyS,
			background: theme.colors.common.white,
			boxShadow: theme.shadows.md,
			height: "44px",
			marginBlockEnd: theme.spaces.md,
		}),

	searchIcon: (theme: AppTheme) =>
		css({
			marginBlockStart: theme.spaces.sm,
			marginInline: theme.spaces.xs3,
		}),
};

export const HoldUnitForm = ({
	contactRegistryId,
	defaultValue = {},
	onDiscard,
	onHold,
	isUnitSearchLoading,
	disabled = false,
	withPrice = false,
}: HoldUnitProps) => {
	const { t } = useTranslation();
	const th = useTranslation(undefined, {
		keyPrefix: "pages.salesAdvisor.holdUnit",
	}).t;

	const t_project = useTranslation(undefined, {
		keyPrefix: `features.masterPlan.breadcrumb.project`,
	}).t;
	const [searchedCustomer, setSearchedCustomer] = useState<{
		label: string;
		leads: { CommunityOfInterest: string; LeadNumber: string }[];
		subLabel: string;
		value: string;
	}>();

	const { data: holdNafathInfoBanner } = useSAFeatureFlagApi(
		SAFeatureFlags.HoldNafathInfoBanner,
	);

	const { projects: saProjects, isCommunityEligibleForHold } = useSaProject();

	const [disableState, setDisableState] = useState<boolean>(disabled);

	useEffect(() => {
		setDisableState(disabled);
	}, [disabled]);
	const [leadOptions, setLeadOptions] = useState<Option[]>();
	const [searchOptions, setSearchOptions] = useState<any>();

	const { communitiesOptions, projectOption } = useTranslatedOptions();

	const { data: projects } = !defaultValue?.project
		? useProjectsQuery()
		: { data: null };

	const [communityProjects, setCommunityProjects] = useState<any>(
		projects?.[defaultValue?.communityName?.toLowerCase()],
	);
	const projectOptions = communityProjects?.map((item) => ({
		label: t_project(item),
		value: item,
	}));

	const { data: allCustomers } = useAllCustomersList();

	const validationSchema = Yup.object<HoldUnitFields>({
		communityName: Yup.string().required("errorRequired"),
		customerName: Yup.string().required("errorRequired"),
		leadNumber: Yup.string().required("errorRequired"),
		phoneNumber: Yup.string().required("errorRequired"),
		price: Yup.string(),
		project: Yup.string().required("errorRequired"),
		unitNumber: Yup.string().required("errorRequired"),
	});

	const {
		values,
		setFieldValue,
		errors,
		setValues,
		isValid,
		handleSubmit,
		resetForm,
		setFieldTouched,
	} = useFormik<HoldUnitFields>({
		initialValues: defaultValue,
		onSubmit: (values) => {
			onHold({ ...values, contactRegistryId: searchedCustomer?.value ?? "" });
		},
		validateOnBlur: true,
		validateOnChange: true,
		validateOnMount: true,
		validationSchema,
	});

	const valueManager = (
		options: { label: string; subLabel?: string | undefined; value: string }[],
		value: string,
	) => options?.findLast((i) => i?.value === value) ?? null;

	const CustomOptions = (props: OptionProps<any, false>) => (
		<components.Option {...props}>
			<div css={styles.customOptionsLabel}>{props.data.label}</div>
			<div>{props.data.subLabel}</div>
		</components.Option>
	);

	const CustomControl = ({ children, ...props }: ControlProps<any>) => (
		<components.Control {...props}>
			<SearchDarkIcon size={16} css={styles.searchIcon} />
			{children}
		</components.Control>
	);

	const leadOptionMapper = (data) => {
		return data?.leads?.map((i) => {
			const communityInterest = i?.CommunityOfInterestEN;
			return {
				label: i?.LeadNumber,
				subLabel:
					communityInterest &&
					t(
						`pages.salesAdvisor.followUpList.filter.${communityInterest.toLowerCase()}`,
					),
				subLabelValue: toTitleCase(communityInterest),
				value: i?.LeadNumber,
			};
		});
	};
	const customFilterOption = (option: any, inputValue: string) => {
		const label = option.data.label.toLowerCase();
		const subLabel = option.data.subLabel.toLowerCase();
		const input = inputValue.toLowerCase();

		return label.includes(input) || subLabel.includes(input);
	};

	const noDefaults = Object.keys(defaultValue).length === 0;

	const { isLeadAssignedToAdvisor } = useIsLeadAssignedToAdvisor();

	useEffect(() => {
		const filteredCustomers = contactRegistryId
			? allCustomers.filter(
					(customer) => customer?.ContactRegistryId === contactRegistryId,
				)
			: allCustomers;

		const searchOptions = filteredCustomers?.map((customer) => {
			const filteredLeads = customer.Leads?.filter((lead) => {
				const isMatchingCommunity =
					lead.CommunityOfInterestEN?.toLowerCase() ===
					values?.communityName?.toLowerCase();

				const isQualifiedStatus = isLeadStatusEligibleForHold(lead?.StatusCode);

				// checks if lead number was passed as default value then only use it for filtering
				const isLeadNumberMatching = defaultValue.leadNumber
					? defaultValue.leadNumber === lead.LeadNumber
					: true;

				const isSaAllowedToHold = isCommunityEligibleForHold(
					values?.communityName?.toUpperCase() ??
						lead?.CommunityOfInterestEN?.toUpperCase(),
				);

				const isLeadAssignedToCurrentSalesAdvisor = isLeadAssignedToAdvisor(
					lead?.OwnerRegistryId,
				);

				const communityNullCheck = !!lead.CommunityOfInterestEN;

				const isLeadValid =
					isMatchingCommunity &&
					isQualifiedStatus &&
					isSaAllowedToHold &&
					isLeadNumberMatching &&
					isLeadAssignedToCurrentSalesAdvisor &&
					communityNullCheck;

				const isLeadValidWithNoDefaults =
					noDefaults &&
					isLeadAssignedToCurrentSalesAdvisor &&
					isSaAllowedToHold &&
					isQualifiedStatus &&
					communityNullCheck;

				if (isLeadValid || isLeadValidWithNoDefaults) {
					return lead;
				}
			});

			const sortedByCreationDate = filteredLeads?.sort(
				(a, b) => new Date(b?.LeadCreationDate) - new Date(a?.LeadCreationDate),
			);

			return {
				label: customer?.ContactName,
				leads: sortedByCreationDate,
				subLabel: customer?.MobileNumber,
				subValueLabel: customer?.MobileNumber,
				value: customer?.ContactRegistryId,
			};
		});

		setSearchOptions(searchOptions);
		contactRegistryId && setLeadOptions(leadOptionMapper(searchOptions?.[0]));
	}, [allCustomers, contactRegistryId, values?.communityName, saProjects]);

	useEffect(() => {
		const leads = leadOptionMapper(searchedCustomer);

		!contactRegistryId &&
			setValues(
				{
					...defaultValue,
					customerName: searchedCustomer?.label,
					leadNumber: undefined,
					phoneNumber: searchedCustomer?.subLabel,
				},
				true,
			);

		!contactRegistryId && setLeadOptions(leads as Option[]);
	}, [searchedCustomer]);

	useEffect(() => {
		setCommunityProjects(
			projects?.[
				values?.communityName?.toLocaleLowerCase() as keyof typeof projects
			],
		);
	}, [values?.communityName]);

	return (
		<div css={styles.container}>
			<div css={styles.headerWrapper}>
				<Typography css={styles.dialogHeading}>
					{t("common.holdUnit")}
				</Typography>
				{holdNafathInfoBanner && (
					<div css={styles.nafathWrapper}>
						<InfoIcon width={16} height={16} />
						<Typography css={styles.nafathHeading}>
							{th("nafathInfo")}
						</Typography>
					</div>
				)}
			</div>

			{!contactRegistryId && (
				<SelectInput
					placeholder={th("selectCustomer")}
					options={searchOptions ?? []}
					value={searchedCustomer}
					css={styles.search}
					components={{
						Control: CustomControl,
						Option: CustomOptions,
					}}
					filterOption={customFilterOption}
					isSearchable
					onChange={(e) => {
						setSearchedCustomer(e ?? undefined);
					}}
				/>
			)}
			<form onSubmit={handleSubmit}>
				<div css={styles.fieldsWrapper}>
					<LabelWrapper
						label={th("customerName")}
						error={errors["customerName"]}
					>
						<Input
							placeholder=""
							value={values["customerName"]}
							disabled={true}
							css={styles.input}
						/>
					</LabelWrapper>
					<LabelWrapper label={th("phoneNumber")} error={errors["phoneNumber"]}>
						<Input
							placeholder=""
							value={values["phoneNumber"]}
							disabled={true}
							css={styles.input}
						/>
					</LabelWrapper>
					<LabelWrapper label={th("leadNumber")} error={errors["leadNumber"]}>
						<SelectInput
							placeholder={th("selectLead")}
							options={(leadOptions ?? []) as Option[]}
							value={valueManager(
								(leadOptions ?? []) as Option[],
								values["leadNumber"] as string,
							)}
							onChange={(e) => {
								setFieldValue("communityName", e?.subLabelValue, true).then(
									() => {
										setFieldTouched("communityName", true);
									},
								);
								setFieldValue("leadNumber", e?.value);
							}}
							components={{
								Option: CustomOptions,
							}}
							isDisabled={!!defaultValue?.leadNumber}
						/>
					</LabelWrapper>
					<LabelWrapper label={th("community")} error={errors["communityName"]}>
						<SelectInput
							placeholder=""
							options={communitiesOptions}
							value={valueManager(communitiesOptions, values["communityName"])}
							isDisabled
						/>
					</LabelWrapper>
					<LabelWrapper label={th("project")} error={errors["project"]}>
						<SelectInput
							placeholder={th("selectProject")}
							options={
								!defaultValue.project
									? projectOptions
									: projectOption(values["project"])
							}
							value={valueManager(
								!defaultValue.project
									? (projectOptions as any)
									: projectOption(values["project"]),
								values["project"],
							)}
							onChange={(e) => setFieldValue("project", e?.value)}
							isDisabled={!!defaultValue.project}
						/>
					</LabelWrapper>
					<LabelWrapper label={th("unitNumber")} error={errors["unitNumber"]}>
						<Input
							placeholder={th("unitNumberPlaceholder")}
							type="number"
							inputMode="numeric"
							onChange={(e) => {
								if (values["unitNumber"] !== e.target.value) {
									setDisableState(false);
								}
								setFieldValue("unitNumber", e.target.value);
							}}
							value={values["unitNumber"] ?? ""}
							disabled={!!defaultValue.unitNumber}
							css={styles.input}
						/>
					</LabelWrapper>
					{!!values["price"] && withPrice && (
						<LabelWrapper label={th("price")}>
							<Input
								placeholder="Price"
								inputMode="numeric"
								value={`${values["price"]}${""}` as string}
								disabled={true}
								css={styles.input}
							/>
						</LabelWrapper>
					)}
				</div>
				<div css={styles.buttonWrapper}>
					<RDSButton
						onClick={onDiscard}
						css={styles.button}
						variant="secondary"
						text={t("common.discard")}
					/>
					<RDSButton
						css={styles.button}
						disabled={
							!isValid ||
							disableState ||
							isUnitSearchLoading ||
							(withPrice && !values["price"])
						}
						variant="primary"
						text={t("common.hold")}
					/>
				</div>
			</form>
		</div>
	);
};
