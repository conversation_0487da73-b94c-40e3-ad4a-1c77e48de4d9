import { Typography } from "@/components/typography";
import { Input, SearchItem, Toggle } from "./search-item";
import { useState } from "react";
import { AppTheme, tabletMQ } from "@/theme";
import { css } from "@emotion/react";
import { useTranslation } from "react-i18next";
import { AppPaths, useAppPathGenerator } from "@/routes";
import { useNavigate } from "@remix-run/react";
import { SearchUnitSkeleton } from "./search-unit-skeleton";
import { LeadCasedInfo } from "@/services/sales-advisor";
import { RDSButton } from "@roshn/ui-kit";
import { useProjectsQuery, useSearchUnitByUnitNumber } from "../../communities";

export const styles = {
	button: (theme: AppTheme) =>
		css({
			[tabletMQ(theme)]: {
				alignSelf: "end",
			},
			alignSelf: "flex-start",
			height: theme.spaces.xl3,
			textWrap: "nowrap",
		}),
	itemsWrapper: (theme: AppTheme) =>
		css({
			display: "flex",
			[tabletMQ(theme)]: {
				flexDirection: "row",
				gap: theme.spaces.md,
				justifyContent: "space-between",
			},
			flexDirection: "column",
			gap: theme.spaces.md,
			width: "100%",
		}),
	searchWrapper: (theme: AppTheme) =>
		css({
			display: "flex",
			[tabletMQ(theme)]: {
				flexDirection: "row",
				gap: theme.spaces.xl,
				justifyContent: "space-between",
			},
			flexDirection: "column",
			gap: theme.spaces.lg,
		}),
	title: (theme: AppTheme) =>
		css({
			lineHeight: theme.spaces.xl,
			marginBottom: theme.spaces.lg,
			textTransform: "capitalize",
		}),
};

type SearchByUnitSectionProps = {
	data?: ReturnType<typeof useProjectsQuery>["data"];
	getUnitCodeInHandleClick?: boolean;
	handleClick?: ({
		communityName,
		leadNumber,
		project,
		unitNumber,
		price,
	}: {
		communityName: string;
		leadNumber: string;
		price?: number;
		project: string;
		unitId?: string;
		unitNumber: string;
	}) => void;
	isLoading: boolean;
	label?: string;
	leads?: LeadCasedInfo[];
	showLeadSelector?: boolean;
	withPrice?: boolean;
};

export const SearchByUnitSection = ({
	data,
	isLoading,
	label,
	leads,
	showLeadSelector = false,
	getUnitCodeInHandleClick = false,
	handleClick,
	withPrice = false,
}: SearchByUnitSectionProps) => {
	const t = useTranslation(undefined, {
		keyPrefix: "pages.salesAdvisor.dashboard.searchUnits",
	}).t;

	const leadOptions = leads?.map((lead) => lead?.leadNumber);
	const [communityExpanded, setCommunityExpanded] = useState(false);
	const [leadExpanded, setLeadExpanded] = useState(false);
	const [projectExpanded, setProjectExpanded] = useState(false);

	const [community, setCommunity] = useState<string>("");
	const [project, setProject] = useState<string>("");
	const [unitNumber, setUnitNumber] = useState<string>("");
	const [leadNumber, setLeadNumber] = useState<string>("");

	const generateAppPath = useAppPathGenerator();
	const navigate = useNavigate();

	const {
		searchUnit,
		isLoading: getUnitLoading,
		error,
	} = useSearchUnitByUnitNumber({
		onSuccess: (data?: string) => {
			if (handleClick && getUnitCodeInHandleClick) {
				handleClick({
					communityName: community,
					leadNumber: leadNumber,
					project,
					unitId: withPrice ? data?.unitCode : data,
					unitNumber,
					...(withPrice && data),
				});
				return;
			}
			return navigate(
				generateAppPath(AppPaths.propertyDetails, {
					communityName: community,
					unitId: data as string | number,
				}),
			);
		},
		withPrice,
	});

	const handleSearch = () => {
		searchUnit({
			communityName: community,
			projectName: project,
			unitNumber,
		});
	};

	if (isLoading) {
		return <SearchUnitSkeleton width={label ? 200 : 320} />;
	}

	return (
		<div>
			<Typography variant="bodyL" css={styles.title}>
				{label ? label : t("title")}
			</Typography>
			<div css={styles.searchWrapper}>
				<div css={styles.itemsWrapper}>
					{showLeadSelector && (
						<SearchItem title={t("leadNumber")} isEnabled>
							<Toggle
								item="leadNumber"
								enableOptionsTranslations={false}
								options={leadOptions as string[]}
								isEnabled={
									leadOptions &&
									Array.isArray(leadOptions) &&
									leadOptions?.length > 0
								}
								onSelectedOptionChanged={(val) => {
									if (val) {
										setTimeout(() => {
											setLeadExpanded(false);
										}, 200);
									}
									setProject("");
									setLeadNumber(val);
									const leadInfo = leads?.find((l) => l.leadNumber === val);
									const leadCommunityOfInterest =
										leadInfo?.communityOfInterestEN?.toLowerCase();
									if (leadCommunityOfInterest) {
										setCommunity(leadCommunityOfInterest);
									}
								}}
								expand={leadExpanded}
								onExpand={(open) => {
									setLeadExpanded(open);
									setCommunityExpanded(false);
									setProjectExpanded(false);
								}}
								selectedOption={leadNumber}
							/>
						</SearchItem>
					)}
					<SearchItem title={t("item.community")} isEnabled={!showLeadSelector}>
						<Toggle
							item="community"
							options={Object.keys(data as object)}
							isEnabled={!showLeadSelector}
							onSelectedOptionChanged={(val) => {
								if (val) {
									setTimeout(() => {
										setCommunityExpanded(false);
									}, 200);
								}
								setProject("");
								setCommunity(val);
							}}
							expand={communityExpanded}
							onExpand={(open) => {
								setCommunityExpanded(open);
								setProjectExpanded(false);
							}}
							selectedOption={community}
						/>
					</SearchItem>
					<SearchItem title={t("item.project")} isEnabled={!!community}>
						<Toggle
							item="project"
							options={
								(data?.[community as keyof typeof data] ?? []) as string[]
							}
							onSelectedOptionChanged={(val) => {
								if (val) {
									setTimeout(() => {
										setProjectExpanded(false);
									}, 200);
								}
								setProject(val);
							}}
							expand={projectExpanded}
							onExpand={(open) => {
								setProjectExpanded(open);
								setCommunityExpanded(false);
							}}
							isEnabled={!!community}
							selectedOption={project}
						/>
					</SearchItem>
					<SearchItem title={t("item.unit")} isEnabled={!!project}>
						<Input
							placeholder={t("unitPlaceholder")}
							inputChanged={(e) => setUnitNumber(e.target.value)}
							isEnabled={!!community && !!project}
							error={error ? t("error") : ""}
							inputMode="numeric"
						/>
					</SearchItem>
				</div>
				<RDSButton
					variant="primary"
					css={styles.button}
					onClick={() => {
						handleClick && !getUnitCodeInHandleClick
							? handleClick({
									communityName: community,
									leadNumber,
									project,
									unitNumber,
								})
							: handleSearch();
					}}
					disabled={!community || !project || !unitNumber || getUnitLoading}
					loading={getUnitLoading}
					text={label ? label : t("button")}
				/>
			</div>
		</div>
	);
};
