import {
	renderWithContainer,
	renderWithTheme,
} from "@test/helpers/custom-renders";
import { SearchByUnitSection } from "./search-unit";
import { baseI18Next } from "@test/helpers/i18n";
import userEvent from "@testing-library/user-event";
import { Input } from "./search-item";
import { createRemixStub } from "@remix-run/testing";
import * as communities from "../../communities";

const MOCK_DATA = {
	alarous: ["alarous_1a"],
	sedra: ["sedra_3", "sedra_4a","sedra_2a", "sedra_5"],
	warefa: ["warefa_1"],
};

describe("Search unit", () => {
	test("should render correctly", async () => {
		const searchSpy = vi.fn();
		vi.spyOn(communities, "useSearchUnitByUnitNumber").mockReturnValue({
			error: null,
			isLoading: false,
			searchUnit: searchSpy,
		} as any);

		const RemixStub = createRemixStub([
			{
				Component: () => (
					<SearchByUnitSection data={MOCK_DATA} isLoading={false} />
				),
				path: "/",
			},
		]);

		const result = renderWithContainer(<RemixStub />);

		const t = baseI18Next
			.cloneInstance()
			.getFixedT(null, null, "pages.salesAdvisor.dashboard.searchUnits");
		const tc = baseI18Next
			.cloneInstance()
			.getFixedT(null, null, "features.masterPlan.breadcrumb");
		const user = userEvent.setup();

		expect(result.getByText(t("title"))).toBeVisible();
		expect(result.getByText(t("item.community"))).toBeVisible();
		expect(result.getByText(t("item.project"))).toBeVisible();
		expect(result.getByText(t("item.unit"))).toBeVisible();

		await user.click(result.getByText(t("placeholder.community")));
		await user.click(result.getByText(tc("community.alarous")));
		await user.click(result.getByText(t("placeholder.project")));
		await user.click(result.getByText(tc("project.alarous_1a")));

		await user.type(result.getByPlaceholderText(t("unitPlaceholder")), "1706");
		expect(result.getByText(t("button"))).toBeEnabled();

		await user.click(result.getByText(t("button")));
		expect(searchSpy).toHaveBeenCalled();
	});

	test("should render skeleton", () => {
		const RemixStub = createRemixStub([
			{
				Component: () => (
					<SearchByUnitSection data={MOCK_DATA} isLoading={true} />
				),
				path: "/",
			},
		]);

		const result = renderWithContainer(<RemixStub />);

		expect(
			result.container.getElementsByClassName("react-loading-skeleton").length,
		).toBeTruthy();
	});

	test("should show error for input", () => {
		const result = renderWithTheme(
			<Input
				isEnabled
				placeholder="test"
				inputChanged={vi.fn}
				error={"error"}
			/>,
		);
		expect(result.getByText(/error/i)).toBeVisible();
	});
});
