import { AuxiliaryButton } from "@/components/button";
import { ButtonIcon } from "@/components/button-icon";
import * as Dialog from "@/components/dialog";
import {
	CloseIcon,
	DoubleCheckIcon,
	SearchDarkIcon,
	SettingsIcon,
} from "@/components/icons";
import { Input } from "@/components/input";
import { Typography } from "@/components/typography";
import { AppPaths, generateAppPath } from "@/routes";
import { useMarkAllRead, useNotificationCount } from "@/services/sales-advisor";
import { AppTheme } from "@/theme";
import { BreakerBay500, GoldenRod500 } from "@/theme/palette/all-colors";
import { css, useTheme } from "@emotion/react";
import { DirectionProvider } from "@radix-ui/react-direction";
import * as Tabs from "@radix-ui/react-tabs";
import { useNavigate } from "@remix-run/react";
import { useInjection } from "@roshn/shared";
import { Dispatch, SetStateAction, useState } from "react";
import { useTranslation } from "react-i18next";
import { NotificationService } from "../services";
import { ErrorBanner } from "./error-banner";
import { NotificationList } from "./notification-item-list";
import { RDSButton } from "@roshn/ui-kit";
import { useSAFeatureFlagApi, SAFeatureFlags } from "@/features/feature-flags";

type NotificationTabProps = {
	isFullScreen?: boolean;
	isPopupOpen?: boolean;
	setCurrentState?: Dispatch<SetStateAction<"notification" | "preferences">>;
	setPopUp?: Dispatch<SetStateAction<boolean>>;
};

const styles = {
	actions: (theme: AppTheme) =>
		css({
			alignItems: "center",
			display: "flex",
			gap: theme.spaces.md,
		}),
	button: (theme: AppTheme) =>
		css({
			...theme.typography.captionM,
			height: theme.spaces.xl4,
			width: "fit-content",
		}),

	buttonIcon: (theme: AppTheme) =>
		css({
			height: theme.spaces.xl,
			marginBlockStart: theme.spaces.xs3,
			width: theme.spaces.xl,
		}),

	buttonWrapper: css({
		display: "flex",
		justifyContent: "center",
		width: "100%",
	}),

	clearIcon: css({
		cursor: "pointer",
	}),

	dialogBackButton: (theme: AppTheme) =>
		css({
			...theme.typography.captionM,
			marginBlockEnd: theme.spaces.xs3,
			width: "100%",
		}),

	dialogConfirmButton: (theme: AppTheme) =>
		css({
			...theme.typography.captionM,
			marginBlockEnd: theme.spaces.md,
			textTransform: "uppercase",
			width: "100%",
		}),

	dialogHeading: css({
		marginBlockEnd: "44px",
	}),

	dialogRoot: (theme: AppTheme) =>
		css({
			alignItems: "center",
			display: "flex",
			paddingBlock: theme.spaces.xl3,
			paddingInline: theme.spaces.lg,
			width: "500px",
		}),

	eyeIcon: css({
		transform: "rotate(-180deg)",
	}),

	icon: css({
		color: GoldenRod500,
		position: "absolute",
		top: "4px",
	}),

	iconWrapper: (theme: AppTheme) =>
		css({
			height: theme.spaces.lg,
			position: "relative",
			width: theme.spaces.lg,
		}),

	readButton: (theme: AppTheme) =>
		css({
			...theme.typography.captionM,
			"&:hover": {
				borderBottom: `2px solid ${theme.colors.button.tertiary.active}`,
				color: theme.colors.button.tertiary.hoverText,
				marginBlockEnd: -2,
			},
			height: theme.spaces.lg,
			marginBlockEnd: 0,
			marginBlockStart: theme.spaces.xs4,
		}),

	search: (theme: AppTheme) =>
		css({
			...theme.typography.bodyS,
			background: theme.colors.common.white,
			boxShadow: theme.shadows.md1,
			height: "44px",
			marginBlockEnd: theme.spaces.md1,
			marginBlockStart: theme.spaces.lg,
		}),

	searchIcon: (theme: AppTheme) =>
		css({
			marginRight: theme.spaces.sm,
		}),

	searchLayout: css({
		display: "flex",
		flexDirection: "column",
	}),

	tabContent: (theme: AppTheme) =>
		css({
			flexGrow: 1,
			outline: "none",
			paddingTop: theme.spaces.md,
		}),

	tabList: css({
		"& button": {
			background: "transparent",
			border: "none",
		},
		display: "flex",
		flexShrink: 0,
	}),

	tabRoot: css({
		display: "flex",
		flexDirection: "column",
		marginTop: "-16px",
		width: "100%",
	}),

	tabTrigger: (theme: AppTheme) =>
		css({
			"&[data-state=active]": {
				borderBottomWidth: "2px",
				boxShadow: "inset 0 -1px 0 0 currentColor, 0 .05px 0 0 currentColor",
				color: BreakerBay500,
			},

			alignItems: "center",
			color: theme.colors.grey[500],
			cursor: "pointer",
			display: "flex",
			fontSize: "16px",
			fontWeight: theme.typography.fontWeightBold,
			height: "45px",
			justifyContent: "center",
			lineHeight: "18px",
			marginRight: theme.spaces.sm,
			paddingInline: "8px",
			textTransform: "uppercase",
			userSelect: "none",
		} as any),

	triggerWrapper: (theme: AppTheme) =>
		css({
			display: "flex",
			justifyContent: "space-between",
			paddingRight: theme.spaces.lg,
			width: "100%",
		}),
};

export const NotificationTabs = ({
	setCurrentState,
	isFullScreen = false,
	isPopupOpen = false,
	setPopUp,
}: NotificationTabProps) => {
	const { data: notificationSearch } = useSAFeatureFlagApi(
		SAFeatureFlags.NotificationSearch,
	);
	const theme = useTheme();
	const { t, i18n } = useTranslation(undefined, {
		keyPrefix: "pages.salesAdvisor.notification",
	});
	const navigate = useNavigate();
	const { data: notificationPreferenceFeature } = useSAFeatureFlagApi(
		SAFeatureFlags.NotificationPreferences,
	);

	const [markAllReadStatus, setMarkAllReadStatus] = useState<boolean>(false);

	const notifications = useInjection<NotificationService>(NotificationService);

	const { refetch } = useNotificationCount();

	const isAr = i18n.resolvedLanguage === "ar";

	const { isError, mutate } = useMarkAllRead();

	const [open, setOpen] = useState<boolean>(false);
	const [toSearch, setToSearch] = useState<string>("");

	const handleClearSearch = () => setToSearch("");

	const handleSearch = async (e: React.ChangeEvent<HTMLInputElement>) => {
		setToSearch(e.target.value);
	};

	const suffixContent = () => {
		return (
			<>
				{toSearch.length > 0 && (
					<CloseIcon
						onClick={handleClearSearch}
						css={styles.clearIcon}
						size={20}
					/>
				)}
				{/* <span css={styles.loadingIcon}>{<LoadingIcon />}</span> */}
			</>
		);
	};

	const [markReadDisabled, setMarkReadDisabled] = useState<boolean>(true);

	const handleMarkReadDisabled = (isDisabled: boolean) => {
		setMarkReadDisabled(
			isDisabled || notifications.getNotificationCount() === 0,
		);
	};

	return (
		<DirectionProvider dir={isAr ? "rtl" : "ltr"}>
			<Tabs.Root css={styles.tabRoot} defaultValue="all">
				<div css={styles.searchLayout}>
					<ErrorBanner
						trigger={isError}
						description={t("readError")}
						top={isFullScreen ? "-40" : "20"}
					/>
					<div css={styles.triggerWrapper}>
						<Tabs.List css={styles.tabList} aria-label="Manage your account">
							<Tabs.Trigger css={styles.tabTrigger} value="all">
								{t("all")}
							</Tabs.Trigger>
							<Tabs.Trigger css={styles.tabTrigger} value="un-read">
								{t("unRead")}
							</Tabs.Trigger>
						</Tabs.List>
						<div css={styles.actions}>
							<AuxiliaryButton
								prefixIcon={
									<DoubleCheckIcon
										css={styles.eyeIcon}
										width={isFullScreen ? 24 : 18}
										height={isFullScreen ? 24 : 18}
									/>
								}
								css={
									[
										styles.readButton,
										isFullScreen && {
											...theme.typography.captionM,
										},
									] as any
								}
								onClick={() => setOpen(true)}
								disabled={markReadDisabled}
							>
								{t("markAllRead")}
							</AuxiliaryButton>
							{!isFullScreen && notificationPreferenceFeature && (
								<ButtonIcon css={styles.buttonIcon} size="sm">
									<SettingsIcon
										onClick={() =>
											setCurrentState && setCurrentState("preferences")
										}
										css={styles.icon}
										width={18}
										height={24}
									/>
								</ButtonIcon>
							)}
							<Dialog.Root open={open} onClose={() => setOpen(false)}>
								<Dialog.Overlay onClick={() => setOpen(false)} />
								<Dialog.Content
									css={styles.dialogRoot}
									dismissOnOutsideClick={false}
								>
									<Typography css={styles.dialogHeading} variant="bodyL" isBold>
										{`${t("markAllRead")}?`}
									</Typography>
									<RDSButton
										css={styles.dialogConfirmButton}
										onClick={async () => {
											await mutate();
											notifications.toggleAllRead();
											setOpen(false);
											setMarkAllReadStatus(true);
											refetch();
										}}
										variant="primary"
										text={t("confirm")}
									/>
									<RDSButton
										css={styles.dialogBackButton}
										onClick={() => setOpen(false)}
										variant="secondary"
										text={t("goBack")}
									/>
								</Dialog.Content>
							</Dialog.Root>
						</div>
					</div>
					{isFullScreen && notificationSearch && (
						<Input
							prefix={<SearchDarkIcon size={16} css={styles.searchIcon} />}
							value={toSearch}
							onChange={handleSearch}
							placeholder={"Search"}
							suffix={suffixContent()}
							css={styles.search}
						/>
					)}
				</div>
				<Tabs.Content css={styles.tabContent} value="all">
					<NotificationList
						isPopupOpen={isPopupOpen}
						isFullScreen={isFullScreen}
						markAllReadStatus={markAllReadStatus}
						handleMarkReadDisabled={handleMarkReadDisabled}
						setPopUp={setPopUp}
					/>
				</Tabs.Content>
				<Tabs.Content css={styles.tabContent} value="un-read">
					<NotificationList
						isPopupOpen={isPopupOpen}
						isFullScreen={isFullScreen}
						markAllReadStatus={markAllReadStatus}
						readStatus="false"
						handleMarkReadDisabled={handleMarkReadDisabled}
						setPopUp={setPopUp}
					/>
				</Tabs.Content>
				{!isFullScreen && (
					<div css={styles.buttonWrapper}>
						<RDSButton
							size="lg"
							variant="secondary"
							css={styles.button}
							onClick={() =>
								navigate(generateAppPath(AppPaths.salesAdvisorNotificationList))
							}
							text={t("viewAll")}
						/>
					</div>
				)}
			</Tabs.Root>
		</DirectionProvider>
	);
};
