import { Typography } from "@/components/typography";
import { desktopMQ } from "@/theme";
import { css, Theme, useTheme } from "@emotion/react";
import { useParams } from "@remix-run/react";
import { AppTheme, getThemeBare, RDSTabs } from "@roshn/ui-kit";
import { useTranslation } from "react-i18next";

type HeroBannerProps = {
	backgroundImg: string;
	activeTab: number;
	setActiveTab: (index: number) => void;
	locale?: string;
};

const styles = {
	container: (theme: Theme, backgroundImg: string) =>
		css({
			backgroundImage: `url(${backgroundImg})`,
			backgroundSize: "cover",
			backgroundPosition: "center",
			textAlign: "left",
			color: "white",
			display: "flex",
			flexDirection: "column",
			alignItems: "flex-start",
			justifyContent: "flex-end",
			gap: theme.spaces.md,
			maxWidth: "100%",
			padding: `1rem 1.5rem`,
			height: "25vh",
			[`@media (max-height: 667px)`]: {
				maxHeight: "18.5rem",
			},
			[`@media (min-width: ${theme?.breakPoints.mobile})`]: {
				height: "18.5rem",
			},
			[`@media (min-height: ${theme?.breakPoints.tablet})`]: {
				height: "calc(100vh - 10rem)",
				maxHeight: "18.5rem",
			},
			[`@media (min-width: ${theme?.breakPoints.desktop})`]: {
				minHeight: "18.5rem",
				padding: `1rem 5rem`,
			},
			[`@media (max-height: 1024px)`]: {
				minHeight: "18.5rem",
			},
			[`@media (min-width: ${theme?.breakPoints.largeDesktop})`]: {
				minHeight: "20.5rem",
			},
			[`@media (min-width: 2000px)`]: {
				minHeight: "20.5rem",
			},
			[`@media (min-height: 2400px)`]: {
				minHeight: "20.5rem",
			},
		}),
	tabContainer: (theme: Theme) =>
		css({
			position: "absolute",
			top: "calc(headingHeight + spacing)",
			display: "flex",
			width: "100%",
			gap: theme.spaces.md,
			overflowX: "scroll",
			whiteSpace: "nowrap",
			scrollSnapType: "x mandatory",
			scrollbarWidth: "none",
			msOverflowStyle: "none",
			marginInline: "-1.5rem",
			paddingInline: "1.5rem",

			"&::-webkit-scrollbar": {
				display: "none",
			},

			[desktopMQ(theme)]: {
				marginInline: "-5rem",
				paddingInline: "5rem",
			},
		}),
	title: ({ rds: { typographies } }: AppTheme) =>
		css({
			...typographies.heading.emphasis.h1,
		}),
};

const HeroBanner = ({
	backgroundImg,
	activeTab,
	setActiveTab,
	locale,
}: HeroBannerProps) => {
	const rdsTheme = getThemeBare({
		locale: locale ? locale : "",
		direction: locale === "en" ? "ltr" : "rtl",
		name: "light",
		device: "",
	});
	const theme = useTheme();
	const { t } = useTranslation();
	const rawTabsData = t("pages.profile.accountTabs", { returnObjects: true });
	const tabsData = Object.values(rawTabsData);
	return (
		<div css={styles.container(theme, backgroundImg)}>
			<Typography css={styles.title(rdsTheme)}>
				{t("pages.profile.myAccount")}
			</Typography>
			<div css={{ height: "78px" }}>
				<div css={styles.tabContainer}>
					{tabsData.length < 1 &&
						tabsData.map((tab, index) => (
							<RDSTabs
								key={index}
								label={tab}
								asset={false}
								assetProps={{}}
								isParent={false}
								isDisabled={false}
								rdsBadge={false}
								size="lg"
								state={activeTab === index ? "active" : "inactive"}
								onClick={() => setActiveTab(index)}
							/>
						))}
				</div>
			</div>
		</div>
	);
};

export default HeroBanner;
