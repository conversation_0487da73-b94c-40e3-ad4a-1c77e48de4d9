import { useTranslation } from "react-i18next";
import { PropertyUnit } from "@/types/property-type";
import { DetailCard } from "@/components/detail-card/detail-card";
import {
	propertyTypeKeyMap,
	unitClassification,
} from "@/utils/property-type-utils";
import { usePropertyDetail } from "@roshn/shared";
import { useParams } from "@remix-run/react";

type PropertyInformationCardProps = {
	unit: Partial<PropertyUnit>;
};
export const PropertyInformationCard = ({
	unit,
}: PropertyInformationCardProps) => {
	const { t } = useTranslation();
	const { locale } = useParams();
	const isArabic = locale === "ar";
	const { data: unitData } = usePropertyDetail(unit.unitCode!);

	const typeKey =
		(unit?.unitType && propertyTypeKeyMap[unit.unitType?.toLowerCase()]) ||
		"villa";
	const propertyTypeKey = `features.masterPlan.name.propertyType.${typeKey}`;
	const isPremiumCollection =
		unitData?.unitTypeClassification === unitClassification;
	const propertyTypeName = isPremiumCollection
		? isArabic
			? ` ${t(propertyTypeKey)} ${t("common.premium")}`
			: `${t("common.premium")} ${t(propertyTypeKey)} `
		: t(propertyTypeKey, unit.unitType ?? "");

	return (
		<DetailCard
			title={t(
				"features.propertyReservation.propertyInformation.propertyInformation",
			)}
			data={[
				{
					label: t("features.propertyReservation.propertyInformation.unitType"),
					value: `${propertyTypeName} ${unit.unitInfo?.typologyGroup} ${t(
						`features.masterPlan.name.facadeType.${unit.community?.toLowerCase()}.${unit.unitInfo?.facade?.toLowerCase()}`,
						unit.unitInfo?.facade ?? "",
					)}`,
				},
				{
					label: t(
						"features.propertyReservation.propertyInformation.unitNumber",
					),
					value: unit.unitNumber,
				},
				{
					label: t("features.propertyReservation.propertyInformation.facade"),
					value: `${t(
						`features.masterPlan.name.facadeType.${unit.community?.toLowerCase()}.${unit.unitInfo?.facade?.toLowerCase()}`,
						unit.unitInfo?.facade ?? "",
					)}`,
				},
				{
					label: t("features.propertyDiscovery.plotArea"),
					value: `${unit.unitInfo?.plotArea ?? "N/A"} ${unit.unitInfo?.plotArea ? t("common.sqm") : ""}`.trim(),
				},
				{
					label: t("features.propertyDiscovery.grossFloorArea"),
					value: `${unit.unitInfo?.grossFloorArea ?? "N/A"} ${unit.unitInfo?.grossFloorArea ? t("common.sqm") : ""}`.trim(),
				},
				// {
				// 	label: t("features.propertyDiscovery.builtUpArea"),
				// 	value: unit.unitInfo?.builtUpArea
				// 		? `${unit.unitInfo?.builtUpArea} ${t("common.sqm")}`
				// 		: "N/A",
				// },
				{
					label: t("features.propertyDiscovery.bedroom"),
					value: `${unit.unitInfo?.bedroom ?? ""}`,
				},
				{
					label: t("features.propertyDiscovery.roofTerrace"),
					value:
						(unit.unitInfo?.roofTerrace ? t("common.yes") : t("common.no")) ??
						"",
				},
				{
					label: t("features.propertyDiscovery.additionalRooms"),
					value: `${unit.unitInfo?.additionalRooms ?? ""}`,
				},
				{
					label: t("features.myProperties.parking"),
					value: `${unit.unitInfo?.parking ?? ""}`,
				},
			]}
		/>
	);
};
