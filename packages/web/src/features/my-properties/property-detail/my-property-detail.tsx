import * as React from "react";
import { useNavigate, useParams } from "@remix-run/react";
import { useTranslation } from "react-i18next";
import { useAppPathGenerator } from "@/routes";
import { mapParentStep, hasStepsToComplete, StepsCompletion } from "../utils";
import { Step, StepProps } from "@/components/steps";
import { ReservationItems, FinancingItems, PurchaseItems } from "../components";
import { useFeatureFlagApi } from "@/features/feature-flags";

import * as shared from "@roshn/shared";
import { useAppDispatch, useAppSelector } from "@/store";
import { AppPaths } from "@/routes/app-paths";
import { useMyPropertyDetail } from "@/features/my-properties/hooks";
import { PropertyUnit } from "@/types/property-type";
import { AppTheme, mobileMQ, tabletMQ } from "@/theme";
import { css, Theme } from "@emotion/react";
import { Typography } from "@/components/typography";
import { useFreshCallback } from "rooks";
import { trackNafathExpiredError } from "@/features/reservation/redux/actions-analytics";
import {
	mainMenuSlice,
	nafathIdExpireModalSlice,
} from "@/store/global-modal-slices";
import {
	ReservationCancellationStatus,
	ReservationProgress,
} from "@roshn/shared";
import { RDSButton } from "@roshn/ui-kit";
import {
	getReservationData,
	reservationSlice,
} from "@/features/reservation/redux";

const styles = {
	wrapper: (theme: AppTheme) =>
		css({
			background: theme.colors.common.white,
			bottom: 0,
			boxShadow: "0px -4px 8px rgba(4, 6, 15, 0.05)",
			left: 0,
			padding: `${theme.spaces.lg} 0px`,
			position: "fixed",
			width: "100%",
			zIndex: 1099,
		}),
	body: (theme: AppTheme) =>
		css({
			display: "flex",
			justifyContent: "space-between",
			alignItems: "center",
			paddingInline: theme.spaces.md,
			margin: "auto",
			maxWidth: "1312px",
			width: "100%",
			flexDirection: "row",
			[tabletMQ(theme)]: {
				justifyContent: "space-between",
				display: "flex",
			},
			[mobileMQ(theme)]: {
				flexDirection: "column",
				justifyContent: "center !important",
				alignItems: "center",
			},
		}),
	btn: () =>
		css({
			maxWidth: "380px",
			width: "100%",
			alignSelf: "center",
		}),

	steps: (theme: AppTheme) =>
		css({
			width: "100%",
			[mobileMQ(theme)]: {
				display: "flex",
				gap: "10px",
				marginBottom: theme.spaces.sm,
			},
		}),

	nextStep: (theme: AppTheme) =>
		css({
			alignItems: "start",
			display: "flex",
			color: theme.colors.grey[500],
		}),
	nextStepContent: (theme: AppTheme) =>
		css({
			alignItems: "start",
			display: "flex",
		}),
	buttonContainer: (theme: Theme) =>
		css({
			width: "100%",
			display: "flex",
			justifyContent: "flex-end",
			alignItems: "center",
			gap: "1rem",
			[mobileMQ(theme)]: {
				flexDirection: "column-reverse",
				justifyContent: "center",
				alignItems: "center",
			},
		}),
};
const MyPropertyDetail = () => {
	const { t } = useTranslation();
	const navigate = useNavigate();
	const { unitId = "" } = useParams();
	const dispatch = useAppDispatch();
	const [nextStep, setNextStep] = React.useState<string>("");
	const { data } = useMyPropertyDetail(unitId);
	const currentStep = data?.step ?? StepsCompletion.STARTED;
	const unit: Partial<PropertyUnit> = data?.unit ?? {};
	const isOffline = !data?.id;
	const source = data?.source;
	const currentUser = shared.useCurrentUser();
	const nafathVerified = currentUser?.nafathVerified;
	const { data: featureFlagAPI } = useFeatureFlagApi();
	const enableCancelReservation = featureFlagAPI?.enableCancelReservation;
	const shouldCheckNafathID = featureFlagAPI?.checkNafathIDExpiry ?? false;
	const generateAppPath = useAppPathGenerator();
	const resident = shared.useInjection<shared.AccountService>(
		shared.AccountService,
	);
	const reservationData = useAppSelector((state) => state.reservation);
	const cancellationEligibility = data?.cancellationEligibility;
	const screeningResultStatus = data?.screeningResultStatus;
	const cantProceedAfterWCScreening = screeningResultStatus === false;
	const worldCheckFeatureFlag = featureFlagAPI?.enableWorldCheckScreening;
	const showIfScreeningError =
		cantProceedAfterWCScreening && worldCheckFeatureFlag && currentStep == 4;


	// Extract the Nafath ID expiration check into a separate function
	const checkNafathIdExpiry = useFreshCallback(async () => {
		try {
			const { nafath, isValidNationalIdPresent } =
				await resident.getFullProfile();

			if (!isValidNationalIdPresent) {
				return { id: nafath?.id, isExpired: true };
			}

			return { id: nafath?.id, isExpired: false };
		} catch (error) {
			console.error("Error checking expiration date", error);
			return { id: null, isExpired: true };
		}
	});

	// Generic navigation function that handles Nafath ID check
	const handleNavigation = useFreshCallback(async (path: string) => {
		// The below id variable can be used to send the the dispatch action of opening the nafath modal
		const { id, isExpired: isNafathIdExpired } = await checkNafathIdExpiry();
		if (shouldCheckNafathID && (isNafathIdExpired || !nafathVerified)) {
			dispatch(trackNafathExpiredError());
			dispatch(nafathIdExpireModalSlice.actions.openModal());
			return;
		}
		navigate(path);
	});

	const continueReservation = async () => {
		const path =
			data?.reservationProgress === ReservationProgress.IN_PROGRESS_PAYMENT ||
			data?.reservationProgress ===
				ReservationProgress.IN_PROGRESS_PAYMENT_CHECKOUT
				? generateAppPath(AppPaths.reservationOnboardInvoice, {
						communityName: unit.community?.toLowerCase() ?? "",
						groupId: unit.typology ?? "",
						unitId,
					})
				: generateAppPath(AppPaths.reservation, {
						communityName: unit.community?.toLowerCase() ?? "",
						groupId: unit.typology ?? "",
						unitId,
					});

		await handleNavigation(path);
	};

	const continueFunding = async () => {
		const path = generateAppPath(AppPaths.reservationFunding, {
			communityName: unit.community?.toLowerCase() ?? "",
			groupId: unit.typology ?? "",
			unitId,
		});
		await handleNavigation(path);
	};

	const continuePurchase = async () => {
		const path = generateAppPath(AppPaths.reservationPurchaseComplete, {
			communityName: unit.community?.toLowerCase() ?? "",
			groupId: unit.typology ?? "",
			unitId,
		});
		await handleNavigation(path);
	};

	const items: StepProps["items"] = [
		{
			content: (
				<ReservationItems
					showProgress={!isOffline}
					actionable={!isOffline}
					currentStep={currentStep}
					onContinueReservation={continueReservation}
					setNextStep={setNextStep}
				/>
			),
			onViewDetailClick: () => {
				dispatch(mainMenuSlice.actions.closeModal());
				navigate(
					generateAppPath(AppPaths.myPropertyReservationDetail, {
						unitId,
					}),
				);
			},
			showViewDetail: true,
			title: t("features.myProperties.propertyDetail.steps.reservation.title"),
		},
		{
			content: (
				<FinancingItems
					showProgress={!isOffline}
					actionable={!isOffline}
					currentStep={currentStep}
					onContinueReservation={continueFunding}
					setNextStep={setNextStep}
				/>
			),
			onViewDetailClick: () => {
				dispatch(mainMenuSlice.actions.closeModal());
				navigate(
					generateAppPath(AppPaths.myPropertyFinanceDetail, {
						unitId,
					}),
				);
			},
			showViewDetail: currentStep >= StepsCompletion.CONFIRM_FUNDING_METHOD,
			title: t("features.myProperties.propertyDetail.steps.finance.title"),
		},
		{
			content: (
				<PurchaseItems
					showProgress={!isOffline}
					actionable={!isOffline}
					currentStep={currentStep}
					onContinueReservation={continuePurchase}
					setNextStep={setNextStep}
				/>
			),
			onViewDetailClick: () => {
				dispatch(mainMenuSlice.actions.closeModal());
				navigate(
					generateAppPath(AppPaths.myPropertyPurchaseDetail, {
						unitId,
					}),
				);
			},
			showViewDetail: currentStep >= StepsCompletion.PURCHASE_E_SIGNATURE || data?.enableDownloadSPA === true,
			title: t("features.myProperties.propertyDetail.steps.purchase.title"),
		},
		// { title: t("features.myProperties.propertyDetail.steps.inspect.title") },
	];

	const continueReservationCallbackMap = {
		0: continueReservation,
		1: continueFunding,
		2: continuePurchase,
	};
	const showButton = hasStepsToComplete(currentStep);
	const cancellationStatus =
		reservationData?.data?.reservationCancellationStatus ??
		data?.reservationCancellationStatus;

	const isCancellationRequestSigned = [
		ReservationCancellationStatus.DOCUMENT_SIGNED_BY_USER,
	].includes(cancellationStatus!);

	const isCancellationRequestBeenSubmitted = Boolean(cancellationStatus);

	const effectiveCancellationEligibility =
		cancellationEligibility ||
		(isCancellationRequestBeenSubmitted && !isCancellationRequestSigned);

	const showCancellationButton =
		effectiveCancellationEligibility && !isCancellationRequestSigned;

	const handelCancelReservationDialog = () => {
		if (!!cancellationStatus && !isCancellationRequestSigned) {
			dispatch(reservationSlice.actions.setShowCancellationDocumentModal());
		}
		dispatch(reservationSlice.actions.setOpenCancelReservationDialog(true));
		dispatch(
			getReservationData({
				reservationId: data?.id,
				unitId: data?.unit?.id ?? "",
			}),
		);
	};

	return (
		<>
			<Step
				items={items}
				activeIndex={mapParentStep(currentStep)}
				defaultExpand
			/>

			{(showButton || showCancellationButton) && source === "ROSHNAPP" && (
				<div css={styles.wrapper}>
					<div css={styles.body}>
						{nextStep && (
							<div css={styles.steps}>
								<Typography css={styles.nextStep}>
									{t("features.myProperties.propertyDetail.nextStep")}:
								</Typography>
								<Typography css={styles.nextStepContent}>{nextStep}</Typography>
							</div>
						)}
						<div css={styles.buttonContainer}>
							{enableCancelReservation &&
								showCancellationButton &&
								!isCancellationRequestSigned && (
									<RDSButton
										css={styles.btn}
										text={t("pages.cancelReservation.requestCancellation")}
										variant="secondary"
										onClick={() => handelCancelReservationDialog()}
									/>
								)}

							{showButton && (
								<RDSButton
									css={styles.btn}
									variant="primary"
									size="md"
									onClick={() => {
										const currentStepIndex = mapParentStep(currentStep);
										const cb = continueReservationCallbackMap[currentStepIndex];
										if (cb) {
											cb();
										}
									}}
									text={t("features.propertyDiscovery.continue")}
									disabled={showIfScreeningError ? true : false}
								/>
							)}
						</div>
					</div>
				</div>
			)}

			{/* {currentStep >= StepsCompletion.PURCHASE_COMPLETED ? (
                <PaymentMilestone paymentPlan={paymentPlan} />
            ) : null} */}
		</>
	);
};

export { MyPropertyDetail };
