import { Typography } from "@/components/typography";
import { AppTheme, desktopMQ } from "@/theme";
import { css } from "@emotion/react";
import { useTranslation } from "react-i18next";
import { PropertyUnit } from "@/types/property-type";
import { useScreenSize } from "@/hooks/media-query-hooks";
import { UnitOverviewCard } from "@/features/unit-overview-card/unit-overview-card";

const styles = {
	cardWrapper: (theme: AppTheme) =>
		css({
			marginBottom: 0,
			[desktopMQ(theme)]: {
				marginBottom: theme.spaces.xl,
			},
		}),
	infoList: (theme: AppTheme) =>
		css({
			display: "none",
			["li"]: {
				":last-of-type": {
					marginBottom: 0,
				},
				alignItems: "center",
				display: "flex",
				justifyContent: "space-between",
				marginBottom: theme.spaces.md,
			},
			listStyle: "none",
			marginBottom: 0,

			padding: 0,
			[desktopMQ(theme)]: {
				display: "block",
			},
		}),
	listTitle: (theme: AppTheme) =>
		css({
			alignItems: "center",
			display: "none",
			justifyContent: "space-between",
			[desktopMQ(theme)]: {
				display: "flex",
			},
		}),
	title: (theme: AppTheme) =>
		css({
			lineHeight: theme.spaces.xl,
		}),
	wrapper: (theme: AppTheme) =>
		css({
			background: theme.colors.background.default,
			borderRadius: 0,
			padding: 0,
			[desktopMQ(theme)]: {
				background: theme.colors.background.secondary,
				borderRadius: theme.borderRadius.sm,
				padding: theme.spaces.lg,
			},
		}),
};

export type MyPropertyDetailCardProps = Readonly<{
	exteriorPreview?: string;
	handoverDate: string | undefined;
	showConstructionProgress?: boolean;
	unit: PropertyUnit;
	latestHandoverDateFlag?: boolean;
}>;

function MyPropertyDetailCard({
	unit,
	exteriorPreview,
	handoverDate,
	showConstructionProgress = false,
	latestHandoverDateFlag = false,
}: MyPropertyDetailCardProps) {
	const { t } = useTranslation();
	const { isDesktop } = useScreenSize();
	const propertyInfo = unit?.unitInfo ?? {};

	return (
		<div css={styles.wrapper}>
			<div css={styles.cardWrapper}>
				<UnitOverviewCard
					community={unit?.community}
					thumbnail={exteriorPreview}
					unitName={unit?.unitName}
					bedrooms={propertyInfo?.bedroom}
					bathrooms={propertyInfo?.bathroom}
					estDeliveryDate={handoverDate}
					price={unit?.reservationPrice?.price ?? unit?.unitPrice?.price}
					note={propertyInfo.note}
					squareSpace={propertyInfo.plotArea}
					type={unit?.unitType}
					typologyGroup={propertyInfo.typologyGroup}
					facade={propertyInfo.facade}
					latestHandoverDateFlag={latestHandoverDateFlag}
					orientation={unit?.orientation}
					unitId={unit?.unitCode}
				/>
			</div>
			{isDesktop ? (
				<>
					<div css={styles.listTitle}>
						<Typography css={styles.title} variant="subtitleL">
							{t("features.propertyDiscovery.propertyInformation")}
						</Typography>
					</div>
					<ul css={styles.infoList}>
						<li>
							<Typography variant="subtitleS">
								{t("features.propertyDiscovery.plotArea")}
							</Typography>
							<Typography variant="bodyS">
								{`${propertyInfo.plotArea ?? "N/A"} ${propertyInfo.plotArea ? t("common.sqm") : ""}`.trim()}
							</Typography>
						</li>
						<li>
							<Typography variant="subtitleS">
								{t("features.propertyDiscovery.grossFloorArea")}
							</Typography>
							<Typography variant="bodyS">
								{`${propertyInfo.grossFloorArea ?? "N/A"} ${propertyInfo.grossFloorArea ? t("common.sqm") : ""}`.trim()}
							</Typography>
						</li>
						{/* <li>
							<Typography variant="subtitleS">
								{t("features.propertyDiscovery.builtUpArea")}
							</Typography>
							<Typography variant="bodyS">
								{propertyInfo.builtUpArea
									? `${propertyInfo.builtUpArea} ${t("common.sqm")}`
									: "N/A"}
							</Typography>
						</li> */}
						<li>
							<Typography variant="subtitleS">
								{t("features.propertyDiscovery.bedroom")}
							</Typography>
							<Typography variant="bodyS">
								{propertyInfo.bedroom ?? 0}
							</Typography>
						</li>
						<li>
							<Typography variant="subtitleS">
								{t("features.propertyDiscovery.roofTerrace")}
							</Typography>
							<Typography variant="bodyS">
								{propertyInfo.roofTerrace ? t("common.yes") : t("common.no")}
							</Typography>
						</li>
						<li>
							<Typography variant="subtitleS">
								{t("features.propertyDiscovery.additionalRooms")}
							</Typography>
							<Typography variant="bodyS">
								{propertyInfo.additionalRooms ?? 0}
							</Typography>
						</li>
						{propertyInfo.note ? (
							<li>
								<Typography variant="subtitleS">
									{t(propertyInfo.note)}
								</Typography>
							</li>
						) : null}
					</ul>
				</>
			) : null}
		</div>
	);
}

export { MyPropertyDetailCard };
