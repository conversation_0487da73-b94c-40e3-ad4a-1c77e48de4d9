import { useEffect, useRef, useState } from "react";
import { useTheme, css } from "@emotion/react";

type NationalDayVideoProps = {
	onVideoEnd: () => void;
};

const NATIONAL_DAY_STORAGE_KEY = "roshn_national_day_video_shown";

export const NationalDayVideo = ({ onVideoEnd }: NationalDayVideoProps) => {
	const theme = useTheme();
	const videoRef = useRef<HTMLVideoElement>(null);
	const [isVideoLoaded, setIsVideoLoaded] = useState(false);
	const [isFadingOut, setIsFadingOut] = useState(false);
	const isMobile = theme?.rds?.device === "mobile";

	const videoSrc = isMobile
		? "https://alb-home.roshn.sa/National_Day_Outro_5_Vertical_56d663b439/National_Day_Outro_5_Vertical_56d663b439.mp4"
		: "https://alb-home.roshn.sa/National_Day_Outro_5_Horizontal_c19ea19d87/National_Day_Outro_5_Horizontal_c19ea19d87.mp4";

	const styles = {
		videoContainer: css({
			position: "fixed",
			top: 0,
			left: 0,
			width: "100vw",
			height: "100vh",
			zIndex: 9999,
			display: "flex",
			justifyContent: "center",
			alignItems: "center",
			opacity: isFadingOut ? 0 : 1,
			transition: "opacity 1s ease-in-out",
		}),
		video: css({
			width: "100%",
			height: "100%",
			objectFit: "cover",
			opacity: isVideoLoaded ? 1 : 0,
			transition: "opacity 1s ease-in-out",
		}),
	};

	useEffect(() => {
		const video = videoRef.current;
		if (!video) return;

		const handleLoadedData = () => {
			setIsVideoLoaded(true);
		};

		const handleEnded = () => {
			setIsFadingOut(true);
			sessionStorage.setItem(NATIONAL_DAY_STORAGE_KEY, "true");

			setTimeout(() => {
				onVideoEnd();
			}, 1000);
		};

		const handleError = () => {
			console.error("National Day video failed to load");
			onVideoEnd();
		};

		video.addEventListener("loadeddata", handleLoadedData);
		video.addEventListener("ended", handleEnded);
		video.addEventListener("error", handleError);

		video.play().catch((error) => {
			console.error("Failed to play National Day video:", error);
			onVideoEnd();
		});

		return () => {
			video.removeEventListener("loadeddata", handleLoadedData);
			video.removeEventListener("ended", handleEnded);
			video.removeEventListener("error", handleError);
		};
	}, [onVideoEnd]);

	return (
		<div css={styles.videoContainer}>
			<video
				ref={videoRef}
				css={styles.video}
				src={videoSrc}
				muted
				playsInline
				preload="auto"
			/>
		</div>
	);
};

export const shouldShowNationalDayVideo = (): boolean => {
	if (typeof window === "undefined") return false;

	const hasShownVideo = sessionStorage.getItem(NATIONAL_DAY_STORAGE_KEY);
	return !hasShownVideo;
};
