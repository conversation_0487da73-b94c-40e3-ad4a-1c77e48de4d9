import { COMMUNITY_NAMES, PROJECT_NAMES } from "@/constants";
import * as Layers from "../layers";
import * as ac from "@/theme/palette/all-colors";
import { CSSObject } from "@emotion/react";

const isRebrandedFlag = import.meta.env.VITE_FF_REBRANDED === "TRUE";

export const overlayLayerStyles = {
	[`${COMMUNITY_NAMES.ALAROUS}-${PROJECT_NAMES.ALAROUS_1A}`]: {
		activeTiles: {
			[Layers.CssVars.FillOpacity]: 0.5,
			[Layers.CssVars.StrokeOpacity]: 1,
			[Layers.CssVars.FillColor]: ac.GoldenRod500,
			[Layers.CssVars.StrokeColor]: ac.GoldenRod200,
			[Layers.CssVars.StrokeWidth]: 2,
		} as CSSObject,

		availableTiles: {
			[Layers.CssVars.FillColor]: isRebrandedFlag ? ac.ForestGreen : ac.LightGreen,
			[Layers.CssVars.StrokeColor]: ac<PERSON><PERSON>,
			[Layers.CssVars.HoverColor]: ac.GoldenRod500,
			[Layers.CssVars.HoverStrokeColor]: ac.GoldenRod200,

			[Layers.CssVars.FillOpacity]: 0.7,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 0.5,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]: 1,
			[Layers.CssVars.HoverStrokeWidth]: 2,
		} as CSSObject,
		color: Layers.markerStyles.color.fill,
		markerSize: Layers.markerStyles.size.lg,
		toggleSize: Layers.markerStyles.size.md,
		unitLayers: {
			[Layers.CssVars.FillOpacity]: 0,
			[Layers.CssVars.StrokeOpacity]: 0,
			[Layers.CssVars.HoverFillOpacity]: 0,
			[Layers.CssVars.HoverStrokeOpacity]: 0,
		} as React.CSSProperties,
	},
	[`${COMMUNITY_NAMES.SEDRA}-${PROJECT_NAMES.SEDRA_4A}`]: {
		activeTiles: {
			[Layers.CssVars.FillOpacity]: 0.5,
			[Layers.CssVars.StrokeOpacity]: 1,
			[Layers.CssVars.FillColor]: ac.GoldenRod500,
			[Layers.CssVars.StrokeColor]: ac.GoldenRod200,
			[Layers.CssVars.StrokeWidth]: 2,
		} as CSSObject,

		availableTiles: {
			[Layers.CssVars.FillColor]: isRebrandedFlag ? ac.ForestGreen : ac.LightGreen,
			[Layers.CssVars.StrokeColor]: ac.White,
			[Layers.CssVars.HoverColor]: ac.GoldenRod500,
			[Layers.CssVars.HoverStrokeColor]: ac.GoldenRod200,

			[Layers.CssVars.FillOpacity]: 0.7,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 0.5,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]: 1,
			[Layers.CssVars.HoverStrokeWidth]: 2,
		} as CSSObject,
		color: Layers.markerStyles.color.fill,
		markerSize: Layers.markerStyles.size.lg,
		toggleSize: Layers.markerStyles.size.md,
		unitLayers: {
			[Layers.CssVars.FillOpacity]: 0,
			[Layers.CssVars.StrokeOpacity]: 0,
			[Layers.CssVars.HoverFillOpacity]: 0,
			[Layers.CssVars.HoverStrokeOpacity]: 0,
		} as React.CSSProperties,
	},
	[`${COMMUNITY_NAMES.SEDRA}-${PROJECT_NAMES.SEDRA_3}`]: {
		activeTiles: {
			[Layers.CssVars.FillOpacity]: 0.5,
			[Layers.CssVars.StrokeOpacity]: 1,
			[Layers.CssVars.FillColor]: ac.GoldenRod500,
			[Layers.CssVars.StrokeColor]: ac.GoldenRod200,
			[Layers.CssVars.StrokeWidth]: 0.2,
		} as CSSObject,

		availableTiles: {
			[Layers.CssVars.FillColor]: isRebrandedFlag ? ac.ForestGreen : ac.LightGreen,
			[Layers.CssVars.StrokeColor]: ac.White,
			[Layers.CssVars.HoverColor]: ac.GoldenRod500,
			[Layers.CssVars.HoverStrokeColor]: ac.GoldenRod200,

			[Layers.CssVars.FillOpacity]: 0.7,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 0.5,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]: 0.1,
			[Layers.CssVars.HoverStrokeWidth]: 0.2,
		} as CSSObject,
		color: Layers.markerStyles.color.fill,
		markerSize: Layers.markerStyles.size.xl1,
		toggleSize: Layers.markerStyles.size.md,
		unitLayers: {
			[Layers.CssVars.FillOpacity]: 0,
			[Layers.CssVars.StrokeOpacity]: 0,
			[Layers.CssVars.HoverFillOpacity]: 0,
			[Layers.CssVars.HoverStrokeOpacity]: 0,
		} as React.CSSProperties,
	},
	[`${COMMUNITY_NAMES.ALDANAH}-${PROJECT_NAMES.ALDANAH_1}`]: {
		activeTiles: {
			[Layers.CssVars.FillOpacity]: 0.5,
			[Layers.CssVars.StrokeOpacity]: 1,
			[Layers.CssVars.FillColor]: ac.GoldenRod500,
			[Layers.CssVars.StrokeColor]: ac.GoldenRod200,
			[Layers.CssVars.StrokeWidth]: 2,
		} as CSSObject,

		availableTiles: {
			[Layers.CssVars.FillColor]: isRebrandedFlag ? ac.ForestGreen : ac.LightGreen,
			[Layers.CssVars.StrokeColor]: ac.White,
			[Layers.CssVars.HoverColor]: ac.GoldenRod500,
			[Layers.CssVars.HoverStrokeColor]: ac.GoldenRod200,

			[Layers.CssVars.FillOpacity]: 0.7,
			[Layers.CssVars.StrokeOpacity]: 0.5,

			[Layers.CssVars.HoverFillOpacity]: 0.5,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]: 1,
			[Layers.CssVars.HoverStrokeWidth]: 2,
		} as CSSObject,
		color: Layers.markerStyles.color.fill,
		markerSize: Layers.markerStyles.size.lg,
		toggleSize: Layers.markerStyles.size.md,
		unitLayers: {
			[Layers.CssVars.FillOpacity]: 0,
			[Layers.CssVars.StrokeOpacity]: 0,
			[Layers.CssVars.HoverFillOpacity]: 0,
			[Layers.CssVars.HoverStrokeOpacity]: 0,
		} as React.CSSProperties,
	},
	[`${COMMUNITY_NAMES.WAREFA}-${PROJECT_NAMES.WAREFA_1}`]: {
		activeTiles: {
			[Layers.CssVars.FillOpacity]: 0.5,
			[Layers.CssVars.StrokeOpacity]: 1,
			[Layers.CssVars.FillColor]: ac.GoldenRod500,
			[Layers.CssVars.StrokeColor]: ac.GoldenRod200,
			[Layers.CssVars.StrokeWidth]: 0.2,
		} as CSSObject,

		availableTiles: {
			[Layers.CssVars.FillColor]: isRebrandedFlag ? ac.ForestGreen : ac.LightGreen,
			[Layers.CssVars.StrokeColor]: ac.White,
			[Layers.CssVars.HoverColor]: ac.GoldenRod500,
			[Layers.CssVars.HoverStrokeColor]: ac.GoldenRod200,

			[Layers.CssVars.FillOpacity]: 0.7,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 0.5,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]: 0.1,
			[Layers.CssVars.HoverStrokeWidth]: 0.2,
		} as CSSObject,
		color: Layers.markerStyles.color.fill,
		markerSize: Layers.markerStyles.size.xl1,
		toggleSize: Layers.markerStyles.size.md,
		unitLayers: {
			[Layers.CssVars.FillOpacity]: 0,
			[Layers.CssVars.StrokeOpacity]: 0,
			[Layers.CssVars.HoverFillOpacity]: 0,
			[Layers.CssVars.HoverStrokeOpacity]: 0,
		} as React.CSSProperties,
	},
	[`${COMMUNITY_NAMES.SEDRA}-${PROJECT_NAMES.SEDRA_2A}`]: {
		activeTiles: {
			[Layers.CssVars.FillOpacity]: 0.5,
			[Layers.CssVars.StrokeOpacity]: 1,
			[Layers.CssVars.FillColor]: ac.GoldenRod500,
			[Layers.CssVars.StrokeColor]: ac.GoldenRod200,
			[Layers.CssVars.StrokeWidth]: 0.2,
		} as CSSObject,

		availableTiles: {
			[Layers.CssVars.FillColor]: isRebrandedFlag ? ac.ForestGreen : ac.LightGreen,
			[Layers.CssVars.StrokeColor]: ac.White,
			[Layers.CssVars.HoverColor]: ac.GoldenRod500,
			[Layers.CssVars.HoverStrokeColor]: ac.GoldenRod200,

			[Layers.CssVars.FillOpacity]: 0.7,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 0.5,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]: 1,
			[Layers.CssVars.HoverStrokeWidth]: 0.2,
		} as CSSObject,
		color: Layers.markerStyles.color.fill,
		markerSize: Layers.markerStyles.size.xl1,
		toggleSize: Layers.markerStyles.size.md,
		unitLayers: {
			[Layers.CssVars.FillOpacity]: 0,
			[Layers.CssVars.StrokeOpacity]: 0,
			[Layers.CssVars.HoverFillOpacity]: 0,
			[Layers.CssVars.HoverStrokeOpacity]: 0,
		} as React.CSSProperties,
	},
	[`${COMMUNITY_NAMES.ALMANAR}-${PROJECT_NAMES.ALMANAR_1}`]: {
		activeTiles: {
			[Layers.CssVars.FillOpacity]: 0.5,
			[Layers.CssVars.StrokeOpacity]: 1,
			[Layers.CssVars.FillColor]: ac.GoldenRod500,
			[Layers.CssVars.StrokeColor]: ac.GoldenRod200,
			[Layers.CssVars.StrokeWidth]: 0.2,
		} as CSSObject,

		availableTiles: {
			[Layers.CssVars.FillColor]: isRebrandedFlag ? ac.ForestGreen : ac.LightGreen,
			[Layers.CssVars.StrokeColor]: ac.White,
			[Layers.CssVars.HoverColor]: ac.GoldenRod500,
			[Layers.CssVars.HoverStrokeColor]: ac.GoldenRod200,

			[Layers.CssVars.FillOpacity]: 0.7,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 0.5,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]: 1,
			[Layers.CssVars.HoverStrokeWidth]: 2,
		} as CSSObject,
		color: Layers.markerStyles.color.fill,
		markerSize: Layers.markerStyles.size.xl1,
		toggleSize: Layers.markerStyles.size.md,
		unitLayers: {
			[Layers.CssVars.FillOpacity]: 0,
			[Layers.CssVars.StrokeOpacity]: 0,
			[Layers.CssVars.HoverFillOpacity]: 0,
			[Layers.CssVars.HoverStrokeOpacity]: 0,
		} as React.CSSProperties,
	},
	[`${COMMUNITY_NAMES.SEDRA}-${PROJECT_NAMES.SEDRA_5}`]: {
		activeTiles: {
			[Layers.CssVars.FillOpacity]: 0.5,
			[Layers.CssVars.StrokeOpacity]: 1,
			[Layers.CssVars.FillColor]: ac.GoldenRod500,
			[Layers.CssVars.StrokeColor]: ac.GoldenRod200,
			[Layers.CssVars.StrokeWidth]: 0.2,
		} as CSSObject,

		availableTiles: {
			[Layers.CssVars.FillColor]: isRebrandedFlag ? ac.ForestGreen : ac.LightGreen,
			[Layers.CssVars.StrokeColor]: ac.White,
			[Layers.CssVars.HoverColor]: ac.GoldenRod500,
			[Layers.CssVars.HoverStrokeColor]: ac.GoldenRod200,

			[Layers.CssVars.FillOpacity]: 0.7,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 0.5,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]: 1,
			[Layers.CssVars.HoverStrokeWidth]: 0.2,
		} as CSSObject,
		color: Layers.markerStyles.color.fill,
		markerSize: Layers.markerStyles.size.xl1,
		toggleSize: Layers.markerStyles.size.md,
		unitLayers: {
			[Layers.CssVars.FillOpacity]: 0,
			[Layers.CssVars.StrokeOpacity]: 0,
			[Layers.CssVars.HoverFillOpacity]: 0,
			[Layers.CssVars.HoverStrokeOpacity]: 0,
		} as React.CSSProperties,
	},
};
