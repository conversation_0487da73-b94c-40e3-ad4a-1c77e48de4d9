import { useParams } from "@remix-run/react";
import { MasterPlanZone } from "./view-zone";
import { ScalaType, useStateParams } from "../use-state-params";
import { useFreshCallback } from "rooks";
import { HouseUnit } from "../data/schema";
import { DefaultUnitIdKey } from "../open-with-default-unit";
import * as React from "react";
import * as MapView from "@/components/map-view";
import * as Schema from "../data/schema";
import {
	trackFilterBathrooms,
	trackFilterBedrooms,
	trackFilterPrice,
	trackFloorPlanClick,
	trackPropertyArchetype,
	trackSelectProperty,
	trackViewMasterplaneCommunityZone,
	trackZoomMasterplaneCommunityZone,
	trackPhaseFilterChanges
} from "../redux/actions";
import { useDispatch } from "react-redux";
import { DEFAULT_CHOICE } from "@/constants";
import {
	mapPropertyTypologiesToTypologies,
	mapUnitTypesToTypologies,
} from "./container-zone.mapper";
import { useMasterPlan } from "../context";
import { Navigate } from "@/utils/navigate";
import { AppPaths, useAppPathGenerator } from "@/routes";
import type { ParamParseKey } from "@remix-run/router";
import { useIsSaleAdvisor } from "@/features/sales-advisor/use-is-sale-advisor";
import { useInjection } from "@roshn/shared";
import { useFeatureFlagApi } from "@/features/feature-flags";
import { ZoneViewService } from "@/services/zone-view";

export const DefaultParams = {
	[DefaultUnitIdKey]: undefined as string | undefined,

	bathrooms: [DEFAULT_CHOICE],
	// unit filters
	bedrooms: [DEFAULT_CHOICE],
	priceRanges: [DEFAULT_CHOICE],
	showAmenities: true,
	showEducations: true,
	showGrandMosques: true,
	showNccs: true,
	showParks: true,
	showPolices: true,
	showRetails: true,

	typologies: undefined as string[] | undefined,
	unitTypes: [DEFAULT_CHOICE],
};

export const MasterPlanZoneContainer = () => {
	const [state, setParamsState] = useStateParams({
		defaultParams: DefaultParams,
		schema: {
			bathrooms: ScalaType.ArrayString,
			bedrooms: ScalaType.ArrayString,
			priceRanges: ScalaType.ArrayString,
			defaultUnitId: ScalaType.String,
			typologies: ScalaType.ArrayString,
			unitTypes: ScalaType.ArrayString,
		},
	});

	const {
		communityName = "",
		zone = "",
		locale = "en",
		projectName = "",
	} = useParams<
		ParamParseKey<
			typeof AppPaths.masterPlanZone | typeof AppPaths.masterPlanZoneStandalone
		>
	>();
	const dispatch = useDispatch();

	const {
		useZoneData,
		getPropertyGroup,
		onCommunityClick,
		reservationLoading,
		onReservationClick,
		isLoggedIn,
		onOpenUnitDetail,
	} = useMasterPlan();
	let crmProjectName = "";
	if (projectName === "sedra4") {
		crmProjectName = "sedra_4a";
	} else {
		crmProjectName = projectName;
	}

	const { data, propertyTypologies } = useZoneData(
		communityName,
		zone,
		crmProjectName,
	);
	const [defaultUnitId, handleDefaultUnitIdChange] = [
		state[DefaultUnitIdKey],
		useFreshCallback(
			(
				target: HTMLElement,
				unit?: HouseUnit,
				setUnitDetail?: (arg: {
					position: MapView.Coordinates;
					unit?: Schema.HouseUnit;
				}) => void,
			) => {
				setParamsState({ [DefaultUnitIdKey]: unit?.unitCode });

				onOpenUnitDetail({
					communityName,
					data: data!,
					el: target,
					handleGetPropertyGroup: getPropertyGroup,
					locale,
					setUnitDetail,
					tileId: unit?.unitCode ?? "",
					trackSelectProperty: (unitCode) =>
						dispatch(trackSelectProperty(unitCode)),
				});
			},
		),
	];

	const [bedrooms, handleBedroomsChange] = [
		state.bedrooms,
		useFreshCallback((value: string[]) => {
			dispatch(trackFilterBedrooms(value));
			setParamsState({ bedrooms: value });
		}),
	];

	const [priceRanges, handlePriceFilterChange] = [
		state.priceRanges,
		useFreshCallback((value: any) => {
			dispatch(trackFilterPrice(value));
			setParamsState({ priceRanges: value });
		}),
	];

	const [bathrooms, handleBathroomsChange] = [
		state.bathrooms,
		useFreshCallback((value: string[]) => {
			dispatch(trackFilterBathrooms(value));
			setParamsState({ bathrooms: value });
		}),
	];

	const [unitTypes, handleUnitTypesChange] = [
		state.unitTypes,
		useFreshCallback((value: string[]) => {
			dispatch(trackPropertyArchetype(value));
			setParamsState({
				typologies: mapUnitTypesToTypologies(propertyTypologies, value),
				unitTypes: value,
			});
		}),
	];

	const [typologies, handleTypologiesChange] = [
		state.typologies,
		useFreshCallback((value: string[]) => {
			setParamsState({
				typologies: value,
			});
		}),
	];

	const handleShowAllAmenitiesChange = useFreshCallback((value: boolean) =>
		setParamsState({
			showAmenities: value,
			showEducations: value,
			showGrandMosques: value,
			showNccs: value,
			showParks: value,
			showPolices: value,
			showRetails: value,
		}),
	);

	const [showGrandMosques, handleShowGrandMosquesChange] = [
		state.showGrandMosques,
		useFreshCallback((value: boolean) => {
			if (value) {
				dispatch(trackPhaseFilterChanges("Grand mosques"));
			}
			setParamsState({ showGrandMosques: value });
		}),
	];

	const [showEducations, handleShowEducationsChange] = [
		state.showEducations,
		useFreshCallback((value: boolean) => {
			if (value) {
				dispatch(trackPhaseFilterChanges("Education"));
			}
			setParamsState({ showEducations: value });
		}),
	];

	const [showAmenities, handleShowAmenitiesChange] = [
		state.showAmenities,
		useFreshCallback((value: boolean) => {
			if (value) {
				dispatch(trackPhaseFilterChanges("Amenities"));
			}
			setParamsState({ showAmenities: value });
		}),
	];

	const [showRetails, handleShowRetailsChange] = [
		state.showRetails,
		useFreshCallback((value: boolean) =>{
			if (value) {
			dispatch(trackPhaseFilterChanges("Retails"))
		}
			setParamsState({ showRetails: value });
}),
	];

	const [showParks, handleShowParksChange] = [
		state.showParks,
		useFreshCallback((value: boolean) => {
			if (value) {
				dispatch(trackPhaseFilterChanges("Parks"))
			}
			setParamsState({ showParks: value });
		}),
	];

	const [showNccs, handleShowNccsChange] = [
		state.showNccs,
		useFreshCallback((value: boolean) => {
			if (value) {
				dispatch(trackPhaseFilterChanges("NCCS"))
			}
			setParamsState({ showNccs: value });
		}),
	];

	const [showPolices, handleShowPolicesChange] = [
		state.showPolices,
		useFreshCallback((value: boolean) => {
			if (value) {
				dispatch(trackPhaseFilterChanges("Police"))
			}
			setParamsState({ showPolices: value });
		}),
	];

	const handleOnZoom = () => {
		dispatch(trackZoomMasterplaneCommunityZone(communityName, zone));
	};


	// Fetch Zone View Data from strapi
	let currentProjectName;
	const [compassValue, setCompassValue] = React.useState(30);
	const [unitLabelPositions, setUnitLabelPositions] = React.useState([]);
	currentProjectName = (projectName === "sedra4") ? "sedra_4a" : projectName;
	let currentZone = currentProjectName.toUpperCase() + "_ZONE_" + zone.toUpperCase();
	const zoneViewService = useInjection<ZoneViewService>(ZoneViewService);
	const zoneViewFetch = React.useCallback(async () => {
		const response = await zoneViewService.getAllZoneViews(currentZone);
		const check = response[0].attributes;
		setCompassValue(check.compassRotation);
		setUnitLabelPositions(check.unitLabelPosition);
	}, [projectName, zone]);

	React.useEffect(() => {
		zoneViewFetch()
	}, [zoneViewFetch]);

	
	React.useEffect(() => {
		dispatch(trackViewMasterplaneCommunityZone(communityName, zone));
	}, []);

	const generateAppPath = useAppPathGenerator();

	const isSaleAdvisor = useIsSaleAdvisor();

	// Wait for feature flags to load before deciding navigation
	const { isLoading } = useFeatureFlagApi();
	if (isLoading) {
		return null;
	}

	if (!data) {
		return <Navigate to={generateAppPath(AppPaths.home)} />;
	}

	data['unitLabel'] = unitLabelPositions;

	return (
		<MasterPlanZone
			data={data}
			isLoggedIn={isLoggedIn}
			defaultUnitId={defaultUnitId}
			isSaleAdvisor={isSaleAdvisor}
			onSelectedUnitChanged={handleDefaultUnitIdChange}
			onShowAllAmenitiesChange={handleShowAllAmenitiesChange}
			//
			showGrandMosques={showGrandMosques}
			onShowGrandMosquesChange={handleShowGrandMosquesChange}
			showEducations={showEducations}
			onShowEducationsChange={handleShowEducationsChange}
			showAmenities={showAmenities}
			onShowAmenitiesChange={handleShowAmenitiesChange}
			showRetails={showRetails}
			onShowRetailsChange={handleShowRetailsChange}
			showParks={showParks}
			onShowParksChange={handleShowParksChange}
			showNccs={showNccs}
			onShowNccsChange={handleShowNccsChange}
			showPolices={showPolices}
			onShowPolicesChange={handleShowPolicesChange}
			//
			bedrooms={bedrooms}
			onBedroomsChange={handleBedroomsChange}
			priceRanges={priceRanges}
			handlePriceFilterChange={handlePriceFilterChange}
			bathrooms={bathrooms}
			onBathroomsChange={handleBathroomsChange}
			unitTypes={unitTypes}
			onUnitTypesChange={handleUnitTypesChange}
			typologies={typologies}
			typologyChoices={mapPropertyTypologiesToTypologies(propertyTypologies)}
			onTypologiesChange={handleTypologiesChange}
			startingReservation={reservationLoading}
			onReservationClick={onReservationClick}
			handleGetPropertyGroup={getPropertyGroup}
			onZoom={handleOnZoom}
			onFloorPlanClick={() => {
				dispatch(trackFloorPlanClick());
			}}
			onGoBack={() => onCommunityClick(crmProjectName)}
			compassRotationValue={compassValue}
			unitLabelPosition={unitLabelPositions}
		/>
	);
};
