/* eslint-disable sort-keys-fix/sort-keys-fix */
import { COMMUNITY_NAMES, PROJECT_NAMES } from "@/constants";
import * as Layers from "../layers";
import * as ac from "@/theme/palette/all-colors";
import { CSSObject } from "@emotion/react";

export const unitLabelLayerStyles = {
	[`${COMMUNITY_NAMES.ALAROUS}-${PROJECT_NAMES.ALAROUS_1A}`]: {
		activeTiles: {
			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.StrokeWidth]: 2,
		} as CSSObject,

		availableTiles: {
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.HoverColor]: ac.Transparent,
			[Layers.CssVars.HoverStrokeColor]: ac.Transparent,

			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]: 2,
			[Layers.CssVars.HoverStrokeWidth]: 2,
		} as CSSObject,

		hideTiles:{
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.HoverColor]: ac.Transparent,
			[Layers.CssVars.HoverStrokeColor]: ac.Transparent,

			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]: 2,
			[Layers.CssVars.HoverStrokeWidth]: 2,

		} as CSSObject,
		unavailableTiles: {
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.HoverColor]: ac.Transparent,
			[Layers.CssVars.HoverStrokeColor]: ac.Transparent,

			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]: 2,
			[Layers.CssVars.HoverStrokeWidth]: 2,

		} as CSSObject,

		unreleasedTiles:{
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.HoverColor]: ac.Transparent,
			[Layers.CssVars.HoverStrokeColor]: ac.Transparent,

			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]: 2,
			[Layers.CssVars.HoverStrokeWidth]: 2,

		} as CSSObject,
		color: Layers.markerStyles.color.fill,
		markerSize: Layers.markerStyles.size.lg,
		toggleSize: Layers.markerStyles.size.md,
		unitLayers: {
			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,
			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,
		} as React.CSSProperties,
	},
	[`${COMMUNITY_NAMES.SEDRA}-${PROJECT_NAMES.SEDRA_4A}`]: {
		activeTiles: {
			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.StrokeWidth]: 2,
		} as CSSObject,

		availableTiles: {
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.HoverColor]: ac.Transparent,
			[Layers.CssVars.HoverStrokeColor]: ac.Transparent,

			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]: 2,
			[Layers.CssVars.HoverStrokeWidth]: 2,
		} as CSSObject,

		hideTiles:{
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.HoverColor]: ac.Transparent,
			[Layers.CssVars.HoverStrokeColor]: ac.Transparent,

			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]: 2,
			[Layers.CssVars.HoverStrokeWidth]: 2,

		} as CSSObject,
		unavailableTiles: {
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.HoverColor]: ac.Transparent,
			[Layers.CssVars.HoverStrokeColor]: ac.Transparent,

			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]: 2,
			[Layers.CssVars.HoverStrokeWidth]: 2,

		} as CSSObject,

		unreleasedTiles:{
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.HoverColor]: ac.Transparent,
			[Layers.CssVars.HoverStrokeColor]: ac.Transparent,

			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]: 2,
			[Layers.CssVars.HoverStrokeWidth]: 2,

		} as CSSObject,
		color: Layers.markerStyles.color.fill,
		markerSize: Layers.markerStyles.size.lg,
		toggleSize: Layers.markerStyles.size.md,
		unitLayers: {
			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,
			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,
		} as React.CSSProperties,
	},
	[`${COMMUNITY_NAMES.ALDANAH}-${PROJECT_NAMES.ALDANAH_1}`]: {
		activeTiles: {
			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.StrokeWidth]: 2,
		} as CSSObject,

		availableTiles: {
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.HoverColor]: ac.Transparent,
			[Layers.CssVars.HoverStrokeColor]: ac.Transparent,

			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]: 2,
			[Layers.CssVars.HoverStrokeWidth]: 2,
		} as CSSObject,

		hideTiles:{
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.HoverColor]: ac.Transparent,
			[Layers.CssVars.HoverStrokeColor]: ac.Transparent,

			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]: 2,
			[Layers.CssVars.HoverStrokeWidth]: 2,

		} as CSSObject,
		unavailableTiles: {
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.HoverColor]: ac.Transparent,
			[Layers.CssVars.HoverStrokeColor]: ac.Transparent,

			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]: 2,
			[Layers.CssVars.HoverStrokeWidth]: 2,

		} as CSSObject,

		unreleasedTiles:{
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.HoverColor]: ac.Transparent,
			[Layers.CssVars.HoverStrokeColor]: ac.Transparent,

			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]: 2,
			[Layers.CssVars.HoverStrokeWidth]: 2,

		} as CSSObject,
		color: Layers.markerStyles.color.fill,
		markerSize: Layers.markerStyles.size.lg,
		toggleSize: Layers.markerStyles.size.md,
		unitLayers: {
			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,
			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,
		} as React.CSSProperties,
	},
	[`${COMMUNITY_NAMES.SEDRA}-${PROJECT_NAMES.SEDRA_3}`]: {
		activeTiles: {
			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.StrokeWidth]:0.2,
		} as CSSObject,

		availableTiles: {
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.HoverColor]: ac.Transparent,
			[Layers.CssVars.HoverStrokeColor]: ac.Transparent,

			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]:0.2,
			[Layers.CssVars.HoverStrokeWidth]:0.2,
		} as CSSObject,

		hideTiles:{
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.HoverColor]: ac.Transparent,
			[Layers.CssVars.HoverStrokeColor]: ac.Transparent,

			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]:0.2,
			[Layers.CssVars.HoverStrokeWidth]:0.2,

		} as CSSObject,
		unavailableTiles: {
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.HoverColor]: ac.Transparent,
			[Layers.CssVars.HoverStrokeColor]: ac.Transparent,

			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]:0.2,
			[Layers.CssVars.HoverStrokeWidth]:0.2,

		} as CSSObject,

		unreleasedTiles:{
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.HoverColor]: ac.Transparent,
			[Layers.CssVars.HoverStrokeColor]: ac.Transparent,

			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]:0.2,
			[Layers.CssVars.HoverStrokeWidth]:0.2,

		} as CSSObject,
		color: Layers.markerStyles.color.fill,
		markerSize: Layers.markerStyles.size.lg,
		toggleSize: Layers.markerStyles.size.md,
		unitLayers: {
			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,
			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,
		} as React.CSSProperties,
	},
	[`${COMMUNITY_NAMES.WAREFA}-${PROJECT_NAMES.WAREFA_1}`]: {
		activeTiles: {
			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.StrokeWidth]:0.2,
		} as CSSObject,

		availableTiles: {
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.HoverColor]: ac.Transparent,
			[Layers.CssVars.HoverStrokeColor]: ac.Transparent,

			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]:0.2,
			[Layers.CssVars.HoverStrokeWidth]:0.2,
		} as CSSObject,

		hideTiles:{
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.HoverColor]: ac.Transparent,
			[Layers.CssVars.HoverStrokeColor]: ac.Transparent,

			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]:0.2,
			[Layers.CssVars.HoverStrokeWidth]:0.2,

		} as CSSObject,
		unreleasedTiles:{
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.HoverColor]: ac.Transparent,
			[Layers.CssVars.HoverStrokeColor]: ac.Transparent,

			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]:0.2,
			[Layers.CssVars.HoverStrokeWidth]:0.2,

		} as CSSObject,

		unavailableTiles: {
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.HoverColor]: ac.Transparent,
			[Layers.CssVars.HoverStrokeColor]: ac.Transparent,

			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]:0.2,
			[Layers.CssVars.HoverStrokeWidth]:0.2,

		} as CSSObject,
		color: Layers.markerStyles.color.fill,
		markerSize: Layers.markerStyles.size.lg,
		toggleSize: Layers.markerStyles.size.md,
		unitLayers: {
			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,
			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,
		} as React.CSSProperties,
	},
	[`${COMMUNITY_NAMES.SEDRA}-${PROJECT_NAMES.SEDRA_2A}`]: {
		activeTiles: {
			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.StrokeWidth]:0.2,
		} as CSSObject,

		availableTiles: {
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.HoverColor]: ac.Transparent,
			[Layers.CssVars.HoverStrokeColor]: ac.Transparent,

			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]:0.2,
			[Layers.CssVars.HoverStrokeWidth]:0.2,
		} as CSSObject,

		hideTiles:{
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.HoverColor]: ac.Transparent,
			[Layers.CssVars.HoverStrokeColor]: ac.Transparent,

			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]:0.2,
			[Layers.CssVars.HoverStrokeWidth]:0.2,

		} as CSSObject,
		unavailableTiles: {
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.HoverColor]: ac.Transparent,
			[Layers.CssVars.HoverStrokeColor]: ac.Transparent,

			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]:0.2,
			[Layers.CssVars.HoverStrokeWidth]:0.2,

		} as CSSObject,

		unreleasedTiles:{
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.HoverColor]: ac.Transparent,
			[Layers.CssVars.HoverStrokeColor]: ac.Transparent,

			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]:0.2,
			[Layers.CssVars.HoverStrokeWidth]:0.2,

		} as CSSObject,
		color: Layers.markerStyles.color.fill,
		markerSize: Layers.markerStyles.size.lg,
		toggleSize: Layers.markerStyles.size.md,
		unitLayers: {
			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,
			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,
		} as React.CSSProperties,
	},
	[`${COMMUNITY_NAMES.ALMANAR}-${PROJECT_NAMES.ALMANAR_1}`]: {
		activeTiles: {
			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.StrokeWidth]:0.2,
		} as CSSObject,

		availableTiles: {
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.HoverColor]: ac.Transparent,
			[Layers.CssVars.HoverStrokeColor]: ac.Transparent,

			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]:0.2,
			[Layers.CssVars.HoverStrokeWidth]:0.2,
		} as CSSObject,

		hideTiles:{
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.HoverColor]: ac.Transparent,
			[Layers.CssVars.HoverStrokeColor]: ac.Transparent,

			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]:0.2,
			[Layers.CssVars.HoverStrokeWidth]:0.2,

		} as CSSObject,
		unreleasedTiles:{
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.HoverColor]: ac.Transparent,
			[Layers.CssVars.HoverStrokeColor]: ac.Transparent,

			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]:0.2,
			[Layers.CssVars.HoverStrokeWidth]:0.2,

		} as CSSObject,

		unavailableTiles: {
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.HoverColor]: ac.Transparent,
			[Layers.CssVars.HoverStrokeColor]: ac.Transparent,

			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]:0.2,
			[Layers.CssVars.HoverStrokeWidth]:0.2,

		} as CSSObject,
		color: Layers.markerStyles.color.fill,
		markerSize: Layers.markerStyles.size.lg,
		toggleSize: Layers.markerStyles.size.md,
		unitLayers: {
			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,
			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,
		} as React.CSSProperties,
	},
	[`${COMMUNITY_NAMES.ALMANAR}-${PROJECT_NAMES.ALDANAH_1}`]: {
		activeTiles: {
			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.StrokeWidth]:0.2,
		} as CSSObject,

		availableTiles: {
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.HoverColor]: ac.Transparent,
			[Layers.CssVars.HoverStrokeColor]: ac.Transparent,

			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]:0.2,
			[Layers.CssVars.HoverStrokeWidth]:0.2,
		} as CSSObject,

		hideTiles:{
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.HoverColor]: ac.Transparent,
			[Layers.CssVars.HoverStrokeColor]: ac.Transparent,

			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]:0.2,
			[Layers.CssVars.HoverStrokeWidth]:0.2,

		} as CSSObject,
		unreleasedTiles:{
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.HoverColor]: ac.Transparent,
			[Layers.CssVars.HoverStrokeColor]: ac.Transparent,

			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]:0.2,
			[Layers.CssVars.HoverStrokeWidth]:0.2,

		} as CSSObject,

		unavailableTiles: {
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.HoverColor]: ac.Transparent,
			[Layers.CssVars.HoverStrokeColor]: ac.Transparent,

			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]:0.2,
			[Layers.CssVars.HoverStrokeWidth]:0.2,

		} as CSSObject,
		color: Layers.markerStyles.color.fill,
		markerSize: Layers.markerStyles.size.lg,
		toggleSize: Layers.markerStyles.size.md,
		unitLayers: {
			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,
			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,
		} as React.CSSProperties,
	},
	[`${COMMUNITY_NAMES.SEDRA}-${PROJECT_NAMES.SEDRA_5}`]: {
		activeTiles: {
			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.StrokeWidth]:0.2,
		} as CSSObject,

		availableTiles: {
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.HoverColor]: ac.Transparent,
			[Layers.CssVars.HoverStrokeColor]: ac.Transparent,

			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]:0.2,
			[Layers.CssVars.HoverStrokeWidth]:0.2,
		} as CSSObject,

		hideTiles:{
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.HoverColor]: ac.Transparent,
			[Layers.CssVars.HoverStrokeColor]: ac.Transparent,

			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]:0.2,
			[Layers.CssVars.HoverStrokeWidth]:0.2,

		} as CSSObject,
		unavailableTiles: {
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.HoverColor]: ac.Transparent,
			[Layers.CssVars.HoverStrokeColor]: ac.Transparent,

			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]:0.2,
			[Layers.CssVars.HoverStrokeWidth]:0.2,

		} as CSSObject,

		unreleasedTiles:{
			[Layers.CssVars.FillColor]: ac.Transparent,
			[Layers.CssVars.StrokeColor]: ac.Transparent,
			[Layers.CssVars.HoverColor]: ac.Transparent,
			[Layers.CssVars.HoverStrokeColor]: ac.Transparent,

			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,

			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,

			[Layers.CssVars.StrokeWidth]:0.2,
			[Layers.CssVars.HoverStrokeWidth]:0.2,

		} as CSSObject,
		color: Layers.markerStyles.color.fill,
		markerSize: Layers.markerStyles.size.lg,
		toggleSize: Layers.markerStyles.size.md,
		unitLayers: {
			[Layers.CssVars.FillOpacity]: 1,
			[Layers.CssVars.StrokeOpacity]: 1,
			[Layers.CssVars.HoverFillOpacity]: 1,
			[Layers.CssVars.HoverStrokeOpacity]: 1,
		} as React.CSSProperties,
	},
};