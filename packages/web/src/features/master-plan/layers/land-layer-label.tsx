/* eslint-disable sort-keys-fix/sort-keys-fix */
import * as MapView from "@/components/map-view";
import { Trans } from "@/components/trans";
import { OverrideProps } from "@/types/react";
import { css } from "@emotion/react";
import { useTranslation } from "react-i18next";
import * as ac from "@/theme/palette/all-colors";
import { styles } from ".";
import { AppTheme, mobileMQ } from "@/theme";
import { Typography } from "@/components/typography";
import { useParams } from "@remix-run/react";
import { useFeatureFlagApi } from "@/features/feature-flags";

const labelStyles = {
	label: {
		base: (theme: AppTheme) =>
			css({
				color: ac.Black,
				letterSpacing: theme.direction == "ltr" ? theme.letterSpacings.lg : 0,
				textTransform: "uppercase",
			}),
		titleConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.xl,
				fontWeight: theme.typography.fontWeightRegular,
				[mobileMQ(theme)]: {
					fontSize: theme.spaces.xl,
				},
			}),
		amenitiesConfig: (theme: AppTheme) =>
				css({
					color: ac.White,
					letterSpacing: theme.direction == "ltr" ? theme.letterSpacings.lg : 0,
					textTransform: "uppercase",
					fontSize: theme.direction == "rtl" ? theme.spaces.md1 : theme.spaces.md,
					'html[dir="rtl"] &': {
						marginRight: "50px"
					},
				}),
		airportTxtConfig: (theme: AppTheme) =>
				css({
					color: ac.Grey500,
					fontSize: theme.spaces.lg1,
					transform: "rotate(58deg)",
					'html[dir="rtl"] &': {
						marginRight: "35px"
					},
				}),
		kingSalmanTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Grey500,
				fontSize: theme.spaces.lg1,
				transform: "rotate(335deg)",
				'html[dir="rtl"] &': {
					marginRight: "115px"
				},
			}),
		thumamaTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Grey500,
				fontSize: theme.spaces.lg1,
				transform: "rotate(335deg)",
				'html[dir="rtl"] &': {
					marginRight: "15px"
				},
			}),
		northernTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Grey500,
				fontSize: theme.spaces.lg1,
				transform: "rotate(338deg)",
				'html[dir="rtl"] &': {
					marginRight: "230px"
				},
			}),
		alJanadiryahTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Grey500,
				fontSize: theme.spaces.lg1,	
				transform: "rotate(78deg)",
				'html[dir="rtl"] &': {
					marginRight: "-80px"
				},
			}),
		almanarTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				fontWeight: "600",
				textTransform: "uppercase",
				fontSize: theme.spaces.xl,
				marginRight: "100px",
					[mobileMQ(theme)]: {
						fontSize: theme.spaces.lg,
					},
				}),
		almanarPhase1TxtConfig: (theme: AppTheme) => 
			css({
				color: ac.Black,
				fontWeight: "600",
				textTransform: "uppercase",
				fontSize: theme.spaces.xl,
				'html[dir="ltr"] &': {
					marginLeft: "-20px"
				},
				'html[dir="rtl"] &': {
					marginRight: "-50px", 
				},
				[mobileMQ(theme)]: {
					fontSize: theme.spaces.lg,
				},
			}),
	
		mekkahJeddahRoadTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				fontSize: "30px",
				transform: "rotate(43deg)",
				'html[dir="ltr"] &': {
					marginRight: "-150px", 
					marginTop: "-15px"
				},
				'html[dir="rtl"] &': {
					marginRight: "-160px", 
					marginTop: "30px"
				},
			}),
		
		multiLine: {
			base: (theme: AppTheme) =>
				css({
					display: "block",
					lineHeight: theme.spaces.xl2,
					[mobileMQ(theme)]: {
						lineHeight: theme.spaces.md1,
					},
				}),
			gap: (theme: AppTheme) =>
				css({
					[mobileMQ(theme)]: {
						display: "none",
					},
				}),
			title: (theme: AppTheme) =>
				css({
					display: "block",
					fontSize: theme.spaces.xl,
					fontWeight: theme.typography.fontWeightBold,
					[mobileMQ(theme)]: {
						fontSize: theme.spaces.md1,
					},
				}),
		},

		// Aldanah City View
		kingPortTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.xl,
				'html[dir="rtl"] &': {
					marginRight: "10px", 
				},
			}),
		kingPortKMConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.xl,
				'html[dir="rtl"] &': {
					marginRight: "160px", 
				},
			}),
		imamUnivTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.xl,
				'html[dir="rtl"] &': {
					marginRight: "-340px", 
					marginTop: "10px"
				},
			}),
		imamUnivKMConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.xl,
				'html[dir="rtl"] &': {
					marginRight: "150px", 
				},
			}),
		dahranExpoTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.xl,
				'html[dir="rtl"] &': {
					marginRight: "60px", 
					marginTop: "10px"
				},
			}),
		dahranExpoKMConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.xl,
				'html[dir="rtl"] &': {
					marginRight: "150px", 
				},
			}),
		princeJalawiStadiumTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.xl,
				'html[dir="rtl"] &': {
					marginRight: "-200px", 
					marginTop: "10px"
				},
			}),
		princeJalawiStadiumKMConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.xl,
				'html[dir="rtl"] &': {
					marginRight: "150px", 
				},
			}),
		greenSportsTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.xl,
				'html[dir="rtl"] &': {
					marginRight: "-200px", 
				},
			}),
		greenSportsKMConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.xl,
				'html[dir="rtl"] &': {
					marginRight: "160px", 
				},
			}),
		alsalamHospitalTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.xl,
				'html[dir="rtl"] &': {
					marginRight: "10px", 
				},
			}),
		alsalamHospitalKMConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.xl,
				'html[dir="rtl"] &': {
					marginRight: "180px", 
				},
			}),
		theAvenuesTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.xl,
				'html[dir="rtl"] &': {
					marginRight: "60px",
					marginTop: "10px" 
				},
			}),
		theAvenuesKMConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.xl,
				'html[dir="rtl"] &': {
					marginRight: "180px", 
				},
			}),
		kingFahdUnivTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.xl,
				'html[dir="rtl"] &': {
					marginRight: "-130px", 
					marginTop: "10px"
				},
			}),
		kingFahdUnivKMConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.xl,
				'html[dir="rtl"] &': {
					marginRight: "160px", 
				},
			}),
		kingCultureCenterTxtConfig: (theme: AppTheme) =>
			css({
			color: ac.Black,
			display: "block",
			fontSize: theme.spaces.xl,
			'html[dir="rtl"] &': {
				marginRight: "-210px", 
				// marginTop: "10px"
			},
		}),
		kingCultureCenterKMConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.xl,
				'html[dir="rtl"] &': {
					marginRight: "180px", 
				},
		}),
		saudiCourtTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.xl,
				'html[dir="rtl"] &': {
					marginRight: "30px", 
				},
			}),
		saudiCourtKMConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.xl,
				'html[dir="rtl"] &': {
					marginRight: "180px", 
				},
			}),
		dammamRailwayTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.xl,
				'html[dir="rtl"] &': {
					marginRight: "30px", 
				},
			}),
		dammamRailwayKMConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.xl,
				'html[dir="rtl"] &': {
					marginRight: "160px", 
				},
			}),
		princeFahdStadiumTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.xl,
				'html[dir="rtl"] &': {
					marginRight: "-20px", 
				},
			}),
		princeFahdStadiumKMConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.xl,
				'html[dir="rtl"] &': {
					marginRight: "160px", 
				},
			}),
		kingFahdAirportTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.xl,
				'html[dir="rtl"] &': {
					marginRight: "-190px", 
				},
			}),
		kingFahdAirportKMConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.xl,
				'html[dir="rtl"] &': {
					marginRight: "160px", 
				},
			}),
		kingAbdulStTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.xl3,
				transform: "rotate(73deg)",
				zIndex: 9999,
				'html[dir="rtl"] &': {
					marginRight: "665px", 
				},
			}),
		princeAhV1StTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.xl3,
				transform: "rotate(-10deg)",
				zIndex: 9999,
				'html[dir="rtl"] &': {
					marginRight: "700px", 
				},
			}),
		princeAhV2StTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.xl3,
				transform: "rotate(-10deg)",
				zIndex: 9999,
				'html[dir="rtl"] &': {
					marginRight: "700px", 
				},
			}),
		jalaludinStTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.xl3,
				transform: "rotate(-15deg)",
				zIndex: 9999,
				'html[dir="rtl"] &': {
					marginRight: "575px", 
				},
			}),
		kingFahdRdTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Grey400,
				display: "block",
				fontSize: theme.spaces.xl,
				transform: "rotate(45deg)",
				zIndex: 9999,
				'html[dir="rtl"] &': {
					marginRight: "40px", 
				},
			}),
		kingSaudRdTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Grey400,
				display: "block",
				fontSize: theme.spaces.xl,
				transform: "rotate(90deg)",
				zIndex: 9999,
				'html[dir="rtl"] &': {
					marginRight: "25px", 
				},
			}),
		kingAbdulRdTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Grey400,
				display: "block",
				fontSize: theme.spaces.xl,
				transform: "rotate(325deg)",
				zIndex: 9999,
				'html[dir="rtl"] &': {
					marginRight: "-45px", 
				},
			}),
		custodianHolyRdTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Grey400,
				display: "block",
				fontSize: theme.spaces.xl,
				transform: "rotate(40deg)",
				zIndex: 9999,
				'html[dir="rtl"] &': {
					marginRight: "-330px", 
				},
			}),

		// Sedra Community Starts
		kingKhalidAirportTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.xl,
				zIndex: 9999,
				'html[dir="rtl"] &': {
					marginRight: "-550px",
				},
			}),

		kingKhalidAirportKMConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.lg1,
				zIndex: 9999,
				'html[dir="rtl"] &': {
					marginRight: "-40px",
				},
			}),
		riyadhExpoTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.xl,
				zIndex: 9999,
				'html[dir="rtl"] &': {
					marginRight: "-200px",
				},
			}),
		riyadhExpoKMConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.lg1,
				zIndex: 9999,
				'html[dir="rtl"] &': {
					marginRight: "-20px",
				},
			}),
		princessNouraUnivTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.xl,
				zIndex: 9999,
				'html[dir="rtl"] &': {
					marginRight: "-160px",
				},
			}),
		princessNouraUnivKMConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.lg1,
				zIndex: 9999,
				'html[dir="rtl"] &': {
					marginRight: "-35px",
				},
			}),
		roshnFrontTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.xl,
				zIndex: 9999,
				'html[dir="rtl"] &': {
					marginRight: "-100px",
				},
			}),
		roshnFrontKMConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.lg1,
				zIndex: 9999,
				'html[dir="rtl"] &': {
					marginRight: "-30px",
				},
			}),
		warefaCommunityTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.xl,
				zIndex: 9999,
				'html[dir="rtl"] &': {
					marginRight: "-10px",
				},
			}),
		warefaCommunityKMConfig: (theme: AppTheme) =>
			css({
				color: ac.Black,
				display: "block",
				fontSize: theme.spaces.lg1,
				zIndex: 9999,
				'html[dir="rtl"] &': {
					marginRight: "260px",
				},
			}),
		kingSalmanRdTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Grey400,
				display: "block",
				fontSize: theme.spaces.xl,
				transform: "rotate(335deg)",
				zIndex: 9999,
				'html[dir="rtl"] &': {
					marginRight: "60px",
				},
			}),
		airportRdTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Grey400,
				display: "block",
				fontSize: theme.spaces.xl,
				transform: "rotate(60deg)",
				zIndex: 9999,
				'html[dir="rtl"] &': {
					marginRight: "10px",
				},
			}),
		thumamahRdTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Grey400,
				display: "block",
				fontSize: theme.spaces.xl,
				transform: "rotate(330deg)",
				zIndex: 9999,
				'html[dir="rtl"] &': {
					marginRight: "-10px",
				},
			}),
		northernRingRdTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Grey400,
				display: "block",
				fontSize: theme.spaces.xl,
				transform: "rotate(335deg)",
				zIndex: 9999,
				'html[dir="rtl"] &': {
					marginRight: "200px",
				},
			}),
		dammamRdTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Grey400,
				display: "block",
				fontSize: theme.spaces.xl,
				transform: "rotate(310deg)",
				zIndex: 9999,
				'html[dir="rtl"] &': {
					marginRight: "270px",
				},
			}),
		khuraisRdTxtConfig: (theme: AppTheme) =>
			css({
				color: ac.Grey400,
				display: "block",
				fontSize: theme.spaces.xl,
				transform: "rotate(305deg)",
				zIndex: 9999,
				'html[dir="rtl"] &': {
					marginRight: "280px",
				},
			}),
		// Sedra Community Ends

		

	},
	root: css({
		height: "100%",
		width: "100%",
	}),
};

export type LandLayerLabelProps = OverrideProps<
	typeof MapView.LabelTileRoot,
	{
		isExplore?: boolean;
		text: string;
	}
>;


const text = (
	<Typography
		css={[labelStyles.label.base, labelStyles.label.multiLine.base]}
	/>
);


const title = (
	<Typography
		css={[labelStyles.label.titleConfig]}
	/>
);

const amenitiesText = (
	<Typography
		css={[labelStyles.label.amenitiesConfig, labelStyles.label.multiLine.base]}
	/>
);

const airportAttr = (
	<Typography
		css={[labelStyles.label.airportTxtConfig]}
	/>
);

const kingSalmanAttr = (
	<Typography
		css={[labelStyles.label.kingSalmanTxtConfig]}
	/>
);

const thumamaAttr = (
	<Typography
		css={[labelStyles.label.thumamaTxtConfig]}
	/>
);

const northernAttr = (
	<Typography
		css={[labelStyles.label.northernTxtConfig]}
	/>
);

const alJanaAttr = (
	<Typography
		css={[labelStyles.label.alJanadiryahTxtConfig]}
	/>
);

const almanarTitleAttr = (
	<Typography
		css={[labelStyles.label.almanarTxtConfig]}
	/>
);

const almanar1Attr = (
	<Typography
		css={[labelStyles.label.almanarPhase1TxtConfig]}
	/>
);

const mekkahJeddahAttr = (
	<Typography
		css={[labelStyles.label.mekkahJeddahRoadTxtConfig]}
	/>
);

// Aldanah  Begins

const kingPortTxt = (
	<Typography
		css={[labelStyles.label.kingPortTxtConfig]}
	/>
);

const kingPortKM = (
	<Typography
		css={[labelStyles.label.kingPortKMConfig]}
	/>
);

const imamUnivTxt = (
	<Typography
		css={[labelStyles.label.imamUnivTxtConfig]}
	/>
);
const imamUnivKM = (
	<Typography
		css={[labelStyles.label.imamUnivKMConfig]}
	/>
);
const dahranExpoTxt = (
	<Typography
		css={[labelStyles.label.dahranExpoTxtConfig]}
	/>
);
const dahranExpoKM = (
	<Typography
		css={[labelStyles.label.dahranExpoKMConfig]}
	/>
);
const princeJalawiStadiumTxt = (
	<Typography
		css={[labelStyles.label.princeJalawiStadiumTxtConfig]}
	/>
);
const princeJalawiStadiumKM = (
	<Typography
		css={[labelStyles.label.princeJalawiStadiumKMConfig]}
	/>
);
const greenSportsTxt = (
	<Typography
		css={[labelStyles.label.greenSportsTxtConfig]}
	/>
);
const greenSportsKM = (
	<Typography
		css={[labelStyles.label.greenSportsKMConfig]}
	/>
);
const alsalamHospitalTxt = (
	<Typography
		css={[labelStyles.label.alsalamHospitalTxtConfig]}
	/>
);
const alsalamHospitalKM = (
	<Typography
		css={[labelStyles.label.alsalamHospitalKMConfig]}
	/>
);
const theAvenuesTxt = (
	<Typography
		css={[labelStyles.label.theAvenuesTxtConfig]}
	/>
);
const theAvenuesKM = (
	<Typography
		css={[labelStyles.label.theAvenuesKMConfig]}
	/>
);
const kingFahdUnivTxt = (
	<Typography
		css={[labelStyles.label.kingFahdUnivTxtConfig]}
	/>
);
const kingFahdUnivKM = (
	<Typography
		css={[labelStyles.label.kingFahdUnivKMConfig]}
	/>
);
const kingCultureCenterTxt = (
	<Typography
		css={[labelStyles.label.kingCultureCenterTxtConfig]}
	/>
);
const kingCultureCenterKM = (
	<Typography
		css={[labelStyles.label.kingCultureCenterKMConfig]}
	/>
);
const saudiCourtTxt = (
	<Typography
		css={[labelStyles.label.saudiCourtTxtConfig]}
	/>
);
const saudiCourtKM = (
	<Typography
		css={[labelStyles.label.saudiCourtKMConfig]}
	/>
);
const dammamRailwayTxt = (
	<Typography
		css={[labelStyles.label.dammamRailwayTxtConfig]}
	/>
);
const dammamRailwayKM = (
	<Typography
		css={[labelStyles.label.dammamRailwayKMConfig]}
	/>
);
const princeFahdStadiumTxt = (
	<Typography
		css={[labelStyles.label.princeFahdStadiumTxtConfig]}
	/>
);
const princeFahdStadiumKM = (
	<Typography
		css={[labelStyles.label.princeFahdStadiumKMConfig]}
	/>
);
const kingFahdAirportTxt = (
	<Typography
		css={[labelStyles.label.kingFahdAirportTxtConfig]}
	/>
);
const kingFahdAirportKM = (
	<Typography
		css={[labelStyles.label.kingFahdAirportKMConfig]}
	/>
);

const kingAbdulStTxt = (
	<Typography
		css={[labelStyles.label.kingAbdulStTxtConfig]}
	/>
);

const princeAhV1StTxt = (
	<Typography
		css={[labelStyles.label.princeAhV1StTxtConfig]}
	/>
);


const princeAhV2StTxt = (
	<Typography
		css={[labelStyles.label.princeAhV2StTxtConfig]}
	/>
);

const jalaludinStTxt = (
	<Typography
		css={[labelStyles.label.jalaludinStTxtConfig]}
	/>
);

const kingFahdRdTxt = (
	<Typography
		css={[labelStyles.label.kingFahdRdTxtConfig]}
	/>
);

const kingSaudRdTxt = (
	<Typography
		css={[labelStyles.label.kingSaudRdTxtConfig]}
	/>
);

const kingAbdulRdTxt = (
	<Typography
		css={[labelStyles.label.kingAbdulRdTxtConfig]}
	/>
);

const custodianHolyRdTxt = (
	<Typography
		css={[labelStyles.label.custodianHolyRdTxtConfig]}
	/>
);

const kingKhalidAirportTxt =(
	<Typography
		css={[labelStyles.label.kingKhalidAirportTxtConfig]}
	/>
)

const kingKhalidAirportKM =(
	<Typography
		css={[labelStyles.label.kingKhalidAirportKMConfig]}
	/>
)

const riyadhExpoTxt =(
	<Typography
		css={[labelStyles.label.riyadhExpoTxtConfig]}
	/>
)

const riyadhExpoKM =(
	<Typography
		css={[labelStyles.label.riyadhExpoKMConfig]}
	/>
)

const princessNouraUnivTxt =(
	<Typography
		css={[labelStyles.label.princessNouraUnivTxtConfig]}
	/>
)

const princessNouraUnivKM =(
	<Typography
		css={[labelStyles.label.princessNouraUnivKMConfig]}
	/>
)

const roshnFrontTxt =(
	<Typography
		css={[labelStyles.label.roshnFrontTxtConfig]}
	/>
)

const roshnFrontKM =(
	<Typography
		css={[labelStyles.label.roshnFrontKMConfig]}
	/>
)


const warefaCommunityTxt =(
	<Typography
		css={[labelStyles.label.warefaCommunityTxtConfig]}
	/>
)

const warefaCommunityKM =(
	<Typography
		css={[labelStyles.label.warefaCommunityKMConfig]}
	/>
)

const kingSalmanRdTxt =(
	<Typography
		css={[labelStyles.label.kingSalmanRdTxtConfig]}
	/>
)

const airportRdTxt =(
	<Typography
		css={[labelStyles.label.airportRdTxtConfig]}
	/>
)

const thumamahRdTxt =(
	<Typography
		css={[labelStyles.label.thumamahRdTxtConfig]}
	/>
)

const northernRingRdTxt =(
	<Typography
		css={[labelStyles.label.northernRingRdTxtConfig]}
	/>
)

const dammamRdTxt =(
	<Typography
		css={[labelStyles.label.dammamRdTxtConfig]}
	/>
)

const khuraisRdTxt =(
	<Typography
		css={[labelStyles.label.khuraisRdTxtConfig]}
	/>
)


// Sedra Community Starts


// Sedra Community Ends




const components = {
	n: <br css={labelStyles.label.multiLine.gap} />,
	text: text,
	title: title,
	amenitiesText: amenitiesText,
	airportAttr: airportAttr,
	kingSalmanAttr: kingSalmanAttr,
	thumamaAttr: thumamaAttr,
	northernAttr: northernAttr,
	alJanaAttr: alJanaAttr,
	almanar1Attr: almanar1Attr,
	mekkahJeddahAttr: mekkahJeddahAttr,
	almanarTitleAttr: almanarTitleAttr,
	kingPortTxt: kingPortTxt,
	kingPortKM: kingPortKM,
	imamUnivTxt: imamUnivTxt,
	imamUnivKM: imamUnivKM,
	dahranExpoTxt: dahranExpoTxt,
	dahranExpoKM: dahranExpoKM,
	princeJalawiStadiumTxt: princeJalawiStadiumTxt,
	princeJalawiStadiumKM: princeJalawiStadiumKM,
	greenSportsTxt: greenSportsTxt,
	greenSportsKM: greenSportsKM,
	alsalamHospitalTxt: alsalamHospitalTxt,
	alsalamHospitalKM: alsalamHospitalKM,
	theAvenuesTxt: theAvenuesTxt,
	theAvenuesKM: theAvenuesKM,
	kingFahdUnivTxt: kingFahdUnivTxt,
	kingFahdUnivKM: kingFahdUnivKM,
	kingCultureCenterTxt: kingCultureCenterTxt,
	kingCultureCenterKM: kingCultureCenterKM,
	saudiCourtTxt: saudiCourtTxt,
	saudiCourtKM: saudiCourtKM,
	dammamRailwayTxt: dammamRailwayTxt,
	dammamRailwayKM: dammamRailwayKM,
	princeFahdStadiumTxt: princeFahdStadiumTxt,
	princeFahdStadiumKM: princeFahdStadiumKM,
	kingFahdAirportTxt: kingFahdAirportTxt,
	kingFahdAirportKM: kingFahdAirportKM,
	kingAbdulStTxt: kingAbdulStTxt,
	princeAhV1StTxt: princeAhV1StTxt,
	princeAhV2StTxt: princeAhV2StTxt,
	jalaludinStTxt: jalaludinStTxt,
	kingFahdRdTxt: kingFahdRdTxt,
	kingSaudRdTxt: kingSaudRdTxt,
	kingAbdulRdTxt: kingAbdulRdTxt,
	custodianHolyRdTxt: custodianHolyRdTxt,

	kingKhalidAirportTxt:kingKhalidAirportTxt,
	kingKhalidAirportKM:kingKhalidAirportKM,
	riyadhExpoTxt:riyadhExpoTxt,
	riyadhExpoKM:riyadhExpoKM,
	princessNouraUnivTxt:princessNouraUnivTxt,
	princessNouraUnivKM:princessNouraUnivKM,
	roshnFrontTxt:roshnFrontTxt,
	roshnFrontKM:roshnFrontKM,
	warefaCommunityTxt:warefaCommunityTxt,
	warefaCommunityKM:warefaCommunityKM,
	kingSalmanRdTxt:kingSalmanRdTxt,
	airportRdTxt:airportRdTxt,
	thumamahRdTxt:thumamahRdTxt,
	northernRingRdTxt:northernRingRdTxt,
	dammamRdTxt:dammamRdTxt,
	khuraisRdTxt:khuraisRdTxt
	
};

export const LandLayerLabel = ({
	text,
	isExplore = false,
	...rootProps
}: LandLayerLabelProps) => {
	let prefix = "features.masterPlan.cityViewAmenities.logo";
	const t = useTranslation(undefined, {
		keyPrefix: prefix,
	}).t;
	const { data: featureFlagAPI, isLoading } = useFeatureFlagApi();
	const enableSedraV2 = featureFlagAPI?.enableSedraRevamp || false;

	const { communityName = "" } = useParams();
	if(communityName == "aldanah"){
		prefix = prefix+"."+communityName
	}else if(enableSedraV2 && communityName == "sedra"){
		prefix = prefix+"."+communityName
	}
	
	return (
		<MapView.LabelTileRoot {...rootProps} css={styles.common}>
			<div css={[labelStyles.root, styles.common]}>
				<div css={[labelStyles.root, styles.common]}>
					{isExplore ? (
						<MapView.LabelTileText>
							<Trans i18nKey={`${prefix}.${text}`} components={components} />
						</MapView.LabelTileText>
					) : (
						<MapView.LabelTileText css={labelStyles.label.base}>
							{t(text)}
						</MapView.LabelTileText>
					)}
				</div>
			</div>
		</MapView.LabelTileRoot>
	);
};
