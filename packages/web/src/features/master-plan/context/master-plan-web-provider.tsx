import { useMemo } from "react";
import { HouseUnit } from "../data/schema";
import { useNavigate, useParams, useSearchParams } from "@remix-run/react";
import { useFreshCallback } from "rooks";
import { AppPaths, useAppPathGenerator } from "@/routes";
import { useOpenReservation } from "@/features/reservation/hook/open-flow";
import {
	useFavouriteUnitCodesQuery,
	useMutateFavourite,
} from "@roshn/shared/es/features/favourites";
import {
	AuthService,
	PropertyDetailService,
	useInjection,
} from "@roshn/shared";
import { useFeatureFlagApi } from "@/features/feature-flags";
import * as R from "ramda";
import { createSearchParamsAny } from "@/utils/create-search-params";
import { COMMUNITY_NAMES } from "@/constants";
import { Riyadh, RiyadhSedraCommunity, RiyadhSedraCommunityV2, RiyadhSedraCommunityWithSedra5 } from "../data/riyadh-sedra";
import { EastRiyadh } from "../data/riyadh-warefa";
import { Jeddah } from "../data/jeddah-alarous";
import { mapToPropertyGroupFromData } from "@/pages/property-group/property-group.mapper";
import { Property } from "@/types/property-type";
import { MasterPlanContext, MasterPlanState } from ".";
import { useIsSaleAdvisor } from "@/features/sales-advisor/use-is-sale-advisor";
import { useCommunityDataBase, useZoneDataBase } from "./utils";
import { JeddahAlmanar } from "../data/jeddah-almanar";
import { DammamAldanah, DammamAldanahWithPhase2 } from "../data/dammam-aldanah";
import { createSvg } from "@/components/svgs";
import { useTranslation } from "react-i18next";
import { useParams } from "@remix-run/react";
import { useEffect } from "react";

const mapToLower = R.map(R.toLower);


const CustodianRdARSvg = createSvg( 
	() => import("@/assets/masterplan/dammam-aldanah/custodianRoadAR.svg"),
);

const CustodianRdENSvg = createSvg( 
	() => import("@/assets/masterplan/dammam-aldanah/custodianRoadEN.svg"),
);


const KingAbdulazizRoadENSvg = createSvg(
	() => import("@/assets/masterplan/dammam-aldanah/king-abdulaziz-road-en.svg"),
);

const KingAbdulazizRoadARSvg = createSvg(
	() => import("@/assets/masterplan/dammam-aldanah/king-abdulaziz-road-ar.svg"),
);

const KingFahadRoadENSvg = createSvg(
	() => import("@/assets/masterplan/dammam-aldanah/king-fahad-road-en.svg"),
);

const KingFahadRoadARSvg = createSvg(
	() => import("@/assets/masterplan/dammam-aldanah/king-fahad-road-ar.svg"),
);

const KingSaudiRoadENSvg = createSvg(
	() => import("@/assets/masterplan/dammam-aldanah/king-saudi-road-en.svg"),
);

const KingSaudiRoadARSvg = createSvg(
	() => import("@/assets/masterplan/dammam-aldanah/king-saudi-road-ar.svg"),
);


const PrinceAhmedRdENSvg = createSvg(
	() => import("@/assets/masterplan/dammam-aldanah/prince-ahmed-road-en.svg"),
);

const PrinceAhmedRdARSvg = createSvg(
	() => import("@/assets/masterplan/dammam-aldanah/prince-ahmed-road-ar.svg"),
);

// Sedra Logos
const SedraLogoEN = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/sedra_logo_v2_en.svg"),
)
const SedraLogoAR = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/sedra_logo_v2_ar.svg"),
)
const WarefaLogoEN = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/warefa_logo_v2_en.svg"),
)
const WarefaLogoAR = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/warefa_logo_v2_ar.svg"),
)

const KingSalmanRdSvgEN = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/king-salman-rd-en.svg"),
)
const KingSalmanRdSvgAR = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/king-salman-rd-ar.svg"),
)

const NorthernRingRdSvgEN = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/northern-ring-rd-en.svg"),
)
const NorthernRingRdSvgAR = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/northern-ring-rd-ar.svg"),
)

const ThumamaRdSvgEN = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/thumama_rd_en.svg"),
)

const ThumamaRdSvgAR = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/thumama_rd_ar.svg"),
)

const KhuraisRdEN = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/khurais_rd_en.svg"),
)

const KhuraisRdAR = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/khurais_rd_ar.svg"),
)

const DammamRdSvgEN = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/dammam-rd-en.svg"),
)
const DammamRdSvgAR = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/dammam-rd-ar.svg"),
)

const AirportRdSvgEN = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/airport-rd-en.svg"),
)

const AirportRdSvgAR = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/airport-rd-ar.svg"),
)









const aldanahLogosEN = [
	{
		bounds: [
			[1480, 2030],
			[1480 + 200, 2030 + 200],
		],
		svg:  KingFahadRoadENSvg,
	},
	{
		bounds: [
			[2025, 2830],
			[2025 + 250, 2830 + 250],
		],
		svg:  KingSaudiRoadENSvg,
	},
	{
		bounds: [
			[1390, 2840],
			[1390 + 250, 2840 + 250],
		],
		svg:  KingAbdulazizRoadENSvg,
	},
	{
		bounds: [
			[1304, 3135],
			[1304 + 500, 3135 + 500],
		],
		svg: CustodianRdENSvg,
	},
	{
		bounds: [
			[1815, 3040],
			[1815 + 200, 3040 + 200],
		],
		svg:  PrinceAhmedRdENSvg,
	},
]

const aldanahLogosAR = [
	{
		bounds: [
			[1476, 2030],
			[1476 + 150, 2030 + 150],
		],
		svg:  KingFahadRoadARSvg,
	},
	
	{
		bounds: [
			[2025, 2830],
			[2025 + 250, 2830 + 250],
		],
		svg:  KingSaudiRoadARSvg,
	},
	{
		bounds: [
			[1380, 2852],
			[1380 + 250, 2852 + 250],
		],
		svg:  KingAbdulazizRoadARSvg,
	},
	{
		bounds: [
			[1451, 3235],
			[1451 + 300, 3235 + 300],
		],
		svg: CustodianRdARSvg,
	},
	{
		bounds: [
			[1815, 3040],
			[1815 + 150, 3040 + 150],
		],
		svg:  PrinceAhmedRdARSvg,
	},
]

const sedraLogosEN = [
	{
		bounds: [
			[1900, 1531],
			[1900 + 320, 1531 + 400],
		],
		svg: SedraLogoEN
	},
	{
		bounds: [
			[3700, 2030],
			[3700 + 250, 2030 + 250],
		],
		svg: WarefaLogoEN
	},
	{
		bounds: [
			[1485, 1795],
			[1485 + 200, 1795 + 170],
		],
		svg: AirportRdSvgEN
	},
	{
		bounds: [
			[454, 2360],
			[454 + 450, 2360 + 350],
		],
		svg: KingSalmanRdSvgEN
	},
	{
		bounds: [
			[2307, 2700],
			[2307 + 300, 2700 + 150],
		],
		svg: NorthernRingRdSvgEN
	},
	{
		bounds: [
			[2384, 2118],
			[2384 + 260, 2118 + 130],
		],
		svg: ThumamaRdSvgEN
	},
	{
		bounds: [
			[3500, 1844],
			[3500 + 180, 1844 + 180],
		],
		svg: DammamRdSvgEN
	},
	{
		bounds: [
			[3348, 2670],
			[3348 + 500, 2670 + 170],
		],
		svg: KhuraisRdEN
	},
	
];

const sedraLogosAR = [
	{
		bounds: [
			[1900, 1531],
			[1900 + 320, 1531 + 400],
		],
		svg: SedraLogoAR
	},
	{
		bounds: [
			[3700, 2030],
			[3700 + 250, 2030 + 250],
		],
		svg: WarefaLogoAR
	},
	{
		bounds: [
			[1485, 1875],
			[1485 + 300, 1875 + 170],
		],
		svg: AirportRdSvgAR
	},
	{
		bounds: [
			[454, 2360],
			[454 + 450, 2360 + 350],
		],
		svg: KingSalmanRdSvgAR
	},
	{
		bounds: [
			[2307, 2700],
			[2307 + 300, 2700 + 150],
		],
		svg: NorthernRingRdSvgAR
	},
	{
		bounds: [
			[2384, 2118],
			[2384 + 260, 2118 + 130],
		],
		svg: ThumamaRdSvgAR
	},
	{
		bounds: [
			[3500, 1844],
			[3500 + 180, 1844 + 180],
		],
		svg: DammamRdSvgAR
	},
	{
		bounds: [
			[3348, 2670],
			[3348 + 500, 2670 + 150],
		],
		svg: KhuraisRdAR
	},
];

export const useAldanahLogos = (cityData) => {
	const { i18n } = useTranslation();
	const { communityName } = useParams();
	const { data: featureFlagAPI, isLoading } = useFeatureFlagApi();
	const enableSedraV2 = featureFlagAPI?.enableSedraRevamp || false;

	const updateLogos = () => {
		if(communityName === "aldanah"){
			const community = cityData?.[communityName];
			if (!community) return;
			const newLogos = i18n.language === "ar" ? aldanahLogosAR : aldanahLogosEN;
			if (!Array.isArray(community.logos)) {
				community.logos = [];
			}
			community.logos = community.logos.filter(
				(logo) => !aldanahLogosEN.includes(logo) && !aldanahLogosAR.includes(logo)
			);
			community.logos.push(...newLogos);

		}else if(communityName === "sedra" && enableSedraV2){
			const community = cityData?.[communityName];
			if (!community) return;
			const newLogos = i18n.language === "ar" ? sedraLogosAR : sedraLogosEN;
			if (!Array.isArray(community.logos)) {
				community.logos = [];
			}
			community.logos = community.logos.filter(
				(logo) => !sedraLogosEN.includes(logo) && !sedraLogosAR.includes(logo)
			);
			community.logos.push(...newLogos);
		}	
	};

	// Run initially
	useEffect(() => {
		updateLogos();
	}, [communityName, cityData]);

	// Re-run on language change
	useEffect(() => {
		i18n.on("languageChanged", updateLogos);
		return () => {
			i18n.off("languageChanged", updateLogos);
		};
	}, [cityData, communityName]);

	return cityData?.[communityName]?.logos ?? [];
};

const useCityData = (communityName: string) => {
	const { data: featureFlagAPI, isLoading } = useFeatureFlagApi();
	const enableSedra2A = featureFlagAPI?.enableSedra2A;
	const enableAlmanarCommunity = featureFlagAPI?.enableAlmanarCommunity;
	const enableAldanah = featureFlagAPI?.enableAldanah;
	const enableSedraV2 = featureFlagAPI?.enableSedraRevamp || false;
	const enableSedra5 = featureFlagAPI?.enableSedra5 || false;	
	const enableAldanahPhase2 = featureFlagAPI?.enableAldanahPhase2 || false;

	const DammamAldanahV2 = (enableAldanahPhase2 || isLoading) ? DammamAldanahWithPhase2 : DammamAldanah;
	
	let sedraView = RiyadhSedraCommunity;
	if (enableSedraV2) {
		sedraView = enableSedra5 ? RiyadhSedraCommunityWithSedra5 : RiyadhSedraCommunityV2;
	}
	
	const cityData = {
		[COMMUNITY_NAMES.SEDRA]: enableSedra2A || isLoading ? sedraView : Riyadh,
		[COMMUNITY_NAMES.WAREFA]: EastRiyadh,
		[COMMUNITY_NAMES.ALAROUS]: Jeddah,
		[COMMUNITY_NAMES.ALMANAR]: enableAlmanarCommunity || isLoading ? JeddahAlmanar : null,
		[COMMUNITY_NAMES.ALDANAH]: enableAldanah || isLoading ? DammamAldanahV2 : null,
	};

	useAldanahLogos(cityData);

	return { data: cityData[communityName] ?? undefined };
};

export const useCommunityData = (
	communityName: string,
	projectName: string,
) => {
	const cityData = useCityData(communityName);
	return useCommunityDataBase(projectName, cityData?.data);
};

export const useZoneData = (
	communityName: string,
	zone: string,
	projectName: string,
) => {
	const cityData = useCityData(communityName);
	return useZoneDataBase(communityName, zone, projectName, cityData?.data);
};

export const useGetPropertyGroup = () => {
	const propertyDetailSvc = useInjection<PropertyDetailService>(
		PropertyDetailService,
	);

	const getPropertyGroup = async (
		groupId: string,
		communityName: string,
		locale: string,
	): Promise<Property> => {
		const propertyGroupRes = await propertyDetailSvc.getPropertyGroup(
			groupId,
			communityName,
			locale,
		);
		const property = mapToPropertyGroupFromData(propertyGroupRes.data);
		return property;
	};
	return getPropertyGroup;
};

export const useFavouriteData = () => {
	const { data: _favouriteData } = useFavouriteUnitCodesQuery();
	const { addFavourite, removeFavourite } = useMutateFavourite();
	const data = useMemo(() => _favouriteData?.unitCodes ?? [], [_favouriteData]);
	return {
		addFavourite,
		data,
		removeFavourite,
	};
};

const openUnitDetail: MasterPlanState["onOpenUnitDetail"] = ({
	tileId,
	el,
	data,
	trackSelectProperty,
	setUnitDetail,
	handleGetPropertyGroup,
	communityName,
	locale,
}) => {
	const unit =
		tileId && data.houseUnits?.find(({ unitCode }) => unitCode === tileId);

	if (!unit) {
		return;
	}

	// top center
	const rect = el.getBoundingClientRect();
	const left = rect.left + rect.width / 2;
	const top = rect.top + rect.height / 2;
	trackSelectProperty?.(unit.unitCode ?? "");

	// set unit detail first to avoid flickering
	setUnitDetail?.({
		position: [left, top],
		unit: undefined,
	});

	// then fetch the property info
	handleGetPropertyGroup?.(
		unit.typology ?? "",
		communityName ?? "",
		locale ?? "ar",
	).then((property) => {
		setUnitDetail?.({
			position: [left, top],
			unit: {
				...unit,
				displayFacades: !!property?.facades?.length,
				floorPlanUrl: property.floorPlanLink,
				note: property.information?.note,
			} as HouseUnit,
		});
	});
};

export function MasterPlanWebProvider({
	children,
}: Readonly<{
	children: Readonly<React.ReactNode>;
}>) {
	const isSaleAdvisor = useIsSaleAdvisor();
	const isLoggedIn = useInjection<AuthService>(AuthService).useStore().signedIn;
	const appPathGenerator = useAppPathGenerator();
	const [searchParams] = useSearchParams();
	const { communityName = "", projectName = "" } = useParams();
	const navigate = useNavigate();
	const exit = useFreshCallback(() => {
		if (isSaleAdvisor) {
			navigate(appPathGenerator(AppPaths.salesAdvisor));
		} else {
			navigate(
				appPathGenerator(AppPaths.communities, {
					communityName: communityName,
				}),
			);
		}
	});
	const onZoneClick = useFreshCallback((zone: string) => {
		navigate(
			appPathGenerator(AppPaths.masterPlanZone, {
				communityName,
				projectName,
				zone,
			}) + `?${searchParams.toString()}`,
		);
	});
	const onCommunityClick = (projectName?: string) => {
		navigate(
			appPathGenerator(AppPaths.masterPlanCommunity, {
				communityName: communityName,
				projectName: projectName ?? "",
			}) + `?${searchParams.toString()}`,
		);
	};

	const onCityClick = useFreshCallback(() => {
		navigate(
			appPathGenerator(AppPaths.masterPlanCity, {
				communityName: communityName,
			}) + `?${searchParams.toString()}`,
		);
	});
	const onListViewClick = useFreshCallback(
		({
			communityName,
			unitTypes,
			bedrooms,
			bathrooms,
		}: Parameters<MasterPlanState["onListViewClick"]>[0]) => {
			const options = [
				{ communities: communityName },
				{ unitTypes: mapToLower(unitTypes ?? []) },
				{ bedrooms: mapToLower(bedrooms ?? []) },
				{ bathrooms: mapToLower(bathrooms ?? []) },
			];

			navigate(
				appPathGenerator(AppPaths.propertyFinder) +
					`?${createSearchParamsAny(options)}`,
			);
		},
	);

	const {
		openReservation,
		errorDialog: reservationErrorDialog,
		loading: reservationLoading,
	} = useOpenReservation();

	const onReservationClick = useFreshCallback(async (unit: HouseUnit) => {
		await openReservation({
			communityName: communityName,
			groupId: unit.typology ?? "",
			unitCode: unit.unitCode ?? "",
		});
	});

	const getPropertyGroup = useGetPropertyGroup();

	const value: MasterPlanState = {
		exit,
		getPropertyGroup,
		isDarkTheme: null,
		isLoggedIn,
		onCityClick,
		onCommunityClick,
		onListViewClick,
		onOpenUnitDetail: openUnitDetail,
		onReservationClick,
		onZoneClick,
		reservationLoading,
		useCityData,
		useCommunityData,
		useFavouriteData,
		useZoneData,
	};

	return (
		<MasterPlanContext.Provider value={value}>
			{children}
			{reservationErrorDialog}
		</MasterPlanContext.Provider>
	);
}
