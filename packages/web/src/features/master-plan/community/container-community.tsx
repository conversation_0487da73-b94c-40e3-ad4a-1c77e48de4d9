import * as React from "react";
import { MasterPlanCommunity } from "./view-community";
import { useParams } from "@remix-run/react";
import { useEffect } from "react";
import {
	trackCommunityPanning,
	trackViewMasterplaneCommunity,
} from "../redux/actions";
import { useDispatch } from "react-redux";
import { useMasterPlan } from "../context";
import { Navigate } from "@/utils/navigate";
import { AppPaths, useAppPathGenerator } from "@/routes";
import { useInjection } from "@roshn/shared";
import { ProjectViewService } from "@/services/project-view";
import { useFeatureFlagApi } from "@/features/feature-flags";

export const MasterPlanCommunityContainer = () => {
	const dispatch = useDispatch();

	const { communityName = "", projectName = "" } = useParams();
	let crmProjectName = "";
	if (projectName === "sedra4") {
		crmProjectName = "sedra_4a";
	} else {
		crmProjectName = projectName;
	}
	const handleOnPanning = () => {
		dispatch(trackCommunityPanning(communityName ?? ""));
	};
	
	const [compassValue, setCompassValue] = React.useState(30);
	
	const currentZone = projectName.toUpperCase();

	const projectViewService = useInjection<ProjectViewService>(ProjectViewService);
	const projectViewFetch = React.useCallback(async () => {
		const response = await projectViewService.getProjectViewByName(currentZone);
		const check = response[0].attributes;
		setCompassValue(check.compassRotation);
	}, [projectName]);

	React.useEffect(() => {
		projectViewFetch()
	}, [projectViewFetch]);

	useEffect(() => {
		dispatch(trackViewMasterplaneCommunity(communityName ?? ""));
	}, []);

	const { useCommunityData, onZoneClick, onCityClick } = useMasterPlan();
	const { data, isLoading } = useCommunityData(communityName, crmProjectName);

	const generateAppPath = useAppPathGenerator();
	
	const { isLoading: isFeatureFlagLoading } = useFeatureFlagApi();
	if (isFeatureFlagLoading) {
		return null;
	}

	if (!data) {
		return <Navigate to={generateAppPath(AppPaths.home)} />;
	}


	return (
		<MasterPlanCommunity
			zonesLoading={isLoading}
			data={data}
			onZoneClick={onZoneClick}
			onGoBack={onCityClick}
			onPanning={handleOnPanning}
			compassRotateValue={compassValue}
		/>
	);
};
