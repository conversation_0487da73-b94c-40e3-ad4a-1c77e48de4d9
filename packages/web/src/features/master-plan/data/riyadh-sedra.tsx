import * as React from "react";

// SEDRA Revamp Version assets
import SedraCityView from "@/assets/masterplan/riyadh-sedra/City_Sedra_V2.webp";


// Sedra old version assets
import CityMap from "@/assets/masterplan/riyadh-sedra/City_Sedra_4.webp";


// Common Assets
import CommunityMap from "@/assets/masterplan/riyadh-sedra/Aerial.webp";
import ZoneAMap from "@/assets/masterplan/riyadh-sedra/riyadh-sedra-NEIGHBORHOOD_A-map-web.webp";
import ZoneBMap from "@/assets/masterplan/riyadh-sedra/riyadh-sedra-NEIGHBORHOOD_B-map-web.webp";
import ZoneCMap from "@/assets/masterplan/riyadh-sedra/riyadh-sedra-NEIGHBORHOOD_C-map-web.webp";
import ZoneGMap from "@/assets/masterplan/riyadh-sedra/riyadh-sedra-NEIGHBORHOOD_G-map-web.webp";
import ZoneHMap from "@/assets/masterplan/riyadh-sedra/riyadh-sedra-NEIGHBORHOOD_H-map-web.webp";
import type * as Schema from "./schema";
import { createSvg } from "@/components/svgs";
import { PROJECT_NAMES, ZONE_STATUS } from "@/constants";
import Sedra4ACommunityMap from "@/assets/masterplan/riyadh-sedra-4a/Community.webp";
import Sedra4BCommunityMap from "@/assets/masterplan/riyadh-sedra-4/Community.webp";
import Sedra2CommunityMap from "@/assets/masterplan/riyadh-sedra-2/Community.webp";
import Sedra5CommunityMap from "@/assets/masterplan/riyadh-sedra-5/Community.webp";
import Sedra4BZoneDMap from "@/assets/masterplan/riyadh-sedra-4/Neighborhood_D.webp";
import Sedra4BZoneEMap from "@/assets/masterplan/riyadh-sedra-4/Neighborhood_E.webp";
import Sedra4BZoneFMap from "@/assets/masterplan/riyadh-sedra-4/Neighborhood_F.webp";
import Sedra4BZoneGMap from "@/assets/masterplan/riyadh-sedra-4/Neighborhood_G.webp";
import Sedra2ZoneDMap from "@/assets/masterplan/riyadh-sedra-2/Neighborhood_D.webp";
import Sedra2ZoneEMap from "@/assets/masterplan/riyadh-sedra-2/Neighborhood_E.webp";
import Sedra2ZoneGMap from "@/assets/masterplan/riyadh-sedra-2/Neighborhood_G.webp";
import Sedra4BZoneJMap from "@/assets/masterplan/riyadh-sedra-4/Neighborhood_J.webp";
import Sedra4BZoneKMap from "@/assets/masterplan/riyadh-sedra-4/Neighborhood_K.webp";
import Sedra4BZoneLMap from "@/assets/masterplan/riyadh-sedra-4/Neighborhood_L.webp";
import Sedra4BZoneMMap from "@/assets/masterplan/riyadh-sedra-4/Neighborhood_M.webp";
import Sedra5ZoneAMap from "@/assets/masterplan/riyadh-sedra-5/Neighborhood_A.webp";
import Sedra5ZoneBMap from "@/assets/masterplan/riyadh-sedra-5/Neighborhood_B.webp";
import Sedra5ZoneCMap from "@/assets/masterplan/riyadh-sedra-5/Neighborhood_C.webp";
import {
	Sedra3ZoneA,
	Sedra3ZoneB,
	Sedra3ZoneC,
	Sedra3ZoneH,
	Sedra3ZoneG,
	Sedra4ZoneD,
	Sedra4ZoneE,
	Sedra4ZoneF,
	Sedra4ZoneG,
	Sedra4ZoneJ,
	Sedra4ZoneK,
	Sedra4ZoneL,
	Sedra4ZoneM,
	Sedra2ZoneDLabels,
	Sedra2ZoneELabels,
	Sedra2ZoneGLabels,
    Sedra5ZoneALabels,
	Sedra5ZoneBLabels,
	Sedra5ZoneCLabels
} from "./unit-label-position";

import SedraLogo from "@/assets/masterplan/riyadh-sedra/sedra_logo_en.png";


const enableSedra4Bb = import.meta.env.VITE_ENABLE_SEDRA_4B;

// New Version Assets
const CitySedra3Svg = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/sedra_3_v3.svg"),
);
const SedraTitleLabelSvg = createSvg(
	()=> import("@/assets/masterplan/riyadh-sedra/sedra_title_label.svg"),
);
const Sedra5CommunitySvg = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra-5/Community.svg"),
);
const CitySedra4V2Svg = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/sedra_4_v3.svg"),
);

const CitySedra3V2Svg = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/sedra_3_v2.svg"),
);

const CitySedra2Svg = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra-2/sedra_2_v2.svg"),
);

const CitySedra5Svg = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/sedra_5_v3.svg"),
);

const AirportSvg = createSvg(
	()=> import("@/assets/masterplan/riyadh-sedra-5/airport-icon.svg"),
);

const ExpoSvg = createSvg(
	()=> import("@/assets/masterplan/riyadh-sedra-5/expo-icon.svg"),
);

const UniversitySvg = createSvg(
	()=> import("@/assets/masterplan/riyadh-sedra-5/university-icon.svg"),
);

const ShoppingSvg = createSvg(
	()=> import("@/assets/masterplan/riyadh-sedra-5/shopping-icon.svg"),
);

const CitySedra403V2Svg = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/sedra_403_v3.svg"),
);

const CityWarefaSVG = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/warefa.svg"),
);

const Sedra3LabelSvg = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/sedra_3_label.svg"),
);
const Sedra403LabelSvg = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/sedra_403_label.svg"),
);
const Sedra4LabelSvg = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/sedra_4_label.svg"),
);
const Sedra5LabelSvg = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/sedra_5_label.svg"),
);

// Old Version Assets
const CitySvg = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/sedra_3.svg"),
);
const SedraTitleSvg = createSvg( 
	() => import("@/assets/masterplan/riyadh-sedra/sedraBTitleLabel.svg"),
);

const CitySedra2ASvg = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra-2/sedra_2.svg"),
);

const CitySedra4BSvg = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra-4/sedra_4.svg"),
);


const CommunitySvg = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/Aerial.svg"),
);

const Sedra4BCommunitySvg = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra-4/Community.svg"),
);

const Sedra4ACommunitySvg = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra-4a/Community.svg"),
);

const Sedra2CommunitySvg = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra-2/Community.svg"),
);

const CommunityLayer = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/sedraLogo.svg"),
);

const Sedra3Title = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/sedra3Title.svg"),
);

const Sedra2ATitle = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra-2/sedra2Title.svg"),
);

const Sedra4BTitle = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra-4/sedra4Title.svg"),
);

const Tag = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/logo.svg"),
);

const StationTag = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/stationLogo.svg"),
);

const RoadTag = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/roadLogo.svg"),
);

const WaterRankEn = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra-4/water-tank-en.svg"),
);

const WaterRankAr = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra-4/water-tank-ar.svg"),
);


// New Version
const ImamSaudUniversitySvg = createSvg( 
	() => import("@/assets/masterplan/riyadh-sedra/imamSaudUniversity.svg"),
);
const KingKhalidInternationalAirportSvg = createSvg( 
	() => import("@/assets/masterplan/riyadh-sedra/kingKhalidInternationalAirport.svg"),
);
const MetroStationSvg = createSvg( 
	() => import("@/assets/masterplan/riyadh-sedra/metroStation.svg"),
);
const ParkAvenueMallSvg = createSvg( 
	() => import("@/assets/masterplan/riyadh-sedra/parkAvenueMall.svg"),
);
const PrincessNouraUniversitySvg = createSvg( 
	() => import("@/assets/masterplan/riyadh-sedra/princessNouraUniversity.svg"),
);

const RoshnFrontLogoSvg = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/roshnFront.svg")
);
const AirportRoadTag = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/airportRoadTag.svg")
);
const AlJanadiryahRoadTag = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/alJanadiryahRoadTag.svg")
);
const KingSalmanBinAbdulazizRoadTag = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/kingSalmanBinAbdulazizRoadTag.svg")
);
const NorthernRingRoadTag = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/northernRingRoadTag.svg")
);
const ThumamaRoadTag = createSvg(
	() => import("@/assets/masterplan/riyadh-sedra/thumamaRoadTag.svg")
);
  
const AvailableTag = {
	height: 500,
	leftPrefix: 20,
	svg: createSvg(
		() => import("@/assets/masterplan/shared/tags/tag-available.svg"),
	),
	topPrefix: 250,
	width: 760,
};

const UnavailableTag = {
	height: 420,
	leftPrefix: 20,
	svg: createSvg(
		() => import("@/assets/masterplan/shared/tags/tag-available.svg"),
	),
	topPrefix: 250,
	width: 760,
};


const baseLabel = {
	height: 1600,
	status: ZONE_STATUS.UNRELEASED,
	tag: UnavailableTag,
	width: 800,
};


const sedra4BaseLabel = {
	...baseLabel,
};

const sedra5BaseLabel = {
	...baseLabel,
};

const SedraZoneALabel = {
	...baseLabel,
	key: "a",
	left: 2950,
	tag: {
		...baseLabel.tag,
		left: 2950,
		top: 5530 + 200,
	},
	top: 5530,
};

const SedraZoneBLabel = {
	...baseLabel,
	key: "b",
	left: 5750,
	tag: {
		...baseLabel.tag,
		left: 5750,
		top: 4230 + 200,
	},
	top: 4230,
};

const SedraZoneCLabel = {
	...baseLabel,
	key: "c",
	left: 7850,
	tag: {
		...baseLabel.tag,
		left: 7850,
		top: 3230 + 200,
	},
	top: 3230,
};

const SedraZoneGLabel = {
	...baseLabel,
	key: "g",
	left: 4050,
	tag: {
		...baseLabel.tag,
		left: 4050,
		top: 2530 + 200,
	},
	top: 2530,
};

const SedraZoneHLabel = {
	...baseLabel,
	key: "h",
	left: 1750,
	tag: {
		...baseLabel.tag,
		left: 1750,
		top: 3430 + 200,
	},
	top: 3430,
};

const Sedra4BZoneDLabel = {
	...sedra4BaseLabel,
	key: "d",
	left: 1640,
	tag: {
		...baseLabel.tag,
		left: 1640,
		top: 3800 + 200,
	},
	top: 3800,
};

const Sedra4BZoneELabel = {
	...sedra4BaseLabel,
	key: "e",
	left: 3100,
	tag: {
		...baseLabel.tag,
		left: 3100,
		top: 3300 + 200,
	},
	top: 3300,
};

const Sedra4BZoneFLabel = {
	...sedra4BaseLabel,
	key: "f",
	left: 4700,
	tag: {
		...baseLabel.tag,
		left: 4700,
		top: 2800 + 200,
	},
	top: 2800,
};

const Sedra4BZoneGLabel = {
	...sedra4BaseLabel,
	key: "g",
	left: 5900,
	tag: {
		...baseLabel.tag,
		left: 5900,
		top: 2400 + 200,
	},
	top: 2400,
};

const Sedra4BZoneJLabel = {
	...sedra4BaseLabel,
	key: "j",
	left: 7200,
	tag: {
		...baseLabel.tag,
		left: 7200,
		top: 3150 + 200,
	},
	top: 3150,
};

const Sedra4BZoneKLabel = {
	...sedra4BaseLabel,
	key: "k",
	left: 6100,
	tag: {
		...baseLabel.tag,
		left: 6100,
		top: 3700 + 200,
	},
	top: 3700,
};

const Sedra4BZoneLLabel = {
	...sedra4BaseLabel,
	key: "l",
	left: 4300,
	tag: {
		...baseLabel.tag,
		left: 4300,
		top: 4500 + 200,
	},
	top: 4500,
};
const Sedra4BZoneMLabel = {
	...sedra4BaseLabel,
	key: "m",
	left: 2600,
	tag: {
		...baseLabel.tag,
		left: 2600,
		top: 5300 + 200,
	},
	top: 5300,
};

//  Sedra 4A only --------------
const Sedra4AZoneDLabel = {
	...sedra4BaseLabel,
	key: "d",
	left: 1500,
	tag: {
		...baseLabel.tag,
		left: 1500,
		top: 3000 + 200,
	},
	top: 3000,
};

const Sedra4AZoneELabel = {
	...sedra4BaseLabel,
	key: "e",
	left: 3000,
	tag: {
		...baseLabel.tag,
		left: 3000,
		top: 3500 + 200,
	},
	top: 3500,
};

const Sedra4AZoneFLabel = {
	...sedra4BaseLabel,
	key: "f",
	left: 5100,
	tag: {
		...baseLabel.tag,
		left: 5100,
		top: 4140 + 200,
	},
	top: 4140,
};

const Sedra4AZoneGLabel = {
	...sedra4BaseLabel,
	key: "g",
	left: 7000,
	tag: {
		...baseLabel.tag,
		left: 7000,
		top: 4500 + 200,
	},
	top: 4500,
};

// sedra4A only END --------------

// Sedra 3 Zones Starts
const SedraZoneA: Schema.ZoneView = {
	educations: [
		{
			position: [6580, 5950],
		},
		{
			position: [2850, 2090],
		},
	],
	grandMosques: [
		{
			position: [4580, 4450],
		},
		{
			position: [3950, 1490],
		},
	],
	unitLabel: Sedra3ZoneA,
	mapHeight: 10000,
	mapUrl: ZoneAMap,
	mapWidth: 10000,
	unitsLayers: [
		{
			height: 10000,
			left: 0,
			svg: createSvg(
				() =>
					import(
						"@/assets/masterplan/riyadh-sedra/riyadh-sedra-NEIGHBORHOOD_A-vector-mask.svg"
					),
			),
			top: 0,
			width: 10000,
		},
	],
};

const SedraZoneB: Schema.ZoneView = {
	amenities: [
		{
			position: [5280, 5750],
		},
		{
			position: [6760, 1700],
		},
	],
	educations: [
		{
			position: [6260, 6270],
		},
		{
			position: [6960, 770],
		},
	],
	grandMosques: [
		{
			position: [5120, 5230],
		},
		{
			position: [7360, 1600],
		},
	],
	unitLabel: Sedra3ZoneB,
	mapHeight: 10000,
	mapUrl: ZoneBMap,
	mapWidth: 10000,
	unitsLayers: [
		{
			height: 10000.1,
			left: 0,
			svg: createSvg(
				() =>
					import(
						"@/assets/masterplan/riyadh-sedra/riyadh-sedra-NEIGHBORHOOD_B-vector-mask.svg"
					),
			),
			top: 0,
			width: 9999.3,
		},
	],
};

const SedraZoneC: Schema.ZoneView = {
	educations: [
		{
			position: [5800, 5550],
		},
	],
	grandMosques: [
		{
			position: [5120, 4980],
		},
	],
	mapHeight: 10000,
	unitLabel: Sedra3ZoneC,
	mapUrl: ZoneCMap,
	mapWidth: 10000,
	unitsLayers: [
		{
			height: 10000,
			left: 0,
			svg: createSvg(
				() =>
					import(
						"@/assets/masterplan/riyadh-sedra/riyadh-sedra-NEIGHBORHOOD_C-vector-mask.svg"
					),
			),
			top: 0,
			width: 10000,
		},
	],
};

const SedraZoneG: Schema.ZoneView = {
	amenities: [
		{
			position: [7670, 6890],
		},
		{
			position: [5000, 4200],
		},
	],
	educations: [
		{
			position: [3470, 3650],
		},
		{
			position: [3980, 2320],
		},
		{
			position: [7600, 5920],
		},
	],
	grandMosques: [
		{
			position: [4450, 4350],
		},
		{
			position: [8250, 6690],
		},
	],
	unitLabel: Sedra3ZoneG,
	mapHeight: 10000,
	mapUrl: ZoneGMap,
	mapWidth: 10000,
	unitsLayers: [
		{
			height: 10000,
			left: 0,
			svg: createSvg(
				() =>
					import(
						"@/assets/masterplan/riyadh-sedra/riyadh-sedra-NEIGHBORHOOD_G-vector-mask.svg"
					),
			),
			top: 0,
			width: 10000,
		},
	],
};

const SedraZoneH: Schema.ZoneView = {
	amenities: [
		{
			position: [4380, 5390],
		},
	],
	educations: [
		{
			position: [5760, 7140],
		},
		{
			position: [4150, 7500],
		},
		{
			position: [3400, 7700],
		},
		{
			position: [3150, 3500],
		},
	],
	grandMosques: [
		{
			position: [4050, 4900],
		},
		{
			position: [6700, 6790],
		},
	],
	unitLabel: Sedra3ZoneH,
	mapHeight: 10000,
	mapUrl: ZoneHMap,
	mapWidth: 10000,
	unitsLayers: [
		{
			height: 10000,
			left: 0,
			svg: createSvg(
				() =>
					import(
						"@/assets/masterplan/riyadh-sedra/riyadh-sedra-NEIGHBORHOOD_H-vector-mask.svg"
					),
			),
			top: 0,
			width: 10000,
		},
	],
};

const SedraZoneTile: Schema.SvgTileObject = {
	height: 10000,
	left: 0,
	svg: CommunitySvg,
	top: 0,
	width: 10000,
};

const Sedra4BZoneTile: Schema.SvgTileObject = {
	height: 10000,
	left: 0,
	svg: Sedra4BCommunitySvg,
	top: 0,
	width: 10000,
};

const Sedra4AZoneTile: Schema.SvgTileObject = {
	height: 10000,
	left: 0,
	svg: Sedra4ACommunitySvg,
	top: 0,
	width: 10000,
};

// TODO: update amenities name and background color
const Sedra4BZoneD: Schema.ZoneView = {
	educations: [
		{
			position: [5830, 2770],
		},
		{
			position: [7450, 3870],
		},
		{
			position: [7980, 1730],
		},
	],
	grandMosques: [
		{
			position: [4000, 4750],
		},
		{
			position: [6600, 1690],
		},
	],
	mapHeight: 10000,
	mapUrl: Sedra4BZoneDMap,
	unitLabel: Sedra4ZoneD,
	mapWidth: 10000,
	parks: [
		{
			position: [4170, 4450],
		},
		{
			position: [8380, 2430],
		},
	],
	unitsLayers: [
		{
			height: 10000,
			left: 0,
			svg: createSvg(
				() => import("@/assets/masterplan/riyadh-sedra-4/Neighborhood_D.svg"),
			),
			top: 0,
			width: 10000,
		},
	],
};

const Sedra4BZoneE: Schema.ZoneView = {
	educations: [
		{
			position: [5530, 3170],
		},
		{
			position: [7500, 3170],
		},
		{
			position: [8810, 2760],
		},
		{
			position: [3230, 6290],
		},
	],
	grandMosques: [
		{
			position: [5640, 5170],
		},
	],
	mapHeight: 10000,
	mapUrl: Sedra4BZoneEMap,
	unitLabel: Sedra4ZoneE,
	mapWidth: 10000,

	neighborhoodCommunityCentres: [
		{
			position: [5070, 5000],
		},
	],
	parks: [
		{
			position: [3995, 6105],
		},
		{
			position: [7400, 3870],
		},
	],
	unitsLayers: [
		{
			height: 10000,
			left: 0,
			svg: createSvg(
				() => import("@/assets/masterplan/riyadh-sedra-4/Neighborhood_E.svg"),
			),
			top: 0,
			width: 10000,
		},
	],
};
const Sedra4BZoneF: Schema.ZoneView = {
	educations: [
		{
			position: [8190, 3450],
		},
		{
			position: [7370, 4000],
		},
		{
			position: [1780, 6890],
		},
		{
			position: [3470, 6330],
		},
		{
			position: [8830, 4700],
		},
	],
	grandMosques: [
		{
			position: [7210, 4580],
		},
	],
	mapHeight: 10000,
	mapUrl: Sedra4BZoneFMap,
	unitLabel: Sedra4ZoneF,
	mapWidth: 10000,

	neighborhoodCommunityCentres: [
		{
			position: [6560, 4350],
		},
	],
	parks: [
		{
			position: [4190, 6040],
		},
	],
	unitsLayers: [
		{
			height: 10000,
			left: 0,
			svg: createSvg(
				() => import("@/assets/masterplan/riyadh-sedra-4/Neighborhood_F.svg"),
			),
			top: 0,
			width: 10000,
		},
	],
};

const Sedra4BZoneG: Schema.ZoneView = {
	educations: [
		{
			position: [5880, 6950],
		},
		{
			position: [3850, 7750],
		},
		{
			position: [3640, 1920],
		},
	],
	grandMosques: [
		{
			position: [7090, 5150],
		},
		{
			position: [6550, 2100],
		},
	],
	logos: [
		{
			bounds: [
				[8190, 4650],
				[8190 + 621.55, 4650 + 154.05],
			],
			svg: WaterRankEn,
		},
		{
			bounds: [
				[8305, 4480.63],
				[8305 + 391.55, 4480.63 + 134.05],
			],
			svg: WaterRankAr,
		},
	],
	mapHeight: 10000,
	mapUrl: Sedra4BZoneGMap,
	unitLabel: Sedra4ZoneG,
	mapWidth: 10000,

	neighborhoodCommunityCentres: [
		{
			position: [7820, 5430],
		},
	],
	polices: [
		{
			position: [5270, 810],
		},
	],
	unitsLayers: [
		{
			height: 10000,
			left: 0,
			svg: createSvg(
				() => import("@/assets/masterplan/riyadh-sedra-4/Neighborhood_G.svg"),
			),
			top: 0,
			width: 10000,
		},
	],
};

const Sedra4BZoneJ: Schema.ZoneView = {
	educations: [
		{
			position: [4500, 1200],
		},
		{
			position: [5000, 2500],
		},
	],
	parks: [
		{
			position: [4000, 850],
		},
		{
			position: [5800, 3500],
		},
	],
	grandMosques: [
		{
			position: [7500, 5050],
		},
	],
	logos: [],
	mapHeight: 10000,
	mapUrl: Sedra4BZoneJMap,
	unitLabel: Sedra4ZoneJ,
	mapWidth: 10000,

	neighborhoodCommunityCentres: [
		{
			position: [6800, 4400],
		},
	],
	unitsLayers: [
		{
			height: 10000,
			left: 0,
			svg: createSvg(
				() => import("@/assets/masterplan/riyadh-sedra-4/Neighborhood_J.svg"),
			),
			top: 0,
			width: 10000,
		},
	],
};
const Sedra4BZoneK: Schema.ZoneView = {
	educations: [
		{
			position: [2800, 3050],
		},
		{
			position: [3200, 3900],
		},
	],
	grandMosques: [
		{
			position: [5350, 5300],
		},
	],
	parks: [
		{
			position: [2400, 2550],
		},
		{
			position: [4500, 4500],
		},
	],
	logos: [],
	mapHeight: 10000,
	mapUrl: Sedra4BZoneKMap,
	mapWidth: 10000,
	unitLabel: Sedra4ZoneK,
	neighborhoodCommunityCentres: [
		{
			position: [4800, 4800],
		},
	],
	unitsLayers: [
		{
			height: 10000,
			left: 0,
			svg: createSvg(
				() => import("@/assets/masterplan/riyadh-sedra-4/Neighborhood_K.svg"),
			),
			top: 0,
			width: 10000,
		},
	],
};

const Sedra4BZoneL: Schema.ZoneView = {
	educations: [
		{
			position: [2900, 3050],
		},
		{
			position: [3300, 3900],
		},
	],
	grandMosques: [
		{
			position: [5500, 5850],
		},
	],
	parks: [
		{
			position: [2600, 2550],
		},
		{
			position: [4400, 4800],
		},
	],
	logos: [],
	mapHeight: 10000,
	mapUrl: Sedra4BZoneLMap,
	mapWidth: 10000,
	unitLabel: Sedra4ZoneL,
	neighborhoodCommunityCentres: [
		{
			position: [5000, 5350],
		},
	],
	unitsLayers: [
		{
			height: 10000,
			left: 0,
			svg: createSvg(
				() => import("@/assets/masterplan/riyadh-sedra-4/Neighborhood_L.svg"),
			),
			top: 0,
			width: 10000,
		},
	],
};

const Sedra4BZoneM: Schema.ZoneView = {
	parks: [
		{
			position: [2600, 4000],
		},
	],
	mapHeight: 10000,
	mapUrl: Sedra4BZoneMMap,
	unitLabel: Sedra4ZoneM,

	mapWidth: 10000,
	unitsLayers: [
		{
			height: 10000,
			left: 0,
			svg: createSvg(
				() => import("@/assets/masterplan/riyadh-sedra-4/Neighborhood_M.svg"),
			),
			top: 0,
			width: 10000,
		},
	],
};
const Sedra4BCommunityTile: Schema.CommunityTile = {
	bounds: [
		[1742.49, 1728.57],
		[1742.49 + 405, 1728.57 + 690],
	],
	id: "sedra4",
	svg: CitySedra4BSvg,
};

const Sedra4BCommunityTileV2: Schema.CommunityTile = {
	bounds: [
		[1690, 1860],
		[1690 + 291, 1860 + 300],
	],
	id: "sedra4",
	svg: CitySedra4V2Svg,
};

const sedra2BaseLabel = {
	...baseLabel,
};

const Sedra2ZoneDLabel = {
	...sedra2BaseLabel,
	key: "d",
	left: 3500,
	tag: {
		...baseLabel.tag,
		left: 3500,
		top: 5100 + 200,
	},
	top: 5100,
};

const Sedra2ZoneELabel = {
	...sedra2BaseLabel,
	key: "e",
	left: 5300,
	tag: {
		...baseLabel.tag,
		left: 5300,
		top: 4200 + 200,
	},
	top: 4200,
};

const Sedra2ZoneGLabel = {
	...sedra2BaseLabel,
	key: "g",
	left: 6500,
	tag: {
		...baseLabel.tag,
		left: 6500,
		top: 3800 + 200,
	},
	top: 3800,
};

// TODO: update amenities name and background color
const Sedra2ZoneD: Schema.ZoneView = {
	educations: [
		{
			position: [7300, 9200],
		},
	],
	grandMosques: [
		{
			position: [6100, 7300],
		},
	],
	mapHeight: 10000,
	mapUrl: Sedra2ZoneDMap,
	unitLabel: Sedra2ZoneDLabels,
	mapWidth: 10000,
	parks: [
		{
			position: [4500, 3500],
		},
		{
			position: [4000, 3400],
		},
		{
			position: [3500, 4000],
		},
		{
			position: [2700, 3700],
		},
		{
			position: [800, 6700],
		},
	],
	unitsLayers: [
		{
			height: 10000,
			left: 0,
			svg: createSvg(
				() => import("@/assets/masterplan/riyadh-sedra-2/Neighborhood_D.svg"),
			),
			top: 0,
			width: 10000,
		},
	],
};

const Sedra2ZoneE: Schema.ZoneView = {
	educations: [
		{
			position: [9700, 5150],
		},
		{
			position: [9600, 7450],
		},
	],
	grandMosques: [
		{
			position: [6700, 5900],
		},
	],
	mapHeight: 10000,
	mapUrl: Sedra2ZoneEMap,
	unitLabel: Sedra2ZoneELabels,
	mapWidth: 10000,
	parks: [
		{
			position: [4600, 4500],
		},
	],
	unitsLayers: [
		{
			height: 10000,
			left: 0,
			svg: createSvg(
				() => import("@/assets/masterplan/riyadh-sedra-2/Neighborhood_E.svg"),
			),
			top: 0,
			width: 10000,
		},
	],
};
const Sedra2ZoneG: Schema.ZoneView = {
	educations: [
		{
			position: [8190, 1000],
		},
		{
			position: [8100, 1900],
		},
	],
	grandMosques: [
		{
			position: [8880, 2600],
		},
	],
	mapHeight: 10000,
	mapUrl: Sedra2ZoneGMap,
	mapWidth: 10000,
	unitLabel: Sedra2ZoneGLabels,
	neighborhoodCommunityCentres: [
		{
			position: [6560, 4350],
		},
	],
	parks: [
		{
			position: [9200, 1500],
		},
	],
	unitsLayers: [
		{
			height: 10000,
			left: 0,
			svg: createSvg(
				() => import("@/assets/masterplan/riyadh-sedra-2/Neighborhood_G.svg"),
			),
			top: 0,
			width: 10000,
		},
	],
};

const Sedra2ZoneTile: Schema.SvgTileObject = {
	height: 10000,
	left: 0,
	svg: Sedra2CommunitySvg,
	top: 0,
	width: 10000,
};

const Sedra2CommunityTile: Schema.CommunityTile = {
	bounds:  [
		[1900.49, 1128.57],
		[2300.49 + 405, 3350.57 + 690],
	],
	id: PROJECT_NAMES.SEDRA_2A,
	svg: CitySedra2ASvg,
};

const Sedra2CommunityTileV2: Schema.CommunityTile = {
	bounds: [
		[1742, 2063],
		[1742 + 550, 2063 + 550],
	],
	id: PROJECT_NAMES.SEDRA_2A,
	svg: CitySedra403V2Svg,
};

const Sedra2CommunityView: Schema.CommunityView = {
	id: PROJECT_NAMES.SEDRA_2A,
	mapHeight: 10000,
	mapUrl: Sedra2CommunityMap,
	mapWidth: 10000,

	tags: [AvailableTag, UnavailableTag],

	zoneLabels: [Sedra2ZoneDLabel, Sedra2ZoneELabel, Sedra2ZoneGLabel],
	zoneTiles: Sedra2ZoneTile,

	zoneViews: {
		d: Sedra2ZoneD,
		e: Sedra2ZoneE,
		g: Sedra2ZoneG,
	},
};

const Sedra2ALogo: Schema.SvgTileObject = {
	bounds: [
		[1915, 2550],
		[1915 + 400, 2550 + 400],
	],
	svg: SedraTitleSvg,
};

const Sedra2ALogoV2: Schema.SvgTileObject = {
	bounds: [
		[1810, 2285],
		[1810 + 240, 2285 + 300],
	],
	svg: Sedra403LabelSvg,
};

const Sedra2ALogoLabel: Schema.LogoLabelObject = {
	isExplore: true,
	tag: {
		bounds: [
			[2057, 2167],
			[2057 + 90, 2167 + 90],
		],
		svg: SedraTitleSvg,
	},
	text: "sedra2Title",
};

const Sedra2ALogoLabelV2: Schema.LogoLabelObject = {
	isExplore: true,
	tag: {
		bounds: [
			[1830, 2370],
			[1830 + 172, 2370 + 90],
		],
		svg: SedraTitleLabelSvg,
	},
	text: "sedra2Title",
};



// Sedra 5 Project added

const Sedra5ZoneA: Schema.ZoneView = {
	parks: [
		{
			position: [1900, 3500],
		},
		{
			position: [3000, 5650],
		},
		{
			position: [3700, 7500],
		},
		{
			position: [1800, 1200],
		},
		{
			position: [6300, 400],
		},
	],
	amenities: [
		{
			position: [7200, 7150],
		},
	],
	grandMosques: [
		{
			position: [9000, 8700],
		}
	],
	mapHeight: 10000,
	mapUrl: Sedra5ZoneAMap,
	unitLabel: Sedra5ZoneALabels,
	mapWidth: 10000,
	unitsLayers: [
		{
			height: 10000,
			left: 0,
			svg: createSvg(
				() => import("@/assets/masterplan/riyadh-sedra-5/Neighborhood_A.svg"),
			),
			top: 0,
			width: 10000,
		},
	],
};


const Sedra5ZoneB: Schema.ZoneView = {
	educations: [
		{
			position: [7100, 5100],
		},
	],
	parks: [
		{
			position: [4600, 1850],
		},
		{
			position: [6150, 4500],
		},
	],
	amenities: [
		{
			position: [1200, 7100],
		},
		{
			position: [6100, 3800],
		},
	],
	grandMosques: [
		{
			position: [5450, 2550],
		}
	],
	mapHeight: 10000,
	mapUrl: Sedra5ZoneBMap,
	unitLabel: Sedra5ZoneBLabels,
	mapWidth: 10000,
	unitsLayers: [
		{
			height: 10000,
			left: 0,
			svg: createSvg(
				() => import("@/assets/masterplan/riyadh-sedra-5/Neighborhood_B.svg"),
			),
			top: 0,
			width: 10000,
		},
	],
};

const Sedra5ZoneC: Schema.ZoneView = {
	educations: [
		{
			position: [4850, 5250],
		},
	],
	parks: [
		{
			position: [4800, 1850],
		},
		{
			position: [4800, 3100],
		},
	],
	grandMosques: [
		{
			position: [5000, 2600],
		}
	],
	mapHeight: 10000,
	mapUrl: Sedra5ZoneCMap,
	unitLabel: Sedra5ZoneCLabels,
	mapWidth: 10000,
	unitsLayers: [
		{
			height: 10000,
			left: 0,
			svg: createSvg(
				() => import("@/assets/masterplan/riyadh-sedra-5/Neighborhood_C.svg"),
			),
			top: 0,
			width: 10000,
		},
	],
};


const Sedra5ZoneALabel = {
	...sedra5BaseLabel,
	key: "a",
	left: 1100,
	tag: {
		...baseLabel.tag,
		left: 1150,
		top: 4550 + 200,
	},
	top: 4500,
};

const Sedra5ZoneBLabel = {
	...sedra5BaseLabel,
	key: "b",
	left: 3500,
	tag: {
		...baseLabel.tag,
		left: 3550,
		top: 4350 + 200,
	},
	top: 4300,
};

const Sedra5ZoneCLabel = {
	...sedra5BaseLabel,
	key: "c",
	left: 7800,
	tag: {
		...baseLabel.tag,
		left: 7850,
		top: 4550 + 200,
	},
	top: 4500,
};

const Sedra5ZoneTile: Schema.SvgTileObject = {
	height: 10000,
	left: 0,
	svg: Sedra5CommunitySvg,
	top: 0,
	width: 10000,
};

const Sedra5CommunityTile: Schema.CommunityTile = {
	bounds: [
		[1849, 1784],
		[1849 + 312, 1784 + 310],
	],
	id: PROJECT_NAMES.SEDRA_5,
	svg: CitySedra5Svg,
};

const Sedra5CommunityView: Schema.CommunityView = {
	id: PROJECT_NAMES.SEDRA_5,
	mapHeight: 10000,
	mapUrl: Sedra5CommunityMap,
	mapWidth: 10000,
	tags: [AvailableTag, UnavailableTag],
	zoneLabels: [Sedra5ZoneALabel, Sedra5ZoneBLabel, Sedra5ZoneCLabel],
	zoneTiles: Sedra5ZoneTile,
	zoneViews: {
		a: Sedra5ZoneA,
		b: Sedra5ZoneB,
		c: Sedra5ZoneC,
	},
};

const Sedra5Logo: Schema.SvgTileObject = {
	bounds: [
		[1882, 1785],
		[1882 + 220, 1785 + 300],
	],
	svg: Sedra5LabelSvg,
};

const Sedra5LogoLabel: Schema.LogoLabelObject = {
	isExplore: true,
	tag: {
		bounds: [
			[1915, 1865],
			[1915 + 140, 1865 + 98.25],
		],
		svg: SedraTitleLabelSvg,
	},
	text: "sedra5Title",
};




const Sedra4ACommunityView: Schema.CommunityView = {
	id: "sedra_4a",
	mapHeight: 10000,
	mapUrl: Sedra4ACommunityMap,
	mapWidth: 10000,

	tags: [AvailableTag, UnavailableTag],

	zoneLabels: [
		Sedra4AZoneDLabel,
		Sedra4AZoneELabel,
		Sedra4AZoneGLabel,
		Sedra4AZoneFLabel,
	],
	zoneTiles: Sedra4AZoneTile,

	zoneViews: {
		d: Sedra4BZoneD,
		e: Sedra4BZoneE,
		f: Sedra4BZoneF,
		g: Sedra4BZoneG,
	},
};
const Sedra4BCommunityView: Schema.CommunityView = {
	id: "sedra_4a",
	mapHeight: 10000,
	mapUrl: Sedra4BCommunityMap,
	mapWidth: 10000,

	tags: [AvailableTag, UnavailableTag],

	zoneLabels: [
		Sedra4BZoneDLabel,
		Sedra4BZoneELabel,
		Sedra4BZoneGLabel,
		Sedra4BZoneFLabel,
		Sedra4BZoneJLabel,
		Sedra4BZoneKLabel,
		Sedra4BZoneLLabel,
		Sedra4BZoneMLabel,
	],
	zoneTiles: Sedra4BZoneTile,

	zoneViews: {
		d: Sedra4BZoneD,
		e: Sedra4BZoneE,
		f: Sedra4BZoneF,
		g: Sedra4BZoneG,
		j: Sedra4BZoneJ,
		k: Sedra4BZoneK,
		l: Sedra4BZoneL,
		m: Sedra4BZoneM,
	},
};

const Sedra4BLogo: Schema.SvgTileObject = {
	bounds:[
		[1720, 1855],
		[1720 + 400, 1855 + 400],
	],
	svg: SedraTitleSvg,
};

const Sedra4BLogoV2: Schema.SvgTileObject = {
	bounds: [
		[1710, 1895],
		[1710 + 240, 1895 + 300],
	] ,
	svg: Sedra4LabelSvg ,
};

const Sedra3CommunityTile: Schema.CommunityTile = {
	bounds: [
		[1892.49, 1988.57],
		[1892.49 + 312, 1988.57 + 690],
	],
	id: "sedra_3",
	svg: CitySvg,
};

const Sedra3CommunityTileV2: Schema.CommunityTile = {
	bounds: [
		[1679, 2029],
		[1679 + 340, 2029 + 330],
	],
	id: "sedra_3",
	svg: CitySedra3Svg,
};

export const RiyadhSedra3: Schema.CityView = {
	centerOnInitDesktop: true,
	centerOnInitMobile: true,
	communityTiles: [
		Sedra3CommunityTile
	],
	communityViews: {
		sedra_3: {
			id: PROJECT_NAMES.SEDRA_3,
			mapHeight: 10000,
			mapUrl: CommunityMap,
			mapWidth: 10000,

			tags: [AvailableTag, UnavailableTag],

			zoneLabels: [
				SedraZoneALabel,
				SedraZoneBLabel,
				SedraZoneCLabel,
				SedraZoneGLabel,
				SedraZoneHLabel,
			],
			zoneTiles: SedraZoneTile,

			zoneViews: {
				a: SedraZoneA,
				b: SedraZoneB,
				c: SedraZoneC,
				g: SedraZoneG,
				h: SedraZoneH,
			},
		},
	},
	logoLabels: [
		{
			isExplore: true,
			tag: {
				bounds: [
					[1820, 2110],
					[1820 + 200, 2110 + 90],
				],
				svg: Sedra3Title,
			},
			text: "sedra3Title",
		},
		{
			isExplore: true,
			tag: {
				bounds: [
					[1550, 1988],
					[1550 + 200, 1988 + 18.82],
				],
				svg: AirportRoadTag,
			},
			text: "airportRoadText",
		},
		{
			isExplore: true,
			tag: {
				bounds: [
					[3750, 1970],
					[3750 + 155, 1970 + 18.82],
				],
				svg: Tag,
			},
			text: "alJanadiryahRoadText",
		},
		{
			isExplore: true,
			tag: {
				bounds: [
					[2105, 2860],
					[2105 + 200, 2860 + 18.82],
				],
				svg: RoadTag,
			},
			text: "thumamaRoadText",
		},
		{
			isExplore: true,
			tag: {
				bounds: [
					[542, 2625],
					[542 + 500, 2625 + 200],
				],
				svg: RoadTag,
			},
			text: "kingSalmanBinAbdulazizRoadText",
		},
		{
			isExplore: true,
			tag: {
				bounds: [
					[2840, 3143],
					[2840 + 500, 3143 + 200],
				],
				svg: RoadTag,
			},
			text: "northernRingRoadText",
		},
		{
			isExplore: true,
			tag: {
				bounds: [
					[1432, 1095],
					[1432 + 350, 1095 + 350],
				],
				svg: KingKhalidInternationalAirportSvg
			},
			text: "kingKhalidInternationalAirportText",
		},
		{
			isExplore: true,
			tag: {
				bounds: [
					[2438, 2596],
					[2438 + 200, 2596 + 200],
				],
				svg: MetroStationSvg
			},
			text: "metroStationText",
		},
		{
			isExplore: true,
			tag: {
				bounds: [
					[1470, 2388],
					[1470 + 280, 2388 + 280],
				],
				svg: PrincessNouraUniversitySvg,
			},
			text: "princessNouraUniversityText",
		},
		{
			isExplore: true,
			tag: {
				bounds: [
					[1313, 3304],
					[1313 + 280, 3304 + 280],
				],
				svg: ImamSaudUniversitySvg,
			},
			text: "imamSaudiUniversityText",
		},
		{
			isExplore: true,
			tag: {
				bounds: [
					[1810, 2588],
					[1810 + 200, 2588 + 200],
				],
				svg: RoshnFrontLogoSvg,
			},
			text: "roshnFrontText",
		},
		{
			isExplore: true,
			tag: {
				bounds: [
					[1753, 3067],
					[1753 + 250, 3067 + 250],
				],
				svg: ParkAvenueMallSvg,
			},
			text: "parkAvenueMallText",
		},

	],

	logos: [
		{
			bounds: [
				[1425, 1050],
				[1425 + 400, 1050 + 350],
			],
			svg: KingKhalidInternationalAirportSvg,
		},
		{
			bounds: [
				[1730, 3020],
				[1730 + 250, 3020 + 250],
			],
			svg: ParkAvenueMallSvg,
		},
		{
			bounds: [
				[1300, 3260],
				[1300 + 280, 3260 + 280],
			],
			svg: ImamSaudUniversitySvg,
		},
		{
			bounds: [
				[1790, 2540],
				[1790 + 200, 2540 + 200],
			],
			svg: RoshnFrontLogoSvg,
		},
		{
			bounds: [
				[1470, 2343],
				[1470 + 310, 2343 + 280],
			],
			svg: PrincessNouraUniversitySvg,
		},
		{
			bounds: [
				[1850, 2130],
				[1850 + 400, 2130 + 400],
			],
			svg: SedraTitleSvg,
		},
		{
			bounds: [
				[2425, 2550],
				[2425 + 200, 2550 + 200],
			],
			svg: MetroStationSvg,
		},
		{
			bounds: [
				[1540, 1900],
				[1540 + 200, 1900 + 200],
			],
			svg: AirportRoadTag,
		},
		{
			bounds: [
				[2845, 3143],
				[2845 + 260, 3143 + 200],
			],
			svg: NorthernRingRoadTag,
		},
		{
			bounds: [
				[2083, 2772],
				[2083 + 260, 2772 + 200],
			],
			svg: ThumamaRoadTag,
		},
		{
			bounds: [
				[500, 2610],
				[500 + 500, 2610 + 230],
			],
			svg: KingSalmanBinAbdulazizRoadTag,
		},
		{
			bounds: [
				[3740, 1836],
				[3740 + 300, 1836 + 300],
			],
			svg: AlJanadiryahRoadTag,
		},

	],
	mapHeight: 4096,
	mapUrl: CityMap,
	mapWidth: 4096,
};




export const RiyadhSedra3V2: Schema.CityView = {
	centerOnInitDesktop: true,
	centerOnInitMobile: true,
	communityTiles: [
		Sedra3CommunityTileV2
	],
	communityViews: {
		sedra_3: {
			id: PROJECT_NAMES.SEDRA_3,
			mapHeight: 10000,
			mapUrl: CommunityMap,
			mapWidth: 10000,

			tags: [AvailableTag, UnavailableTag],

			zoneLabels: [
				SedraZoneALabel,
				SedraZoneBLabel,
				SedraZoneCLabel,
				SedraZoneGLabel,
				SedraZoneHLabel,
			],
			zoneTiles: SedraZoneTile,

			zoneViews: {
				a: SedraZoneA,
				b: SedraZoneB,
				c: SedraZoneC,
				g: SedraZoneG,
				h: SedraZoneH,
			},
		},
	},
	logoLabels: [
		{
			isExplore: true,
			tag: {
				bounds: [
					[1800, 2110],
					[1800 + 130, 2110 + 90],
				],
				svg: Sedra3Title,
			},
			text: "sedra3Title",
		},
		{
			isExplore: true,
			tag: {
				bounds: [
					[1437, 770],
					[1437 + 80, 770 + 100],
				],
				svg: SedraTitleLabelSvg,
			},
			text: "kingKhalidInternationalAirportText",
		},
		{
			isExplore: true,
			tag: {
				bounds: [
					[1820, 850],
					[1820 + 80, 850 + 100],
				],
				svg: SedraTitleLabelSvg,
			},
			text: "kingKhalidInternationalAirportKMText",
		},
		{
			isExplore: true,
			tag: {
				bounds: [
					[690, 1712],
					[690 + 80, 1712 + 100],
				],
				svg: SedraTitleLabelSvg,
			},
			text: "riyadhExpoText",
		},
		{
			isExplore: true,
			tag: {
				bounds: [
					[690, 1800],
					[690 + 80, 1800 + 100],
				],
				svg: SedraTitleLabelSvg,
			},
			text: "riyadhExpoKMText",
		},
		{
			isExplore: true,
			tag: {
				bounds: [
					[1030, 2130],
					[1030 + 80, 2130 + 100],
				],
				svg: SedraTitleLabelSvg,
			},
			text: "princessNouraUniversityText",
		},
		{
			isExplore: true,
			tag: {
				bounds: [
					[1030, 2210],
					[1030 + 80, 2210 + 100],
				],
				svg: SedraTitleLabelSvg,
			},
			text: "princessNouraUniversityKM",
		},
		{
			isExplore: true,
			tag: {
				bounds: [
					[1258, 2410],
					[1258 + 80, 2410 + 100],
				],
				svg: SedraTitleLabelSvg,
			},
			text: "roshnFrontText",
		},
		{
			isExplore: true,
			tag: {
				bounds: [
					[1258, 2480],
					[1258 + 80, 2480 + 100],
				],
				svg: SedraTitleLabelSvg,
			},
			text: "roshnFrontKM",
		},
	],

	logos: [		
		{
			bounds: [
				[1775, 2025],
				[1775 + 210, 2025 + 300],
			],
			svg: Sedra3LabelSvg,
		},
		{
			bounds: [
				[1842, 680],
				[1842 + 80, 680 + 100],
			],
			svg: AirportSvg,
		},
		{
			bounds: [
				[710, 1630],
				[710 + 80, 1630 + 100],
			],
			svg: ExpoSvg,
		},
		{
			bounds: [
				[1045, 2060],
				[1045 + 80, 2060 + 100],
			],
			svg: UniversitySvg,
		},
		{
			bounds: [
				[1270, 2330],
				[1270 + 80, 2330 + 100],
			],
			svg: ShoppingSvg,
		},

	],
	mapHeight: 4096,
	mapUrl: SedraCityView,
	mapWidth: 4096,
};



const Sedra4BLogoLabel: Schema.LogoLabelObject = {
	isExplore: true,
	tag: {
		bounds: [
			[1835, 1900],
			[1835 + 160, 1900 + 98.25],
		],
		svg: SedraTitleSvg,
	},
	text: "sedra4Title",
};

const Sedra4BLogoLabelV2: Schema.LogoLabelObject = {
	isExplore: true,
	tag: {
		bounds: [
			[1748, 1964],
			[1748 + 150, 1964 + 120],
		],
		svg: SedraTitleLabelSvg,
	},
	text: "sedra4Title",
};


export const Riyadh: Schema.CityView = {
	...RiyadhSedra3,
	communityTiles: [
		...RiyadhSedra3.communityTiles, 
		Sedra4BCommunityTile
	],
	communityViews: {
		...RiyadhSedra3.communityViews,
		sedra_4a: enableSedra4Bb === "TRUE" ? Sedra4BCommunityView : Sedra4ACommunityView,
	},
	logoLabels: [
		...(RiyadhSedra3.logoLabels ?? []), 
		Sedra4BLogoLabel
	],
	logos: [
		...(RiyadhSedra3.logos ?? []), 
		Sedra4BLogo
	],
};



export const RiyadhSedraCommunity: Schema.CityView = {
	...RiyadhSedra3,
	communityTiles: [
		...RiyadhSedra3.communityTiles,
		Sedra4BCommunityTile,
		Sedra2CommunityTile,
	],
	communityViews: {
		...RiyadhSedra3.communityViews,
		sedra_2a: Sedra2CommunityView,
		sedra_4a: enableSedra4Bb === "TRUE" ? Sedra4BCommunityView : Sedra4ACommunityView,
	},
	logoLabels: [
		...(RiyadhSedra3.logoLabels ?? []),
		Sedra4BLogoLabel,
		Sedra2ALogoLabel,
	],
	logos: [
		...(RiyadhSedra3.logos ?? []), 
		Sedra4BLogo, 
		Sedra2ALogo,
	],
};


const WarefaCommunityTileV2: Schema.CommunityTile = {
	bounds: [
		[3643, 2174],
		[3643 + 250, 2174 + 250],
	],
	id: PROJECT_NAMES.SEDRA_8,
	svg: CityWarefaSVG,
};


export const RiyadhSedraCommunityV2: Schema.CityView = {
	...RiyadhSedra3V2,
	communityTiles: [
		...RiyadhSedra3V2.communityTiles,
		Sedra4BCommunityTileV2,
		Sedra2CommunityTileV2,
	],
	communityViews: {
		...RiyadhSedra3V2.communityViews,
		sedra_2a: Sedra2CommunityView,
		sedra_4a: enableSedra4Bb === "TRUE" ? Sedra4BCommunityView : Sedra4ACommunityView,
	},
	logoLabels: [
		...(RiyadhSedra3V2.logoLabels ?? []),
		Sedra4BLogoLabelV2,
		Sedra2ALogoLabelV2,
	],
	logos: [
		...(RiyadhSedra3V2.logos ?? []), 
		Sedra4BLogoV2, 
		Sedra2ALogoV2,
	],
};

export const RiyadhSedraCommunityWithSedra5: Schema.CityView = {
	...RiyadhSedra3V2,
	communityTiles: [
		...RiyadhSedra3V2.communityTiles,
		Sedra4BCommunityTileV2,
		Sedra2CommunityTileV2,
		Sedra5CommunityTile,
		WarefaCommunityTileV2
	],
	communityViews: {
		...RiyadhSedra3V2.communityViews,
		sedra_2a: Sedra2CommunityView,
		sedra_4a: enableSedra4Bb === "TRUE" ? Sedra4BCommunityView : Sedra4ACommunityView,
		sedra_5: Sedra5CommunityView,
	},
	logoLabels: [
		...(RiyadhSedra3V2.logoLabels ?? []),
		Sedra4BLogoLabelV2,
		Sedra2ALogoLabelV2,
		Sedra5LogoLabel,
	],
	logos: [
		...(RiyadhSedra3V2.logos ?? []), 
		Sedra4BLogoV2, 
		Sedra2ALogoV2, 
		Sedra5Logo,
	],
};

