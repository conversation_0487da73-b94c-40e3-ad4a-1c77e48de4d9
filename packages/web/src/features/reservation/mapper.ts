import {
	HOME_FINANCE_CONFIG,
	HOME_PURCHASE_METHOD,
	PROPERTY_STATUS,
	RESERVATION_DOWNPAYMENT_TIMEOUT_CASH,
	RESERVATION_DOWNPAYMENT_TIMEOUT_HOME_FINANCE,
	RESERVATION_FEE,
	RESERVATION_PAYMENT_TIMEOUT,
} from "@/constants";
import {
	getPropertyAdditionalRooms,
	getPropertyNote,
	mapInstalmentPlan,
	mapToPropertyGroupFromData,
} from "@/pages/property-group/property-group.mapper";
import { Property, PropertyUnit } from "@/types/property-type";
import {
	FinanceInfo,
	FullUserProfile,
	GetSupportedBanksResponse,
	ReservationData as ReservationDataResponse,
	SupportedBank as StrapiSupportedBank,
	SourceOfIncome as StrapiSourceOfIncome,
	GccBank as StrapiGccBank,
	ReservationProgress,
} from "@roshn/shared";
import type {
	Employer,
	GetEmployerResponse,
	GetGccBanksResponse,
	GetPropertyGroupResponse,
	GetSourceOfIncomeResponse,
	GetWorkSectorResponse,
	SourceOfIncome,
	WorkSector,
} from "@roshn/shared";
import { RESERVATION_STEP } from "./constants";
import {
	CheckoutInfo,
	FinancialInfo,
	GccBank,
	PaymentPlanInfo,
	ReservationUserData,
	SignatureInfo,
	SupportedBank,
} from "./types";
import { ReservationData as ReservationDataRedux } from "./redux";
import { Address } from "../billing-address";
import { calculatePaymentTimeout } from "./utils";

export function mapResponseFinancialOptions(
	financeInfo?: FinanceInfo,
): FinancialInfo {
	return {
		downPayment: financeInfo?.homeFinancingMetadata?.downPayment || 0,
		// TODO: ask backend to add this field,
		expectedDownPaymentPercentage:
			financeInfo?.homeFinancingMetadata?.expectedDownPaymentPercentage || 0,

		financeDuration:
			financeInfo?.homeFinancingMetadata?.financeDuration ||
			HOME_FINANCE_CONFIG.MAX_YEARS,

		financingMethod:
			financeInfo?.financialPlan == "UNSELECTED"
				? null
				: financeInfo?.financialPlan,

		isFirstHouse:
			financeInfo?.homeFinancingMetadata?.firstTimeHomeBuyer || true,

		isSakani:
			financeInfo?.sakaniBeneficiary == "UNSELECTED"
				? null
				: financeInfo?.sakaniBeneficiary == "ELIGIBLE",

		maximumAmountOfFinance:
			financeInfo?.homeFinancingMetadata?.maximumAmountOfFinance || 0,

		monthlyInstallment:
			financeInfo?.homeFinancingMetadata?.monthlyInstallment || 0,

		offerDate: new Date().toString(),

		propertyPrice: financeInfo?.homeFinancingMetadata?.propertyPrice || 0,
		// TODO: ask backend to add this field
		salary: financeInfo?.netSalaryAfterGOSI
			? Math.max(financeInfo?.netSalaryAfterGOSI, 0)
			: null,
		totalObligation:
			financeInfo?.homeFinancingMetadata?.totalMonthlyFinancialObligations || 0,
	} as FinancialInfo;
}

export function mapResponseReservationData(
	input: ReservationDataResponse,
): Partial<ReservationDataRedux> {
	try {
		return {
			checkoutInfo: mapResponseGetCheckoutInfo(input),
			downpaymentDueTime: getDownpaymentDueTime(
				input.urfSignTimestamp || "",
				input.financialOption?.financialPlan || "",
			),
			financialInfo: mapResponseFinancialOptions(input.financialOption),
			id: input.reservationId,
			paymentPlan: mapInstalmentPlan(input.paymentPlan),
			paymentPlanInfo: mapPaymentPlanInfo(input?.paymentPlan),
			reservationNumber: input.saleRegisterNumber,
			salesAdvisorInfo: input.salesAdvisorInfo?.salesAdvisorAssigned
				? {
						email: input.salesAdvisorInfo.salesAdvisorEmail,
						name: input.salesAdvisorInfo.salesAdvisorName,
						phone: input.salesAdvisorInfo.salesAdvisorPhone,
					}
				: null,
			unit: mapUnitBasicInformation(input),
			salesRegisterCreationTime:
				new Date(input.salesRegisterCreationTime || "").getTime() +
				calculatePaymentTimeout(input.paymentMaxTimeout),
			paymentMaxTimeout: input.paymentMaxTimeout,
			jobTitle: input?.jobTitle || "",
			employerId: input?.employerId || "",
			otherEmployerName: input?.otherEmployerName || "",
			sourceOfIncome: input?.sourceOfIncome || "",
			sourceOfIncomeOther: input?.sourceOfIncomeOther || "",
			workSector: input?.workSector || "",
			screeningResultStatus: input.screeningResultStatus || false,
			ReservationFeeAdjustment: input.ReservationFeeAdjustment,
			cancellationEligibility: input.cancellationEligibility,
			reservationCancellationStatus: input.reservationCancellationStatus,
			hidePaymentTimer: input.hidePaymentTimer,
		};
	} catch (err) {
		return {};
	}
}
export function mapResponseUpdateInformation(
	input: ReservationDataResponse,
): Partial<ReservationDataRedux> {
	return {
		...mapResponseReservationData(input),
		checkoutInfo: {
			total: Number(input.reservationFee || RESERVATION_FEE),
		} as CheckoutInfo,
		paymentPlan: mapInstalmentPlan(input.paymentPlan),
		paymentPlanInfo: mapPaymentPlanInfo(input?.paymentPlan),
	};
}
export function mapResponseGetCheckoutInfo(
	input: ReservationDataResponse,
): CheckoutInfo {
	return {
		checkoutId: input.checkoutId,
		paymentType: "Reservation Fee",
		paymentUrl: input.checkoutId
			? `${import.meta.env.VITE_APP_HYPERPAY_WIDGET_URL}?checkoutId=${
					input.checkoutId
				}`
			: "",
		integrity: input.integrity,
		total: Number(input.reservationFee || RESERVATION_FEE),
		salesRegisterCreationTime: input.salesRegisterCreationTime,
		paymentMaxTimeout: input.paymentMaxTimeout,
		hidePaymentTimer: input.hidePaymentTimer,
	} as CheckoutInfo;
}
export function mapResponseGetPaymentResult(
	input: ReservationDataResponse,
): Partial<ReservationDataRedux> {
	return {
		...mapResponseReservationData(input),
		paymentStatus: input.paymentStatus,
	};
}

export function mapResponseGetSignatureInfo(input: any): SignatureInfo {
	return input;
}

export function mapUnitBasicInformation(
	input: ReservationDataResponse,
): PropertyUnit {
	const unitBasicInfo = JSON.parse(
		input.onboardPropertyInfo.propertyMetadata || "{}",
	);

	const unitType = unitBasicInfo.UNIT_TYPE?.toLowerCase();
	const typology = unitBasicInfo.TYPOLOGY;
	const typologyGroup = typology.split(" ")[0];
	const propertyGroupSlug = [unitType, typology.replaceAll(" ", "-")].join("-");

	return {
		bankInfo: {
			bankAccountName:
				unitBasicInfo.EscrowAccountDetailsCollection.AccountOwnerName || "",
			bankAccountNumber:
				unitBasicInfo.EscrowAccountDetailsCollection.VirtualEscrowIBAN || "",
			bankName: unitBasicInfo.EscrowAccountDetailsCollection.BankName || "",
		},

		//TODO: find the correct value
		community: unitBasicInfo.CommunityName || "",

		currentProgress: unitBasicInfo.CONSTRUCTION_COMPELLATION || 0,

		estDeliveryDate: input.handOverDate,

		id: unitBasicInfo.UnitCode || "",

		neighborhood: unitBasicInfo.NBHDNAME || "",

		project: unitBasicInfo.ProjectName,

		propertyName: unitBasicInfo.UNIT_TYPE + " " + unitBasicInfo.TYPOLOGY,

		typology: propertyGroupSlug,

		unitCategory: unitBasicInfo.UNIT_CATEGORY || "",
		unitCode: unitBasicInfo.UnitCode,
		unitInfo: {
			additionalRooms: getPropertyAdditionalRooms({
				driverRoom: unitBasicInfo.NUMBER_OF_DRIVERROOMS,
				laundryRoom: unitBasicInfo.LAUNDRY_ROOM,
				maidRoom: unitBasicInfo.NUMBER_OF_MAIDROOMS,
			}),
			bathroom: unitBasicInfo.NUMBER_OF_BATHROOMS,
			bedroom: unitBasicInfo.NUMBER_OF_BEDROOMS,
			builtUpArea: Number(unitBasicInfo.BUILT_UP_AREA_SQM).toFixed(0),
			driverRoom: unitBasicInfo.NUMBER_OF_DRIVERROOMS,
			facade: unitBasicInfo.ELEVATION_TYPE,
			grossFloorArea: Number(unitBasicInfo.UNIT_SELLABLE_AREA_SQM).toFixed(0),
			maidRoom: unitBasicInfo.NUMBER_OF_MAIDROOMS,
			note: getPropertyNote({
				driverRoom: unitBasicInfo.NUMBER_OF_DRIVERROOMS,
				maidRoom: unitBasicInfo.NUMBER_OF_MAIDROOMS,
			}),
			parking: unitBasicInfo.NUMBER_OF_PARKING,
			balcony: unitBasicInfo.BALCONY,
			plotArea: Number(unitBasicInfo.LAND_AREA_SQM).toFixed(0),
			roofTerrace: unitBasicInfo.TERRACE?.toLowerCase() === "yes",
			typologyGroup,
		},
		unitName: `${unitBasicInfo.UNIT_NUMBER || ""}`,
		orientation: unitBasicInfo.ORIENTATION || "",
		unitNumber: unitBasicInfo.UNIT_NUMBER,
		unitPrice: {
			amountRETT: input.onboardPropertyInfo.propertyRETT,
			price: input.onboardPropertyInfo.propertyPrice,
			priceIncludingRETT: input.onboardPropertyInfo.propertyPriceIncludeRETT,
		},

		unitStatus:
			PROPERTY_STATUS[String(unitBasicInfo.UNIT_STATUS).toUpperCase() || ""] ||
			PROPERTY_STATUS.UNAVAILABLE,
		unitType: unitBasicInfo.UNIT_TYPE,

		updateAt: unitBasicInfo.updatedAt || "",
	};
}

export function mapPropertyGroupInfo(
	input: GetPropertyGroupResponse,
): Partial<Property> {
	const propertyGroup = mapToPropertyGroupFromData(input.data);
	if (!propertyGroup) return {};
	return propertyGroup;
}

export function mapResponseInitPaymentCheckout(
	input: ReservationDataResponse,
): CheckoutInfo {
	return {
		paymentType: "Reservation Fee",
		paymentUrl: "",
		total: Number(input.reservationFee),
		hidePaymentTimer: input.hidePaymentTimer,
	} as CheckoutInfo;
}

export function mapSupportedBankList(
	input: GetSupportedBanksResponse,
): SupportedBank[] {
	return input.data.map((item: StrapiSupportedBank) => ({
		bankAccountId: item?.attributes?.AccountId || "",
		logo: item?.attributes?.BankLogo?.data?.attributes?.url || "",
		name: item?.attributes?.AccountName || "",
		registryId: item?.attributes?.RegestryId || "",
	}));
}

export function mapGccBankList(input: GetGccBanksResponse): GccBank[] {
	return input.data.map((item: StrapiGccBank) => ({
		bankId: item?.attributes?.BankId || "",
		bankName: item?.attributes?.BankName || "",
	}));
}

export function mapSourceOfIncomeList(
	input: GetSourceOfIncomeResponse,
): SourceOfIncome[] {
	return input.data.map((item) => ({
		id: item.id,
		attributes: {
			Lookup_Code: item.attributes.Lookup_Code || "",
			Lookup_Meaning: item.attributes.Lookup_Meaning || "",
			Enabled: item.attributes.Enabled || true,
			Status: item.attributes.Status || "",
		},
	}));
}

export function mapWorkSectorList(input: GetWorkSectorResponse): WorkSector[] {
	return input.data.map((item) => ({
		id: item.id,
		attributes: {
			Lookup_Code: item.attributes.Lookup_Code || "",
			Lookup_Meaning: item.attributes.Lookup_Meaning || "",
			Enabled: item.attributes.Enabled || true,
		},
	}));
}

export function mapEmployerList(input: GetEmployerResponse): Employer[] {
	return input.data.map((item) => ({
		id: item.id,
		attributes: {
			EmployerId: item.attributes.EmployerId || "",
			employerName: item.attributes.employerName || "",
		},
	}));
}

export function mapUserInformation(
	input: FullUserProfile,
): ReservationUserData {
	return {
		email: input.mail || "",
		// [personal name] [father's personal name] [grandfather's personal name] [FAMILY/TRIBAL NAME].
		name: [
			input.nafath?.englishFirstName || "",
			input.nafath?.englishSecondName || "",
			input.nafath?.englishThirdName || "",
			input.nafath?.englishLastName || "",
		].join(" "),
		nameAr: [
			input.nafath?.firstName || "",
			input.nafath?.fatherName || "",
			input.nafath?.grandFatherName || "",
			input.nafath?.familyName || "",
		].join(" "),
		nationalId: input.nafath?.id || "",
		phoneNumber: input.telephoneNumber || "",
	};
}

export function mapBillingAddress(input: FullUserProfile): Address {
	const nationalAddress = input?.nafath?.nationalAddress?.[0];
	return {
		additionalAddress: "",
		address: [
			nationalAddress?.district || "",
			nationalAddress?.streetName || "",
		].join(" "),
		city: nationalAddress?.city || "",
		country: "SA",
		email: input.mail || "",

		firstName: [
			input.nafath?.englishFirstName || "",
			input.nafath?.englishSecondName || "",
		].join(" "),

		lastName: [
			input.nafath?.englishThirdName || "",
			input.nafath?.englishLastName || "",
		].join(" "),
		//TODO: remove hard code
		postcode: nationalAddress?.postCode || "",
	};
}

/**
 * @description map reservation status from backend to frontend state
 * @param input backend reservation state
 * @returns frontend reservation state
 */
export function mapReservationStatus(
	input: ReservationProgress,
): RESERVATION_STEP {
	switch (input) {
		case "IN_PROGRESS_ONBOARDING_KITCHEN_COLOR":
			return RESERVATION_STEP.ONBOARD_KITCHEN_PREFERENCE;
		case "IN_PROGRESS_ONBOARDING":
			return RESERVATION_STEP.ONBOARD_INFORMATION;

		case "IN_PROGRESS_E_SIGNATURE":
			return RESERVATION_STEP.ONBOARD_DETAIL;

		case "WAITING_FOR_E_SIGNATURE_CALLBACK":
			return RESERVATION_STEP.ONBOARD_WAITING;

		case "CANCELLED_RESERVATION":
		case "EXPIRED_DURING_ONBOARD":
		case "EXPIRED_DURING_E_SIGNATURE":
		case "EXPIRED_DURING_FINAL_SPA_SIGNATURE":
		case "EXPIRED_DURING_PAYMENT":
			return RESERVATION_STEP.NOT_STARTED;

		case "IN_PROGRESS_PAYMENT":
			return RESERVATION_STEP.ONBOARD_INVOICE;

		case "IN_PROGRESS_PAYMENT_CHECKOUT":
			return RESERVATION_STEP.ONBOARD_PAYMENT;

		case "COMPLETED_RESERVATION":
			return RESERVATION_STEP.FUNDING_START;

		case "IN_PROGRESS_SALE_OFFER_SIGNATURE":
			return RESERVATION_STEP.FUNDING_WAITING;

		case "COMPLETED_SALE_OFFER_SIGNATURE":
			return RESERVATION_STEP.FUNDING_SIGNED;

		case "COMPLETED_HOME_FINANCE":
			return RESERVATION_STEP.PURCHASE_START;

		case "COMPLETED_PURCHASE":
			return RESERVATION_STEP.PURCHASE_DONE;

		case "WAITING_FOR_FINAL_SPA_CALLBACK":
			return RESERVATION_STEP.PURCHASE_WAITING;

		default:
			return RESERVATION_STEP.NOT_STARTED;
	}
}

export function getDownpaymentDueTime(
	startDate: string,
	purchaseMethod: string,
): number {
	if (startDate && purchaseMethod === HOME_PURCHASE_METHOD.CASH)
		return new Date(startDate).getTime() + RESERVATION_DOWNPAYMENT_TIMEOUT_CASH;

	if (startDate && purchaseMethod === HOME_PURCHASE_METHOD.CASH)
		return (
			new Date(startDate).getTime() +
			RESERVATION_DOWNPAYMENT_TIMEOUT_HOME_FINANCE
		);

	return 0;
}

function mapPaymentPlanInfo(input?: string): PaymentPlanInfo | undefined {
	if (!input) return undefined;
	try {
		const paymentPlanParsed = JSON.parse(input);
		return {
			currency: paymentPlanParsed?.Currency,
			id: paymentPlanParsed?.Id,
			message: paymentPlanParsed?.msg ?? null,
			paymentPlanAdjustment: paymentPlanParsed?.PaymentPlanAdjustment ?? false,
			paymentPlanName: paymentPlanParsed?.PaymentPlanName,
			planDetailsCollection:
				paymentPlanParsed?.PlanDetailsCollection?.map((item: any) => ({
					billingTask: item?.BillingTask,
					completionProgress: item?.CompletionProgress,
					description: item?.Description,
					dueDate: item?.DueDate,
					dueType: item?.DueType,
					id: item?.Id,
					installment: item?.Installment,
					installmentAmount: item?.InstallmentAmount,
					installmentAmountBeforeDiscount:
						item?.InstallmentAmountBeforeDiscount,
					installmentNo: item?.InstallmentNo,
					installmentNoArabic: item?.InstallmentNoArabic,
					installmentTotalAmount: item?.InstallmentTotalAmount,
					numberOfDays: item?.NumberOfDays ?? null,
					vat: item?.VAT,
				})) ?? [],
			reservationFeeAdjustment:
				paymentPlanParsed?.ReservationFeeAdjustment ?? false,
			unitCode: paymentPlanParsed?.UnitCode,
			unitDownPaymentAmount: paymentPlanParsed?.UnitDownPaymentAmount,
			unitPrice: paymentPlanParsed?.UnitPrice,
			unitReservationFeeAmount: paymentPlanParsed?.UnitReservationFeeAmount,
			unitType: paymentPlanParsed?.UnitType,
		} as PaymentPlanInfo;
	} catch {
		return undefined;
	}
}
