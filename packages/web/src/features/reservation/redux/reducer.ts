/* eslint-disable import/no-default-export */
import { Address } from "@/features/billing-address";
import { InstallmentPlan, Property, PropertyUnit } from "@/types/property-type";
import { PayloadAction, createSlice } from "@reduxjs/toolkit";
import { mergeDeepRight } from "ramda";
import {
	RESERVATION_ERROR,
	RESERVATION_IN_PROGRESS_STATUS,
	RESERVATION_STEP,
} from "../constants";
import {
	CheckoutInfo,
	FinancialInfo,
	PaymentPlanInfo,
	ReservationUserData,
	SignatureInfo,
} from "../types";
import { getCurrentReservation } from "./actions";
import {
	CancellationData,
	CancellationResponse,
	ReservationCancellationStatus,
} from "@roshn/shared";

export type ReservationData = {
	acceptTerms: boolean;
	checkoutCreated?: boolean;
	checkoutInfo: CheckoutInfo;
	downpaymentDueTime?: number;
	financialInfo: FinancialInfo;
	id: string | null;
	kitchenPreferenceDocumentAvailable?: boolean;
	kitchenPreferenceRequired?: boolean;
	paymentPlan: InstallmentPlan;
	paymentPlanInfo?: PaymentPlanInfo;
	paymentStatus?: string | null;
	propertyGroup: Property;
	// must have information of a reservation
	reservationNumber: string;
	salesAdvisorInfo: {
		email: string;
		name: string;
		phone: string;
	} | null;
	signatureInfo: SignatureInfo;
	unit: PropertyUnit;
	userData: ReservationUserData;
	salesRegisterCreationTime?: number;
	paymentMaxTimeout?: number;
	source?: string;
	workSector: string;
	employerId: string;
	sourceOfIncome: string;
	jobTitle: string;
	sourceOfIncomeOther?: string;
	otherEmployerName?: string;
	screeningResultStatus?: boolean;
	isSkipResigningDocs?: boolean;
	ReservationFeeAdjustment?: boolean;
	cancellationEligibility?: boolean;
	reservationCancellationStatus?: ReservationCancellationStatus;
	hidePaymentTimer?: boolean;
};

export type ReservationState = {
	data: ReservationData;
	error: RESERVATION_ERROR | null;
	errorTraceId: string[];
	// this state is used to keep track of any background process in BE still in progress.
	// we want to restict user interaction when this state is happening
	inProgressBackgroundStep: RESERVATION_IN_PROGRESS_STATUS | undefined;
	isLoading: boolean;
	kitchenPreferenceRequired?: boolean;
	step: RESERVATION_STEP;
	timeout: number;
	isExpired?: boolean;
	isPaymentInProgress: boolean;
	salesRegisterCreationTime?: number;
	paymentMaxTimeout?: number;
	isScreeningUnresolvedOrPositive?: boolean;
	hasJustSignedURF?: boolean;
	openCancelReservationDialog: boolean;
	cancellationData?: CancellationData;
	cancellationResponse?: CancellationResponse;
	showCancellationDocumentModal?: boolean;
	reservationId?: string;
	hidePaymentTimer?: boolean;
};

export type GetCurrentReservationPayload = {
	communityName: string;
	groupId: string;
	reservationId?: string;
	unitId: string;
};

export type StartReservationPayload = {
	kitchenColor?: string;
	unitId: string;
};

export type PersonalInfoUpdatePayload = {
	isSakani: boolean;
	salary: number;
};

export const initialState: ReservationState = {
	data: {
		acceptTerms: false,
		checkoutCreated: false,
		checkoutInfo: {} as CheckoutInfo,
		financialInfo: {} as FinancialInfo,
		id: null,
		paymentPlan: {} as InstallmentPlan,
		paymentPlanInfo: undefined,
		paymentStatus: null,
		propertyGroup: {} as Property,
		reservationNumber: "",
		salesAdvisorInfo: null,
		signatureInfo: {} as SignatureInfo,
		unit: {} as PropertyUnit,
		userData: {} as ReservationUserData,
		workSector: "",
		employerId: "",
		sourceOfIncome: "",
		jobTitle: "",
		sourceOfIncomeOther: "",
		otherEmployerName: "",
		isSkipResigningDocs: false,
		ReservationFeeAdjustment: true,
	},
	error: null,
	errorTraceId: [],
	inProgressBackgroundStep: undefined,
	isLoading: true,
	kitchenPreferenceRequired: false,
	step: RESERVATION_STEP.NOT_STARTED,
	timeout: 0,
	isExpired: false,
	isPaymentInProgress: false,
	salesRegisterCreationTime: undefined,
	paymentMaxTimeout: 0,
	isScreeningUnresolvedOrPositive: false,
	hasJustSignedURF: false,
	openCancelReservationDialog: false,
	cancellationData: {},
	showCancellationDocumentModal: false,
	hidePaymentTimer: false,
	reservationId: "",
};

export const reservationSlice = createSlice({
	extraReducers: (builder) => {
		builder.addCase(getCurrentReservation.pending, (state) => {
			state.isLoading = true;
		});
		builder.addCase(getCurrentReservation.fulfilled, (state) => {
			state.isLoading = false;
		});
	},
	initialState,
	name: "reservation",
	reducers: {
		// eslint-disable-next-line @typescript-eslint/no-empty-function
		backToReservation: () => {},

		clearCheckoutInfo: (state) => {
			state.data.checkoutInfo = {} as CheckoutInfo;
		},
		setIsExpired: (state, action: PayloadAction<boolean>) => {
			state.isExpired = action.payload;
		},
		setIsPaymentInProgress: (state, action: PayloadAction<boolean>) => {
			state.isPaymentInProgress = action.payload;
		},

		reservationTimeout: (state) => {
			state.error = RESERVATION_ERROR.TIMEOUT;
		},

		resetReservationState: () => {
			return initialState;
		},

		setBillingAddress: (state, { payload }: { payload: Address }) => {
			state.data.checkoutInfo.billingAddress = payload;
			state.data.acceptTerms = false;

			if (state.error == RESERVATION_ERROR.BILLING_ADDRESS_NOT_VALID) {
				state.error = null;
			}
		},

		setBillingAddressError: (state) => {
			state.error = RESERVATION_ERROR.BILLING_ADDRESS_NOT_VALID;
			state.data.acceptTerms = false;
			state.data.checkoutInfo.checkoutId = "";
		},

		setError: (state, { payload }: { payload: RESERVATION_ERROR | null }) => {
			state.error = payload;
		},

		setErrorTraceId: (state, { payload }: { payload: string }) => {
			state.errorTraceId = [...state.errorTraceId, payload];
		},
		setPropertyGroup: (
			state,
			{ payload }: PayloadAction<Partial<Property>>,
		) => {
			state.data.propertyGroup = {
				...(state.data.propertyGroup || {}),
				...payload,
			};
		},

		setReservation: (
			state,
			{
				payload,
			}: PayloadAction<{
				data: Partial<ReservationData>;
				error?: RESERVATION_ERROR | null;
				inProgressBackgroundStep: RESERVATION_IN_PROGRESS_STATUS | undefined;
				kitchenPreferenceRequired?: boolean;
				step?: RESERVATION_STEP;
				timeout?: number;
				salesRegisterCreationTime?: string;
				paymentMaxTimeout?: number;
				hidePaymentTimer?: boolean;
			}>,
		) => {
			state.data = mergeDeepRight(state.data, payload.data) as ReservationData;
			state.kitchenPreferenceRequired = payload.kitchenPreferenceRequired;
			state.step = payload.step ?? state.step;
			if ("inProgressBackgroundStep" in payload) {
				state.inProgressBackgroundStep = payload.inProgressBackgroundStep;
			}
			state.error =
				payload.error === null ? payload.error : payload.error ?? state.error;
			state.timeout = payload.timeout ?? state.timeout;
			state.salesRegisterCreationTime =
				payload?.data?.salesRegisterCreationTime;
			state.paymentMaxTimeout = payload?.data?.paymentMaxTimeout;
			if ("hidePaymentTimer" in payload) {
				state.hidePaymentTimer = payload.hidePaymentTimer;
			}
		},

		setUnit: (state, { payload }: PayloadAction<Partial<PropertyUnit>>) => {
			state.data.unit = {
				...(state.data.unit || {}),
				...payload,
			};
		},

		setUserData: (state, { payload }: { payload: ReservationUserData }) => {
			state.data.userData = payload;
		},
		updateAgreeTerms: (state, { payload }) => {
			state.data.acceptTerms = payload;
		},
		updateFinanceOptions: (state, { payload }) => {
			state.data.financialInfo = {
				...state.data.financialInfo,
				...payload,
			};
		},
		updateEmploymentInfo: (
			state,
			{
				payload,
			}: PayloadAction<{
				workSector?: string;
				employerId?: string;
				sourceOfIncome?: string;
				jobTitle?: string;
				sourceOfIncomeOther?: string;
				otherEmployerName?: string;
			}>,
		) => {
			state.data.workSector = payload.workSector ?? state.data.workSector;
			state.data.employerId = payload.employerId ?? state.data.employerId;
			state.data.sourceOfIncome =
				payload.sourceOfIncome ?? state.data.sourceOfIncome;
			state.data.jobTitle = payload.jobTitle ?? state.data.jobTitle;
			state.data.sourceOfIncomeOther =
				payload.sourceOfIncomeOther ?? state.data.sourceOfIncomeOther;
			state.data.otherEmployerName =
				payload.otherEmployerName ?? state.data.otherEmployerName;
		},
		updateReservation: (state, { payload }) => {
			state.data = mergeDeepRight(state.data, payload.data) as ReservationData;
			state.step = payload.step;
		},
		updateStep: (state, { payload }) => {
			state.step = payload;
		},
		updateIsSkipResigningDocs: (state, { payload }: { payload: boolean }) => {
			state.data.isSkipResigningDocs = payload;
		},
		updateWorldCheckResult: (state, { payload }) => {
			state.isScreeningUnresolvedOrPositive = payload;
		},
		setHasJustSignedURF: (state, action: PayloadAction<boolean>) => {
			state.hasJustSignedURF = action.payload;
		},
		setOpenCancelReservationDialog: (state, action: PayloadAction<boolean>) => {
			state.openCancelReservationDialog = action.payload;
		},
		setShowCancellationDocumentModal: (state) => {
			state.showCancellationDocumentModal =
				!state.showCancellationDocumentModal;
		},
		setCancellationData(
			state,
			action: PayloadAction<Partial<CancellationData>>,
		) {
			state.cancellationData = {
				...state.cancellationData,
				...action.payload,
			};
		},
		resetCancellationData(state) {
			state.cancellationData = {};
		},
		setCancellationResponseData(
			state,
			action: PayloadAction<Partial<CancellationResponse>>,
		) {
			state.cancellationResponse = {
				...state.cancellationResponse,
				...action.payload,
			};
		},
		setReservationId: (state, action: PayloadAction<string>) => {
			state.reservationId = action.payload;
		},
		setReservationCancellationStatus(
			state,
			action: PayloadAction<keyof typeof ReservationCancellationStatus>,
		) {
			state.data.reservationCancellationStatus =
				ReservationCancellationStatus[action.payload];
		},
		resetReservationCancellationStatus(state) {
			state.data.reservationCancellationStatus = undefined;
		},
	},
});

export const reservationReducer = reservationSlice.reducer;

export const {
	setUnit,
	setReservation,
	updateFinanceOptions,
	updateEmploymentInfo,
	updateStep,
	updateIsSkipResigningDocs,
	updateAgreeTerms,
	reservationTimeout,
	backToReservation,
	resetReservationState,
	setBillingAddress,
	setUserData,
	setBillingAddressError,
	setIsExpired,
	setIsPaymentInProgress,
	setHasJustSignedURF,
	setCancellationData,
	setOpenCancelReservationDialog,
	resetCancellationData,
	setShowCancellationDocumentModal,
	setReservationId,
	setReservationCancellationStatus,
	resetReservationCancellationStatus,
} = reservationSlice.actions;
