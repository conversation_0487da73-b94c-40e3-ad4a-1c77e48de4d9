import { Address } from "@/features/billing-address";
import { RESERVATION_ERROR_CODE_MAP } from "../constants";

export type FinancialInfo = {
	downPayment: number;
	expectedDownPaymentPercentage: number;
	financeDuration: number;
	financingMethod: "CASH" | "HOME_FINANCE" | "UNSELECTED";
	isFirstHouse: boolean;
	isSakani: boolean;
	maximumAmountOfFinance: number;
	monthlyInstallment: number;
	offerDate: string;
	propertyPrice: number;
	salary: number;
	selectedBankName?: string;
	totalObligation: number;
};

export type PaymentPlan = {
	finalWithVATAmount: string;
	plan: {
		amount: string;
		mileStone: string;
	}[];
};

export type CheckoutInfo = {
	billingAddress: Address;
	checkoutId: string;
	hidePaymentTimer?: boolean;
	integrity?: string;
	paymentMaxTimeout?: number;
	paymentType: string;
	paymentUrl: string;
	salesRegisterCreationTime?: number;
	total: number;
};

export type SignatureInfo = {
	signed: boolean;
	signingUrl: string;
};

// Full CRM payment plan info shape stored as-is from backend JSON
export type PaymentPlanInfo = {
	currency: string;
	id: number;
	message: string | null;
	paymentPlanAdjustment: boolean;
	paymentPlanName: string;
	planDetailsCollection: PlanDetailDTO[];
	reservationFeeAdjustment: boolean;
	unitCode: string;
	unitDownPaymentAmount: number;
	unitPrice: number;
	unitReservationFeeAmount: number;
	unitType: string;
};

export type PlanDetailDTO = {
	billingTask: string;
	completionProgress: number;
	description: string;
	dueDate: string;
	dueType: string;
	id: number;
	installment: number;
	installmentAmount: number;
	installmentAmountBeforeDiscount: number;
	installmentNo: number;
	installmentNoArabic: string;
	installmentTotalAmount: number;
	numberOfDays: number | null;
	vat: number;
};

export type SupportedBank = {
	bankAccountId: string;
	logo: string;
	name: string;
	registryId: string;
};

export type GccBank = {
	bankId: string;
	bankName: string;
};

export type ReservationUserData = {
	email: string;
	name: string;
	nameAr: string;
	nationalId: string;
	phoneNumber: string;
};

export type ActiveSection = "kitchenPreference" | "propertyDetails" | null;

export function isReservationErrorCode(
	code: string,
): code is keyof typeof RESERVATION_ERROR_CODE_MAP {
	return Object.values(RESERVATION_ERROR_CODE_MAP).includes(code);
}
