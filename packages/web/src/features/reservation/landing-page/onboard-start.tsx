import * as React from "react";
import { Container } from "@/components/container/container";
import { AppTheme, tabletMQ } from "@/theme";
import { css, useTheme } from "@emotion/react";
import { useFeatureFlag, useFeatureFlagApi } from "@/features/feature-flags";

import { Col, Row } from "@/components/grid";
import { useNavigateParams } from "@/hooks/navigate-with-params";
import { useAppPathGenerator } from "@/routes";
import { useAppDispatch, useAppSelector } from "@/store";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams, useSearchParams } from "@remix-run/react";
import { StepInformationCard } from "../components/step-information-card";
import { UnitOverviewCard } from "../../unit-overview-card/unit-overview-card";
import { useSelectADiffUnit } from "../hook/close-flow";
import { reservationSlice, startReservation } from "../redux";
import { CollapseWrap } from "../components/collapse-wrap";
import { UnitOverall } from "../components/unit-overall";
import { AppPaths } from "@/routes/app-paths";
import { trackStartReservation } from "../redux/actions-reservation";
import { getFacadeThumbnail } from "@/pages/property-group/property-mapper-utils";
import { RoshnLoadingModal } from "@/features/loading";
import { KitchenPreferences } from "../components/kitchen-preferences";
import { Typography } from "@/components/typography";
import { ActiveSection } from "../types";
import { Header } from "../components/header";
import { shareStyles } from "./share-styles";
import * as shared from "@roshn/shared";
import {
	ReservationHoldError,
	ReservationLimitError,
} from "../components/start-reservation-error-dialog";
import { RESERVATION_ERROR, RESERVATION_ERROR_CODE_MAP } from "../constants";
import { premiumResidentModalSlice } from "@/store/global-modal-slices";
import { RDSButton } from "@roshn/ui-kit";

const styles = {
	infoSection: (theme: AppTheme) =>
		css({
			alignItems: "center",
			display: "flex",
			flexDirection: "row",
			gap: theme.spaces.xs3,
			justifyContent: "flex-start",
			marginTop: theme.spaces.xs4,
		}),
	selectBtnSection: (theme: AppTheme) =>
		css({
			alignItems: "center",
			display: "flex",
			justifyContent: "center",
			marginBottom: theme.spaces.lg,
		}),
	title: (theme: AppTheme) =>
		css({
			marginBottom: theme.spaces.md,
			marginTop: theme.spaces.lg,
		}),
};

export function OnboardStart() {
	const theme = useTheme();
	const generateAppPath = useAppPathGenerator();
	const dispatch = useAppDispatch();
	const [loadingStartReservation, setLoadingStartReservation] =
		React.useState(false);
	const { t } = useTranslation();
	const navigate = useNavigateParams();
	const navigateTo = useNavigate();
	const reservation = useAppSelector((state) => state.reservation);
	const params = useParams();
	const [searchParams] = useSearchParams();
	const [hasHoldError, sethasHoldError] = React.useState(false);
	const [exceedLimit, setExceedLimit] = React.useState(false);
	const [kitchenColor, setKitchenColor] = React.useState<string | null>(null);
	const [errorTraceId, setErrorTraceId] = React.useState<string[]>([]);
	const { data: featureFlagAPI } = useFeatureFlagApi();
	const { enableNewProfilePage } = featureFlagAPI || {};

	// Handlers for changing the open state of each collapsible
	// Opening one collapsible will close the other

	const [activeSection, setActiveSection] =
		React.useState<ActiveSection>("kitchenPreference");

	// Function to toggle the active section
	const toggleSection = (section: ActiveSection) => {
		setActiveSection((prevSection) =>
			prevSection === section ? null : section,
		);
	};
	const startReservationStep = {
		buttonText: t("features.propertyReservation.startReservation"),
		description: t(
			`features.propertyReservation.youCanEasilyPurchaseYourHomeOnlineDescription`,
		),
		stepData: {
			activeIndex: 0,
			items: [
				{ title: t("features.propertyReservation.reserveYourUnit") },
				{ title: t("features.propertyReservation.reviewPaymentInformation") },
				{ title: t("features.propertyReservation.completeThePurchase") },
			],
		},
		title: t("features.propertyReservation.youCanEasilyPurchaseYourHomeOnline"),
	};

	const handleGoBack = useSelectADiffUnit();

	const handleSelectADiffUnit = () => {
		handleGoBack();
	};

	const handleSelectKitchenColor = (color: string) => {
		setKitchenColor(color);
	};
	const reservationService = shared.useInjection<shared.ReservationService>(
		shared.ReservationService,
	);
	const [allowExceedReservation] = useFeatureFlag(
		"AllowExceedReservation",
	);
	const handleOnClick = async () => {
		try {
			setLoadingStartReservation(true);
			dispatch(trackStartReservation());
			await dispatch(
				startReservation({
					kitchenColor: kitchenColor || "",
					unitId: params.unitId || "",
				}),
			).unwrap();
			navigate(
				`../${generateAppPath(AppPaths.reservationOnboardInformation, {
					communityName: params.communityName || "",
					groupId: params.groupId || "",
					unitId: params.unitId || "",
				})}`,
				searchParams,
			);
		} catch (err: any) {
			if (err.code === RESERVATION_ERROR_CODE_MAP.HOLD_ERROR) {
				sethasHoldError(true);
				setErrorTraceId([...errorTraceId, err?.__TRACE_ID__]);
			}
			if (
				err.code ===
				RESERVATION_ERROR_CODE_MAP.ERROR_RESIDENT_ID_CANNOT_DO_RESERVATION
			) {
				dispatch(
					reservationSlice.actions.setError(
						RESERVATION_ERROR.ERROR_RESIDENT_ID_CANNOT_DO_RESERVATION,
					),
				);
				dispatch(
					//@ts-ignore
					premiumResidentModalSlice.actions.openModal({
						errorType: "featureFlagOff",
					}),
				);
			}
		} finally {
			setLoadingStartReservation(false);
		}
	};

	const unit = reservation.data.unit;
	const propertyGroup = reservation.data.propertyGroup;
	const kitchenColors = propertyGroup?.kitchenColors?.data;
	const kitchenPreferenceRequired = reservation.kitchenPreferenceRequired;
	// const isEligibleForReservation = unit.unitStatus == PROPERTY_STATUS.AVAILABLE;
	//TODO: find the correct way to determine here (check reservation timeout)

	const thumbnail = getFacadeThumbnail(propertyGroup, unit.unitInfo?.facade);

	return (
		<>
			<Container css={shareStyles.wrapper(theme, enableNewProfilePage)}>
				<Header />

				<Row gutter={0} tablet={{ gutter: 24 }}>
					<Col
						span={24}
						tablet={{
							span: 16,
						}}
					>
						<div css={shareStyles.propertyCardSection}>
							<UnitOverviewCard
								community={unit?.community}
								unitName={unit?.unitName}
								bedrooms={unit?.unitInfo?.bedroom || 0}
								bathrooms={unit?.unitInfo?.bathroom || 0}
								estDeliveryDate={unit?.estDeliveryDate}
								price={unit?.unitPrice?.price}
								note={unit?.unitInfo?.note}
								squareSpace={unit?.unitInfo?.plotArea}
								thumbnail={thumbnail}
								facade={unit?.unitInfo?.facade}
								type={unit?.unitType}
								typologyGroup={propertyGroup?.typologyGroup}
								orientation={unit?.orientation}
								unitId={unit?.unitCode}
							/>
						</div>
						<div css={styles.selectBtnSection}>
							<RDSButton
								text={t("features.propertyReservation.selectADifferentUnit")}
								variant="tertiary"
								onClick={handleSelectADiffUnit}
							/>
						</div>
						<div css={styles.title}>
							<Typography variant="subtitleL">
								{t("features.propertyDiscovery.title")}
							</Typography>
						</div>
						{kitchenPreferenceRequired && (
							<CollapseWrap
								title={t("features.kitchenPreferences.title")}
								isOpen={activeSection === "kitchenPreference"}
								onToggle={() => toggleSection("kitchenPreference")}
								content={
									<KitchenPreferences
										kitchenColors={kitchenColors}
										handleConfirmKitchenColor={handleSelectKitchenColor}
									/>
								}
							/>
						)}
						<CollapseWrap
							isOpen={activeSection === "propertyDetails"}
							onToggle={() => toggleSection("propertyDetails")}
							title={t("features.propertyDiscovery.propertyDetail")}
							content={
								<UnitOverall propertyGroup={propertyGroup} unit={unit} />
							}
						/>
					</Col>
					<Col
						mobile={{
							span: 24,
						}}
						tablet={{
							span: 8,
						}}
					>
						<div>
							<StepInformationCard
								step={startReservationStep}
								onClick={
									// isEligibleForReservation &&
									kitchenColor || !kitchenPreferenceRequired
										? handleOnClick
										: undefined
								}
							>
								{!!kitchenPreferenceRequired && (
									<div css={styles.infoSection}>
										<Typography variant="bodyS">
											{t("features.kitchenPreferences.confirmButtonInfo")}
										</Typography>
									</div>
								)}
							</StepInformationCard>
						</div>
					</Col>
				</Row>
			</Container>
			<ReservationLimitError
				open={exceedLimit}
				onViewMyProperties={() =>
					navigateTo(generateAppPath(AppPaths.myProperties))
				}
				onClose={() => setExceedLimit(false)}
			/>
			<ReservationHoldError
				open={hasHoldError}
				errorTraceId={errorTraceId}
				onClose={() => sethasHoldError(false)}
			/>
			<RoshnLoadingModal
				isOpen={
					!!reservation.inProgressBackgroundStep || loadingStartReservation
				}
				title={t("features.propertyReservation.waitingForOnboardingMessage")}
			/>
		</>
	);
}
