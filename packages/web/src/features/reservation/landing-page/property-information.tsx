import { Col } from "@/components/grid";
import { useAppSelector } from "@/store";
import { useTranslation } from "react-i18next";
import { UnitOverviewCard } from "../../unit-overview-card/unit-overview-card";
import { CollapseWrap } from "../components/collapse-wrap";
import { UnitOverall } from "../components/unit-overall";
import { getFacadeThumbnail } from "@/pages/property-group/property-mapper-utils";
import { shareStyles } from "./share-styles";

export function PropertyInformation() {
	const reservation = useAppSelector((state) => state.reservation);

	const unit = reservation.data.unit;
	const propertyGroup = reservation.data.propertyGroup;


	const thumbnail = getFacadeThumbnail(propertyGroup, unit.unitInfo.facade);
	const { t } = useTranslation();
	return (
		<Col
			span={24}
			tablet={{
				span: 16,
			}}
		>
			<div css={shareStyles.propertyCardSection}>
				<UnitOverviewCard
					community={unit.community}
					unitName={unit.unitName}
					bedrooms={unit.unitInfo.bedroom}
					bathrooms={unit.unitInfo.bathroom}
					estDeliveryDate={unit.estDeliveryDate}
					price={unit.unitPrice.price}
					note={unit.unitInfo.note}
					squareSpace={unit.unitInfo.plotArea}
					thumbnail={thumbnail}
					facade={unit.unitInfo.facade}
					type={unit.unitType}
					typologyGroup={propertyGroup.typologyGroup}
					orientation={unit.orientation}
					unitId={unit?.unitCode}
				/>
			</div>

			<CollapseWrap
				title={t("features.propertyDiscovery.propertyDetail")}
				content={<UnitOverall propertyGroup={propertyGroup} unit={unit} />}
			/>
		</Col>
	);
}
