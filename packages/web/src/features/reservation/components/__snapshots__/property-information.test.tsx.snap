// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`PropertyInformation > should match snapshot 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="roshn-ui-ujs77z-PropertyInformation"
      >
        <div
          class="roshn-ui-45o6wl-PropertyInformation"
        >
          <div
            class="roshn-ui-3s6sm6-UnitOverviewCard"
          >
            <div
              class="RoshnUIGrid-col roshn-ui-1h8nkcf-Col"
              span="8"
            >
              <div
                class="roshn-ui-1onmw23-UnitOverviewCard"
              />
            </div>
            <div
              class="roshn-ui-1wxlfao-UnitOverviewCard"
              span="16"
            >
              <div
                class="roshn-ui-lqndl3-UnitOverviewCard"
              >
                <p
                  class="typography roshn-ui-h4vedp"
                />
                <p
                  class="typography roshn-ui-1c8cbye-UnitOverviewCard"
                >
                   0
                   
                  <span
                    class="roshn-ui-ksur9-UnitOverviewCard"
                  >
                    Price excl. RETT
                  </span>
                </p>
              </div>
              <p
                class="typography roshn-ui-tx0058-UnitOverviewCard"
              >
                Unit
                 
              </p>
              <div
                class="roshn-ui-cecmtg-UnitOverviewCard"
              >
                <p
                  class="typography roshn-ui-1p2zgin-UnitOverviewCard"
                >
                  <svg
                    class="roshn-ui-92frcv-UnitOverviewCard"
                    fill="none"
                    height="1em"
                    viewBox="0 0 15 12"
                    width="1em"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M12.5 2.5H1V1C1 0.867392 0.947322 0.740215 0.853553 0.646447C0.759785 0.552679 0.632608 0.5 0.5 0.5C0.367392 0.5 0.240215 0.552679 0.146447 0.646447C0.0526784 0.740215 0 0.867392 0 1V11C0 11.1326 0.0526784 11.2598 0.146447 11.3536C0.240215 11.4473 0.367392 11.5 0.5 11.5C0.632608 11.5 0.759785 11.4473 0.853553 11.3536C0.947322 11.2598 1 11.1326 1 11V9H14V11C14 11.1326 14.0527 11.2598 14.1464 11.3536C14.2402 11.4473 14.3674 11.5 14.5 11.5C14.6326 11.5 14.7598 11.4473 14.8536 11.3536C14.9473 11.2598 15 11.1326 15 11V5C15 4.33696 14.7366 3.70107 14.2678 3.23223C13.7989 2.76339 13.163 2.5 12.5 2.5ZM1 3.5H5.5V8H1V3.5ZM6.5 8V3.5H12.5C12.8978 3.5 13.2794 3.65804 13.5607 3.93934C13.842 4.22064 14 4.60218 14 5V8H6.5Z"
                      fill="currentColor"
                    />
                  </svg>
                  3
                </p>
                <p
                  class="typography roshn-ui-1p2zgin-UnitOverviewCard"
                >
                  <svg
                    class="roshn-ui-92frcv-UnitOverviewCard"
                    fill="none"
                    height="1em"
                    viewBox="0 0 16 16"
                    width="1em"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M4 14.75C4 14.8983 3.95601 15.0433 3.8736 15.1667C3.79119 15.29 3.67406 15.3861 3.53701 15.4429C3.39997 15.4997 3.24917 15.5145 3.10368 15.4856C2.9582 15.4566 2.82456 15.3852 2.71967 15.2803C2.61478 15.1754 2.54335 15.0418 2.51441 14.8963C2.48547 14.7508 2.50032 14.6 2.55709 14.463C2.61386 14.3259 2.70999 14.2088 2.83332 14.1264C2.95666 14.044 3.10166 14 3.25 14C3.44891 14 3.63968 14.079 3.78033 14.2197C3.92098 14.3603 4 14.5511 4 14.75ZM5.25 12C5.10166 12 4.95666 12.044 4.83332 12.1264C4.70999 12.2088 4.61386 12.3259 4.55709 12.463C4.50033 12.6 4.48547 12.7508 4.51441 12.8963C4.54335 13.0418 4.61478 13.1754 4.71967 13.2803C4.82456 13.3852 4.9582 13.4566 5.10368 13.4856C5.24917 13.5145 5.39997 13.4997 5.53701 13.4429C5.67406 13.3861 5.79119 13.29 5.8736 13.1667C5.95601 13.0433 6 12.8983 6 12.75C6 12.5511 5.92098 12.3603 5.78033 12.2197C5.63968 12.079 5.44891 12 5.25 12ZM1.25 12C1.10166 12 0.95666 12.044 0.833323 12.1264C0.709986 12.2088 0.613856 12.3259 0.557091 12.463C0.500325 12.6 0.485472 12.7508 0.514411 12.8963C0.54335 13.0418 0.614781 13.1754 0.71967 13.2803C0.82456 13.3852 0.958197 13.4566 1.10368 13.4856C1.24917 13.5145 1.39997 13.4997 1.53701 13.4429C1.67406 13.3861 1.79119 13.29 1.8736 13.1667C1.95601 13.0433 2 12.8983 2 12.75C2 12.5511 1.92098 12.3603 1.78033 12.2197C1.63968 12.079 1.44891 12 1.25 12ZM3.25 10C3.10166 10 2.95666 10.044 2.83332 10.1264C2.70999 10.2088 2.61386 10.3259 2.55709 10.463C2.50032 10.6 2.48547 10.7508 2.51441 10.8963C2.54335 11.0418 2.61478 11.1754 2.71967 11.2803C2.82456 11.3852 2.9582 11.4566 3.10368 11.4856C3.24917 11.5145 3.39997 11.4997 3.53701 11.4429C3.67406 11.3861 3.79119 11.29 3.8736 11.1667C3.95601 11.0433 4 10.8983 4 10.75C4 10.5511 3.92098 10.3603 3.78033 10.2197C3.63968 10.079 3.44891 10 3.25 10ZM16 2.5C16 2.63261 15.9473 2.75979 15.8536 2.85355C15.7598 2.94732 15.6326 3 15.5 3H13.7069L11.9663 4.74125L10.6125 12.6656C10.582 12.8467 10.5023 13.0158 10.3821 13.1546C10.2618 13.2933 10.1057 13.3963 9.93086 13.4522C9.75599 13.5081 9.56911 13.5148 9.39069 13.4715C9.21227 13.4282 9.04921 13.3367 8.91938 13.2069L2.79438 7.08187C2.66438 6.95193 2.57271 6.78868 2.52944 6.61004C2.48616 6.4314 2.49296 6.24429 2.54909 6.06926C2.60521 5.89424 2.70849 5.73807 2.84758 5.6179C2.98666 5.49774 3.15618 5.41824 3.3375 5.38813L11.2587 4.03375L13.1462 2.14625C13.1927 2.09983 13.2479 2.06303 13.3086 2.03793C13.3693 2.01284 13.4343 1.99995 13.5 2H15.5C15.6326 2 15.7598 2.05268 15.8536 2.14645C15.9473 2.24022 16 2.36739 16 2.5ZM10.8881 5.11188L3.5 6.375L9.625 12.5L10.8881 5.11188Z"
                      fill="currentColor"
                    />
                  </svg>
                  3
                </p>
                <p
                  class="typography roshn-ui-1p2zgin-UnitOverviewCard"
                >
                  <svg
                    class="roshn-ui-92frcv-UnitOverviewCard"
                    fill="none"
                    height="1em"
                    viewBox="0 0 16 16"
                    width="1em"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M14 1.5H2C1.17157 1.5 0.5 2.17157 0.5 3V13C0.5 13.8284 1.17157 14.5 2 14.5H14C14.8284 14.5 15.5 13.8284 15.5 13V3C15.5 2.17157 14.8284 1.5 14 1.5Z"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M12.5 8V4.5H9"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M7 11.5H3.5V8"
                      stroke="currentColor"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                  10
                   
                  sqm
                </p>
              </div>
              <p
                class="typography roshn-ui-1p2zgin-UnitOverviewCard"
              >
                 
                Unit Orientation
                :
                 
                -
              </p>
              <p
                class="typography roshn-ui-1p2zgin-UnitOverviewCard"
              >
                note
              </p>
            </div>
          </div>
        </div>
        <ul
          class="roshn-ui-blhfex-PropertyInformation"
        >
          <li>
            <h6
              class="typography roshn-ui-1aw5sid"
            >
              Plot area
            </h6>
            <p
              class="typography roshn-ui-1aw5sid"
            >
              10 sqm
            </p>
          </li>
          <li>
            <h6
              class="typography roshn-ui-1aw5sid"
            >
              Gross floor area
            </h6>
            <p
              class="typography roshn-ui-1aw5sid"
            >
              10 sqm
            </p>
          </li>
          <li>
            <h6
              class="typography roshn-ui-1aw5sid"
            >
              Bedrooms
            </h6>
            <p
              class="typography roshn-ui-1aw5sid"
            >
              3
            </p>
          </li>
          <li>
            <h6
              class="typography roshn-ui-1aw5sid"
            >
              Bathrooms
            </h6>
            <p
              class="typography roshn-ui-1aw5sid"
            >
              3
            </p>
          </li>
          <li>
            <h6
              class="typography roshn-ui-1aw5sid"
            >
              Roof terrace
            </h6>
            <p
              class="typography roshn-ui-1aw5sid"
            >
              Yes
            </p>
          </li>
          <li>
            <h6
              class="typography roshn-ui-1aw5sid"
            >
              Additional rooms
            </h6>
            <p
              class="typography roshn-ui-1aw5sid"
            >
              3
            </p>
          </li>
          <li>
            <h6
              class="typography roshn-ui-1aw5sid"
            >
              note
            </h6>
          </li>
        </ul>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="roshn-ui-ujs77z-PropertyInformation"
    >
      <div
        class="roshn-ui-45o6wl-PropertyInformation"
      >
        <div
          class="roshn-ui-3s6sm6-UnitOverviewCard"
        >
          <div
            class="RoshnUIGrid-col roshn-ui-1h8nkcf-Col"
            span="8"
          >
            <div
              class="roshn-ui-1onmw23-UnitOverviewCard"
            />
          </div>
          <div
            class="roshn-ui-1wxlfao-UnitOverviewCard"
            span="16"
          >
            <div
              class="roshn-ui-lqndl3-UnitOverviewCard"
            >
              <p
                class="typography roshn-ui-h4vedp"
              />
              <p
                class="typography roshn-ui-1c8cbye-UnitOverviewCard"
              >
                 0
                 
                <span
                  class="roshn-ui-ksur9-UnitOverviewCard"
                >
                  Price excl. RETT
                </span>
              </p>
            </div>
            <p
              class="typography roshn-ui-tx0058-UnitOverviewCard"
            >
              Unit
               
            </p>
            <div
              class="roshn-ui-cecmtg-UnitOverviewCard"
            >
              <p
                class="typography roshn-ui-1p2zgin-UnitOverviewCard"
              >
                <svg
                  class="roshn-ui-92frcv-UnitOverviewCard"
                  fill="none"
                  height="1em"
                  viewBox="0 0 15 12"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12.5 2.5H1V1C1 0.867392 0.947322 0.740215 0.853553 0.646447C0.759785 0.552679 0.632608 0.5 0.5 0.5C0.367392 0.5 0.240215 0.552679 0.146447 0.646447C0.0526784 0.740215 0 0.867392 0 1V11C0 11.1326 0.0526784 11.2598 0.146447 11.3536C0.240215 11.4473 0.367392 11.5 0.5 11.5C0.632608 11.5 0.759785 11.4473 0.853553 11.3536C0.947322 11.2598 1 11.1326 1 11V9H14V11C14 11.1326 14.0527 11.2598 14.1464 11.3536C14.2402 11.4473 14.3674 11.5 14.5 11.5C14.6326 11.5 14.7598 11.4473 14.8536 11.3536C14.9473 11.2598 15 11.1326 15 11V5C15 4.33696 14.7366 3.70107 14.2678 3.23223C13.7989 2.76339 13.163 2.5 12.5 2.5ZM1 3.5H5.5V8H1V3.5ZM6.5 8V3.5H12.5C12.8978 3.5 13.2794 3.65804 13.5607 3.93934C13.842 4.22064 14 4.60218 14 5V8H6.5Z"
                    fill="currentColor"
                  />
                </svg>
                3
              </p>
              <p
                class="typography roshn-ui-1p2zgin-UnitOverviewCard"
              >
                <svg
                  class="roshn-ui-92frcv-UnitOverviewCard"
                  fill="none"
                  height="1em"
                  viewBox="0 0 16 16"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M4 14.75C4 14.8983 3.95601 15.0433 3.8736 15.1667C3.79119 15.29 3.67406 15.3861 3.53701 15.4429C3.39997 15.4997 3.24917 15.5145 3.10368 15.4856C2.9582 15.4566 2.82456 15.3852 2.71967 15.2803C2.61478 15.1754 2.54335 15.0418 2.51441 14.8963C2.48547 14.7508 2.50032 14.6 2.55709 14.463C2.61386 14.3259 2.70999 14.2088 2.83332 14.1264C2.95666 14.044 3.10166 14 3.25 14C3.44891 14 3.63968 14.079 3.78033 14.2197C3.92098 14.3603 4 14.5511 4 14.75ZM5.25 12C5.10166 12 4.95666 12.044 4.83332 12.1264C4.70999 12.2088 4.61386 12.3259 4.55709 12.463C4.50033 12.6 4.48547 12.7508 4.51441 12.8963C4.54335 13.0418 4.61478 13.1754 4.71967 13.2803C4.82456 13.3852 4.9582 13.4566 5.10368 13.4856C5.24917 13.5145 5.39997 13.4997 5.53701 13.4429C5.67406 13.3861 5.79119 13.29 5.8736 13.1667C5.95601 13.0433 6 12.8983 6 12.75C6 12.5511 5.92098 12.3603 5.78033 12.2197C5.63968 12.079 5.44891 12 5.25 12ZM1.25 12C1.10166 12 0.95666 12.044 0.833323 12.1264C0.709986 12.2088 0.613856 12.3259 0.557091 12.463C0.500325 12.6 0.485472 12.7508 0.514411 12.8963C0.54335 13.0418 0.614781 13.1754 0.71967 13.2803C0.82456 13.3852 0.958197 13.4566 1.10368 13.4856C1.24917 13.5145 1.39997 13.4997 1.53701 13.4429C1.67406 13.3861 1.79119 13.29 1.8736 13.1667C1.95601 13.0433 2 12.8983 2 12.75C2 12.5511 1.92098 12.3603 1.78033 12.2197C1.63968 12.079 1.44891 12 1.25 12ZM3.25 10C3.10166 10 2.95666 10.044 2.83332 10.1264C2.70999 10.2088 2.61386 10.3259 2.55709 10.463C2.50032 10.6 2.48547 10.7508 2.51441 10.8963C2.54335 11.0418 2.61478 11.1754 2.71967 11.2803C2.82456 11.3852 2.9582 11.4566 3.10368 11.4856C3.24917 11.5145 3.39997 11.4997 3.53701 11.4429C3.67406 11.3861 3.79119 11.29 3.8736 11.1667C3.95601 11.0433 4 10.8983 4 10.75C4 10.5511 3.92098 10.3603 3.78033 10.2197C3.63968 10.079 3.44891 10 3.25 10ZM16 2.5C16 2.63261 15.9473 2.75979 15.8536 2.85355C15.7598 2.94732 15.6326 3 15.5 3H13.7069L11.9663 4.74125L10.6125 12.6656C10.582 12.8467 10.5023 13.0158 10.3821 13.1546C10.2618 13.2933 10.1057 13.3963 9.93086 13.4522C9.75599 13.5081 9.56911 13.5148 9.39069 13.4715C9.21227 13.4282 9.04921 13.3367 8.91938 13.2069L2.79438 7.08187C2.66438 6.95193 2.57271 6.78868 2.52944 6.61004C2.48616 6.4314 2.49296 6.24429 2.54909 6.06926C2.60521 5.89424 2.70849 5.73807 2.84758 5.6179C2.98666 5.49774 3.15618 5.41824 3.3375 5.38813L11.2587 4.03375L13.1462 2.14625C13.1927 2.09983 13.2479 2.06303 13.3086 2.03793C13.3693 2.01284 13.4343 1.99995 13.5 2H15.5C15.6326 2 15.7598 2.05268 15.8536 2.14645C15.9473 2.24022 16 2.36739 16 2.5ZM10.8881 5.11188L3.5 6.375L9.625 12.5L10.8881 5.11188Z"
                    fill="currentColor"
                  />
                </svg>
                3
              </p>
              <p
                class="typography roshn-ui-1p2zgin-UnitOverviewCard"
              >
                <svg
                  class="roshn-ui-92frcv-UnitOverviewCard"
                  fill="none"
                  height="1em"
                  viewBox="0 0 16 16"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M14 1.5H2C1.17157 1.5 0.5 2.17157 0.5 3V13C0.5 13.8284 1.17157 14.5 2 14.5H14C14.8284 14.5 15.5 13.8284 15.5 13V3C15.5 2.17157 14.8284 1.5 14 1.5Z"
                    stroke="currentColor"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M12.5 8V4.5H9"
                    stroke="currentColor"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M7 11.5H3.5V8"
                    stroke="currentColor"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                10
                 
                sqm
              </p>
            </div>
            <p
              class="typography roshn-ui-1p2zgin-UnitOverviewCard"
            >
               
              Unit Orientation
              :
               
              -
            </p>
            <p
              class="typography roshn-ui-1p2zgin-UnitOverviewCard"
            >
              note
            </p>
          </div>
        </div>
      </div>
      <ul
        class="roshn-ui-blhfex-PropertyInformation"
      >
        <li>
          <h6
            class="typography roshn-ui-1aw5sid"
          >
            Plot area
          </h6>
          <p
            class="typography roshn-ui-1aw5sid"
          >
            10 sqm
          </p>
        </li>
        <li>
          <h6
            class="typography roshn-ui-1aw5sid"
          >
            Gross floor area
          </h6>
          <p
            class="typography roshn-ui-1aw5sid"
          >
            10 sqm
          </p>
        </li>
        <li>
          <h6
            class="typography roshn-ui-1aw5sid"
          >
            Bedrooms
          </h6>
          <p
            class="typography roshn-ui-1aw5sid"
          >
            3
          </p>
        </li>
        <li>
          <h6
            class="typography roshn-ui-1aw5sid"
          >
            Bathrooms
          </h6>
          <p
            class="typography roshn-ui-1aw5sid"
          >
            3
          </p>
        </li>
        <li>
          <h6
            class="typography roshn-ui-1aw5sid"
          >
            Roof terrace
          </h6>
          <p
            class="typography roshn-ui-1aw5sid"
          >
            Yes
          </p>
        </li>
        <li>
          <h6
            class="typography roshn-ui-1aw5sid"
          >
            Additional rooms
          </h6>
          <p
            class="typography roshn-ui-1aw5sid"
          >
            3
          </p>
        </li>
        <li>
          <h6
            class="typography roshn-ui-1aw5sid"
          >
            note
          </h6>
        </li>
      </ul>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
