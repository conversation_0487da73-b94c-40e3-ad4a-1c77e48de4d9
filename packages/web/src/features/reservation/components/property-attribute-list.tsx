import { DownloadIcon } from "@/components/icons";
import { Typography } from "@/components/typography";
import { AppTheme } from "@/theme";
import { Property } from "@/types/property-type";
import { css, useTheme } from "@emotion/react";
import { RDSButton } from "@roshn/ui-kit";
import { useTranslation } from "react-i18next";

const styles = {
	downloadIcon: (theme: AppTheme) =>
		css({
			marginRight: theme.spaces.sm,
		}),
	infoDownload: css({
		[`& a:hover`]: {
			textDecoration: "none",
		},
		alignItems: "center",
		display: "flex",

		justifyContent: "center",
	}),
	infoItem: (theme: AppTheme) =>
		css({
			alignItems: "center",
			display: "flex",
			justifyContent: "space-between",
			marginBottom: theme.spaces.md,
		}),
	infoLabel: (theme: AppTheme) =>
		css({
			color: theme.colors.text.secondary,
			textTransform: "uppercase",
		}),
	infoList: css({
		[`& li`]: {
			listStyleType: "none",
		},
		padding: 0,
	}),
	infoNote: (theme: AppTheme) =>
		css({
			marginBottom: theme.spaces.lg,
		}),
	infoValue: (theme: AppTheme) =>
		css({
			color: theme.colors.text.primary,
		}),
};

type PropertyAttributeListProps = {
	data: Property;
};

export function PropertyAttributeList({ data }: PropertyAttributeListProps) {
	const { t } = useTranslation();
	const theme = useTheme();

	return (
		<div css={{ paddingInline: theme.spaces.xs3 }}>
			<ul css={styles.infoList}>
				<li css={styles.infoItem}>
					<Typography variant="subtitleS" css={styles.infoLabel}>
						{t("features.propertyDiscovery.plotArea")}
					</Typography>
					<Typography css={styles.infoValue}>
						{`${data.information?.plotArea ?? "N/A"} ${data.information?.plotArea ? t("common.sqm") : ""}`.trim()}
					</Typography>
				</li>
				<li css={styles.infoItem}>
					<Typography variant="subtitleS" css={styles.infoLabel}>
						{t("features.propertyDiscovery.grossFloorArea")}
					</Typography>
					<Typography css={styles.infoValue}>
						{`${data.information?.grossFloorArea ?? "N/A"} ${data.information?.grossFloorArea ? t("common.sqm") : ""}`.trim()}
					</Typography>
				</li>
				<li css={styles.infoItem}>
					<Typography variant="subtitleS" css={styles.infoLabel}>
						{t("features.propertyDiscovery.bedroom")}
					</Typography>
					<Typography css={styles.infoValue}>
						{`${data.information?.bedroom ?? "N/A"}`}
					</Typography>
				</li>
				<li css={styles.infoItem}>
					<Typography variant="subtitleS" css={styles.infoLabel}>
						{t("features.propertyDiscovery.bathroom")}
					</Typography>
					<Typography css={styles.infoValue}>
						{`${data.information?.bathroom ?? "N/A"}`}
					</Typography>
				</li>
				<li css={styles.infoItem}>
					<Typography variant="subtitleS" css={styles.infoLabel}>
						{t("features.propertyDiscovery.roofTerrace")}
					</Typography>
					<Typography css={styles.infoValue}>
						{`${data.information?.roofTerrace ?? "N/A"}`}
					</Typography>
				</li>
				<li css={styles.infoItem}>
					<Typography variant="subtitleS" css={styles.infoLabel}>
						{t("features.propertyDiscovery.additionalRooms")}
					</Typography>
					<Typography css={styles.infoValue}>
						{`${data.information?.additionalRooms ?? "N/A"}`}
					</Typography>
				</li>
				<li css={styles.infoNote}>
					<Typography variant="captionM">{data.information?.note}</Typography>
				</li>
				<li css={styles.infoDownload}>
					<RDSButton
						target="_blank"
						rel="noreferrer"
						href="src/assets/exolore-preview.png" // TODO: Replace with real file url
						leadIcon={<DownloadIcon css={styles.downloadIcon} />}
						size="sm"
						css={{ width: "100%" }}
						text={t("features.propertyDiscovery.downLoadFloorPlans")}
					/>
				</li>
			</ul>
		</div>
	);
}
