import { useAppSelector } from "@/store";
import { useTranslation } from "react-i18next";
import { CashPaymentMilestone } from "@/features/cash-payment-milestone/cash-payment-milestone";
import { css } from "@emotion/react";
import { DetailCardV2 } from "@/components/detail-cardV2/detail-cardV2";
const styles = {
	paymentPlan: css({
		listStyleType: "none",
		padding: 0,
	}),
};
export function CashPaymentPlan() {
	const { t } = useTranslation();
	const paymentPlan = useAppSelector(
		(state) => state.reservation.data.paymentPlan,
	);
	const paymentPlanInfo = useAppSelector(
		(state) => state.reservation.data.paymentPlanInfo,
	);

	return (
		<DetailCardV2
			title={t("features.propertyReservation.paymentPlan.paymentPlan")}
			description={
				t("features.propertyReservation.paymentPlan.paymentPlanDescription") ||
				""
			}
			isCollapsible={false}
		>
			<CashPaymentMilestone
				plan={paymentPlan.plan}
				planFromReservation={paymentPlanInfo}
				showPrice={true}
				css={styles.paymentPlan}
				isFromReservation={true}
			/>
		</DetailCardV2>
	);
}
