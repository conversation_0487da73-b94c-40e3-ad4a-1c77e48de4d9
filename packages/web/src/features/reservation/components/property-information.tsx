import { Typography } from "@/components/typography";
import { AppTheme, tabletMQ } from "@/theme";
import { css } from "@emotion/react";
import { useTranslation } from "react-i18next";
import { Container } from "@/components/container/container";
import { UnitOverviewCard } from "../../unit-overview-card/unit-overview-card";
import { Property, PropertyUnit } from "@/types/property-type";
import { getFacadeThumbnail } from "@/pages/property-group/property-mapper-utils";
import { RDSButton, RDSLink as Link } from "@roshn/ui-kit";

const styles = {
	button: css({
		borderRadius: 0,
		paddingInline: 0,
	}),
	cardWrapper: (theme: AppTheme) =>
		css({
			marginBottom: theme.spaces.xl,
		}),
	infoList: (theme: AppTheme) =>
		css({
			["li"]: {
				alignItems: "center",
				display: "flex",
				justifyContent: "space-between",
				marginBottom: theme.spaces.md,
			},
			listStyle: "none",

			padding: 0,
		}),
	listTitle: css({
		alignItems: "center",
		display: "flex",
		justifyContent: "space-between",
	}),
	title: (theme: AppTheme) =>
		css({
			lineHeight: theme.spaces.xl,
		}),
	wrapper: (theme: AppTheme) =>
		css({
			background: theme.colors.background.secondary,
			paddingTop: theme.spaces.md,

			[tabletMQ(theme)]: {
				borderRadius: theme.borderRadius.sm,
				height: "fit-content",
				padding: theme.spaces.md,
			},
		}),
};

export type PropertyInformationProps = {
	hasOverview?: boolean;
	propertyGroup: Property;
	unit: PropertyUnit;
};

export function PropertyInformation({
	unit,
	propertyGroup,
	hasOverview = true,
}: PropertyInformationProps) {
	const { t } = useTranslation();

	const thumbnail = getFacadeThumbnail(propertyGroup, unit.unitInfo.facade);

	return (
		<Container css={styles.wrapper}>
			{!!hasOverview && (
				<div css={styles.cardWrapper}>
					<UnitOverviewCard
						community={unit.community}
						unitName={unit.unitName}
						bedrooms={unit.unitInfo.bedroom}
						bathrooms={unit.unitInfo.bathroom}
						estDeliveryDate={unit.estDeliveryDate}
						price={unit.unitPrice.price}
						note={unit.unitInfo.note}
						squareSpace={unit.unitInfo.plotArea}
						thumbnail={thumbnail}
						type={unit.unitType}
						typologyGroup={propertyGroup.typologyGroup}
						unitId={unit.unitCode}
					/>
				</div>
			)}
			{propertyGroup?.floorPlanLink && (
				<div css={styles.listTitle}>
					<Typography css={styles.title} variant="subtitleL">
						{t(
							"features.propertyReservation.propertyInformation.propertyInformation",
						)}
					</Typography>
					<Link
						download
						target="_blank"
						css={{
							[`&:hover`]: {
								textDecoration: "none",
							},
						}}
						underline="none"
						href={propertyGroup.floorPlanLink}
					>
						<RDSButton
							variant="tertiary"
							size="sm"
							css={styles.button}
							text={t(
								"features.propertyReservation.propertyInformation.floorPlan",
							)}
						/>
					</Link>
				</div>
			)}
			<ul css={styles.infoList}>
				<li>
					<Typography variant="subtitleS">
						{t("features.propertyDiscovery.plotArea")}
					</Typography>
					<Typography variant="bodyS">
						{`${unit.unitInfo.plotArea ?? "N/A"} ${unit.unitInfo.plotArea ? t("common.sqm") : ""}`.trim()}
					</Typography>
				</li>
				<li>
					<Typography variant="subtitleS">
						{t("features.propertyDiscovery.grossFloorArea")}
					</Typography>
					<Typography variant="bodyS">
						{`${unit.unitInfo.grossFloorArea ?? "N/A"} ${unit.unitInfo.grossFloorArea ? t("common.sqm") : ""}`.trim()}
					</Typography>
				</li>
				{Boolean(unit.unitInfo.bedroom) && (
					<li>
						<Typography variant="subtitleS">
							{t("features.propertyDiscovery.bedroom")}
						</Typography>
						<Typography variant="bodyS">
							{unit.unitInfo.bedroom || "N/A"}
						</Typography>
					</li>
				)}
				{Boolean(unit.unitInfo.bathroom) && (
					<li>
						<Typography variant="subtitleS">
							{t("features.propertyDiscovery.bathroom")}
						</Typography>
						<Typography variant="bodyS">
							{unit.unitInfo.bathroom || "N/A"}
						</Typography>
					</li>
				)}
				{Boolean(unit.unitInfo.roofTerrace) && (
					<li>
						<Typography variant="subtitleS">
							{t("features.propertyDiscovery.roofTerrace")}
						</Typography>
						<Typography variant="bodyS">
							{unit.unitInfo.roofTerrace ? t("common.yes") : t("common.no")}
						</Typography>
					</li>
				)}
				{Boolean(unit.unitInfo.additionalRooms) && (
					<li>
						<Typography variant="subtitleS">
							{t("features.propertyDiscovery.additionalRooms")}
						</Typography>
						<Typography variant="bodyS">
							{unit.unitInfo.additionalRooms || "N/A"}
						</Typography>
					</li>
				)}
				{Boolean(unit.unitInfo.note) && (
					<li>
						<Typography variant="subtitleS">
							{t(unit.unitInfo.note || "")}
						</Typography>
					</li>
				)}
			</ul>
		</Container>
	);
}
