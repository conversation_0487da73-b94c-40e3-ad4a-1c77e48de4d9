import { DividerV2 } from "@/components/dividerV2";
import { Typography } from "@/components/typography";
import { useScreenSize } from "@/hooks/media-query-hooks";
import { styles } from "./property-section.styles";
import { RDSTabs } from "@roshn/ui-kit";

type TabData = {
	asset: boolean;
	assetProps: any;
	isDisabled: boolean;
	isParent: boolean;
	label: string;
	onClick: () => void;
	rdsBadge: boolean;
	size: "md";
	state: "inactive" | "active";
};

type PhasesSectionProps = {
	activePhaseIndex: number;
	hasPhases?: boolean;
	phases: TabData[];
	subHeading?: string;
	title: string;
};
export const PhasesSection = (props: PhasesSectionProps) => {
	const { isTablet } = useScreenSize();
	return (
		<div css={styles.phasesSection}>
			{props.hasPhases ? (
				<>
					<DividerV2 dividerLine={"both"} css={styles.divider}>
						<Typography css={styles.header}>{props.title}</Typography>
					</DividerV2>
					<Typography variant={isTablet ? "subtitleXL2" : "h6"}>
						{props.subHeading}
					</Typography>
					<div css={styles.phasesTab}>
						<div css={styles.tabsInnerContainer}>
							{props.phases.map((phase, index) => (
								<RDSTabs
									key={index}
									{...phase}
									state={
										index === props.activePhaseIndex ? "active" : "default"
									}
									onClick={phase.onClick}
									level="1"
									platform={isTablet ? "desktop" : "mobile"}
									css={styles.tab}
								>
									{phase.assetProps.children}
								</RDSTabs>
							))}
						</div>
					</div>
				</>
			) : (
				<DividerV2
					dividerLine={isTablet ? "after" : "both"}
					css={styles.divider}
				>
					<Typography css={styles.header}>{props.title}</Typography>
				</DividerV2>
			)}
		</div>
	);
};
