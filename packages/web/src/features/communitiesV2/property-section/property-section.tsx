import { useState, useMemo, useEffect, useCallback } from "react";
import { RDSTabs, RDSLink as Link } from "@roshn/ui-kit";
import { styles } from "./property-section.styles";
import { Typography } from "@/components/typography";
import {
	GetCommunityPropertyGroupsResponse,
	ImageComponent,
} from "@roshn/shared";
import { useTranslation } from "react-i18next";
import { mapPropertiesToPropertyListingListProps } from "@/pages/communities/communities.mapper";
import { PhasesSection } from "./phases-section";
import { PropertyCard } from "./property-card";
import Skeleton from "react-loading-skeleton";

import { useTheme } from "@emotion/react";
import { Link as RouterLink } from "@remix-run/react";
import { useAppDispatch } from "@/store";
import { trackClickProperty } from "@/features/communities/redux/actions";
import { AppPaths, useAppPathGenerator } from "@/routes";
import { COMMUNITY_NAMES } from "@/constants";

const DEFAULT_PHASE_NAME = "SEDRA_3";

type PropertySectionProps = {
	communityData: GetCommunityPropertyGroupsResponse | undefined;
	communityName: string;
	hasPhases: boolean;
	isCommunityLoading: boolean;
	phases: ImageComponent[];
	subHeading?: string;
	title: string;
};

export const PropertySection = ({
	communityName,
	communityData,
	isCommunityLoading,
	title,
	subHeading,
	hasPhases,
	phases,
}: PropertySectionProps) => {
	const theme = useTheme();
	const { t } = useTranslation(undefined, { keyPrefix: "pages.communities" });
	const { t: tGlobal } = useTranslation();
	const dispatch = useAppDispatch();
	const appPathGenerator = useAppPathGenerator();

	const [projectName, setProjectName] = useState(DEFAULT_PHASE_NAME);
	const [activePhaseIndex, setActivePhaseIndex] = useState(0);

	const [activeUnitTypeTabIndex, setActiveUnitTypeTabIndex] = useState(
		communityName === COMMUNITY_NAMES.ALDANAH ? 1 : 0,
	);

	const propertyTabs = useMemo(
		() =>
			isCommunityLoading
				? []
				: mapPropertiesToPropertyListingListProps(t, communityData),
		[t, communityData, isCommunityLoading],
	);

	const filteredUnitTypeTabs = useMemo(() => {
		if (!hasPhases) {
			return propertyTabs.filter((tab) => tab.properties.length > 0);
		}

		return propertyTabs
			.map((tab) => ({
				...tab,
				properties: tab.properties.filter((property) => {
					if (property.communityPhases?.data) {
						return property.communityPhases.data.some(
							(phase) => phase.attributes.phaseName === projectName,
						);
					}
				}),
			}))
			.filter((tab) => tab.properties.length > 0);
	}, [propertyTabs, hasPhases, projectName]);

	const selectedTab = filteredUnitTypeTabs[activeUnitTypeTabIndex] || {
		properties: [],
		tabName: "villa",
	};

	const handleTabSelect = useCallback((index: number) => {
		setActiveUnitTypeTabIndex(index);
	}, []);

	const handlePhaseSelect = useCallback(
		(projectName: string, index: number) => {
			setProjectName(projectName || DEFAULT_PHASE_NAME);
			setActivePhaseIndex(index);
		},
		[],
	);

	useEffect(() => {
		const villaIndex = filteredUnitTypeTabs.findIndex(
			(tab) => tab.tabName.toLowerCase() === "villa",
		);
		setActiveUnitTypeTabIndex(villaIndex !== -1 ? villaIndex : 0);
	}, [communityName, projectName, filteredUnitTypeTabs]);

	useEffect(() => {
		if (activeUnitTypeTabIndex >= filteredUnitTypeTabs.length) {
			setActiveUnitTypeTabIndex(0);
		}
	}, [filteredUnitTypeTabs.length, activeUnitTypeTabIndex]);

	//* Phase items Tabs *//
	const phaseItems = useMemo(
		() =>
			phases.map((phase, index) => ({
				asset: Boolean(phase?.src?.data?.attributes?.url),
				assetProps: {
					children: phase.src ? (
						<img src={phase.src.data.attributes.url} alt={phase.title} />
					) : null,
					size: "56px",
					type: "square",
				},
				isDisabled: false,
				isParent: true,
				label: phase.title,
				onClick: () => handlePhaseSelect(phase.caption, index),
				rdsBadge: false,
				size: "md" as const,
				state: "inactive" as const,
			})),
		[phases, handlePhaseSelect],
	);

	return (
		<section css={styles.mainSection(theme, false)}>
			<PhasesSection
				title={title}
				subHeading={subHeading}
				phases={phaseItems}
				hasPhases={hasPhases}
				activePhaseIndex={activePhaseIndex}
			/>
			<div css={styles.propertySection}>
				<div css={styles.container}>
					<div css={styles.content}>
						<Typography css={styles.heading}>
							{tGlobal("pages.propertySection.title", {
								property: selectedTab.tabName?.toLowerCase(),
							})}
						</Typography>
						<Typography css={styles.subtitle}>
							{tGlobal("pages.propertySection.subTitle", {
								communityName: tGlobal(
									`pages.communities.communityName.${communityName}`,
								),
								property: selectedTab.tabName?.toLowerCase(),
							})}
						</Typography>
					</div>
					<div css={styles.tabsGroup}>
						{isCommunityLoading ? (
							<Skeleton height={40} width="100%" />
						) : (
							<div css={styles.tabsScrollable}>
								{filteredUnitTypeTabs.map((propertyTab, index) => (
									<div key={index} data-testid={propertyTab.testId}>
										<RDSTabs
											label={propertyTab.tabName}
											level="2"
											state={
												activeUnitTypeTabIndex === index ? "active" : "default"
											}
											onClick={() => handleTabSelect(index)}
											isDisabled={false}
											rdsBadge={false}
											platform="desktop"
										/>
									</div>
								))}
							</div>
						)}
					</div>
				</div>

				<div css={styles.cardRow}>
					{isCommunityLoading
						? [...Array(3)].map((_, index) => (
								<Skeleton key={index} height={411} width={411} />
							))
						: selectedTab.properties.map((item) => {
								const propertyGroupPath = appPathGenerator(
									AppPaths.propertyGroup,
									{
										communityName: communityName || "",
										groupId: item.typology || "",
									},
								);
								return (
									<Link
										onClick={() => {
											dispatch(trackClickProperty());
										}}
										underline="none"
										css={styles.link}
										key={item.id}
										to={propertyGroupPath}
										component={RouterLink}
									>
										{isCommunityLoading ? (
											<Skeleton height={411} width={411} />
										) : (
											<PropertyCard {...item} />
										)}
									</Link>
								);
							})}
				</div>
			</div>
		</section>
	);
};
