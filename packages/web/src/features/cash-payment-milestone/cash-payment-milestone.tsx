import { Typography } from "@/components/typography";
import { getNumberWithOrdinal } from "@/utils/intl";
import { css, useTheme } from "@emotion/react";
import { useTranslation } from "react-i18next";
import { AppTheme } from "@/theme";
import { useMoneyFormatter } from "@/hooks/use-money-formatter";
import { PaymentPlanInfo } from "../reservation/types";
import { InstallmentPlan } from "@/types/property-type";

const styles = {
	root: (theme: AppTheme) =>
		css({
			[`& li`]: {
				alignItems: "center",
				display: "flex",
				justifyContent: "space-between",
				marginBottom: theme.spaces.sm,
			},

			background: theme.colors.error.text,
		}),
	textStyle: (theme: AppTheme) =>
		css({
			color: theme.colors.text.secondary,
		}),
};

type CashPaymentMilestone = {
	className?: string;
	plan: InstallmentPlan["plan"];
	planFromReservation: PaymentPlanInfo | undefined;
	isFromReservation: boolean;
	showPrice: boolean;
};

export const CashPaymentMilestone = ({
	plan,
	planFromReservation,
	showPrice = false,
	className,
	isFromReservation,
}: CashPaymentMilestone) => {
	const theme = useTheme();
	const { t } = useTranslation();
	const formatMoney = useMoneyFormatter();

	const hasReservationFeeInstallment =
		planFromReservation &&
		isFromReservation &&
		planFromReservation?.planDetailsCollection?.some(
			(item) => item.installmentNo === 0,
		);

	return isFromReservation ? (
		<ul css={styles.root} className={className}>
			{planFromReservation?.planDetailsCollection?.map((item, index) => (
				<li key={`payment-line-${item.installmentNo}`}>
					<div>
						<Typography
							variant="bodyS"
							css={{
								fontWeight: theme.typography.fontWeightMedium,
							}}
						>
							{showPrice && formatMoney(item?.installmentTotalAmount)}
						</Typography>
						<Typography variant="bodyS" css={styles.textStyle}>
							{index === 0 && hasReservationFeeInstallment
								? t(
										"features.propertyReservation.paymentPlan.dueDuringReservation",
									)
								: item?.completionProgress == 0
									? t("features.propertyReservation.paymentPlan.downPayment")
									: t("features.propertyReservation.paymentPlan.dueAt", {
											percent: item.completionProgress * 100,
										})}
						</Typography>
					</div>
					<div
						css={{
							textAlign: "end",
						}}
					>
						<Typography variant="bodyS" css={styles.textStyle}>
							{index === 0 && hasReservationFeeInstallment
								? t("features.propertyReservation.paymentPlan.reservationFee")
								: item.completionProgress == 1
									? t("features.propertyReservation.paymentPlan.onHanover")
									: `${t(
											`features.propertyReservation.paymentPlan.${getNumberWithOrdinal(
												item.installmentNo,
											)}`,
										)}`}
						</Typography>
						{!(index === 0 && hasReservationFeeInstallment) && (
							<Typography variant="bodyS" css={styles.textStyle}>
								{t("features.propertyReservation.paymentPlan.fromTotal", {
									percent: item.installment * 100,
								})}
							</Typography>
						)}
					</div>
				</li>
			))}
		</ul>
	) : (
		<ul css={styles.root} className={className}>
			{plan?.map((item) => (
				<li key={`payment-line-${item.mileStone}`}>
					<div>
						<Typography
							variant="bodyS"
							css={{
								fontWeight: theme.typography.fontWeightMedium,
							}}
						>
							{showPrice && formatMoney(item.amount)}
						</Typography>
						<Typography variant="bodyS" css={styles.textStyle}>
							{item?.constructionProgress == 0
								? t("features.propertyReservation.paymentPlan.downPayment")
								: t("features.propertyReservation.paymentPlan.dueAt", {
										percent: item.constructionProgress * 100,
									})}
						</Typography>
					</div>
					<div
						css={{
							textAlign: "end",
						}}
					>
						<Typography variant="bodyS" css={styles.textStyle}>
							{item.constructionProgress == 1
								? t("features.propertyReservation.paymentPlan.onHanover")
								: `${t(
										`features.propertyReservation.paymentPlan.${getNumberWithOrdinal(
											item.mileStone,
										)}`,
									)}`}
						</Typography>
						<Typography variant="bodyS" css={styles.textStyle}>
							{t("features.propertyReservation.paymentPlan.fromTotal", {
								percent: item.percentage * 100,
							})}
						</Typography>
					</div>
				</li>
			))}
		</ul>
	);
};
