import * as React from "react";
import { AppTheme, tabletMQ } from "@/theme";
import { css } from "@emotion/react";
import { useTranslation } from "react-i18next";
import { useFormik } from "formik";
import * as Yup from "yup";
import { useDispatch } from "react-redux";
import {
	RDSSideDrawer,
	RDSTabGroup,
	RDSButton,
	RDSSelect,
	RDSTypography,
	RDSEmptyState,
} from "@roshn/ui-kit";
import { Typography } from "@/components/typography";
import { Option, SelectInput } from "@/components/select";
import { useFreshCallback } from "rooks";
import { i18n } from "@/services/locale-impl";
import { useInjection } from "@roshn/shared";
import {
	TimeSlot,
	VirtualAppointmentService,
} from "@/services/virtual-appointment";
import { useNavigate, generatePath } from "@remix-run/react";
import { AppPaths, generateAppPath } from "@/routes";
import * as luxon from "luxon";
import { appointmentTypeProps } from "./constant";
import { ScheduleError } from "../bookings/components/schedule-error";
import { useEffect } from "react";
import { RoshnLoadingModal } from "../loading";
import * as Toast from "@/components/toast";
import { WarningCircleIcon } from "@/components/icons";
import { createPortal } from "react-dom";
import { TimeSlotSkeleton } from "../bookings/components/schedule-dialog-skeleton";
import { AppointmentEDMService } from "@/services/appointment-edm";
import { ActionMeta } from "react-select";
import { SelectStartTimePanelV2 } from "../bookings/components/schedule-select-slot-v2";
import { COMMUNITY_NAMES } from "@/constants";
import { FeedbackMessage } from "@/components/feedback-message";

// Extended Option type with additional properties needed for the component
interface ExtendedOption extends Option {
	CommunityName?: string;
	value: string;
	salesCenters?: Array<{
		Community: string;
		DefaultLocation: string;
		CenterNameEn: string;
		CenterNameAr: string;
		LocationId: string;
	}>;
}

// Type definition for the API response
interface GetAppointmentsResponseData {
	id: string;
	[key: string]: any;
}

interface Schedule {
	[key: string]: any;
}

interface CommunityResponseData {
	CommunityEN: string;
	CommunityAR: string;
	CommunityName: string;
	CommunityId: string;
	salesCenters?: Array<{
		Community: string;
		DefaultLocation: string;
		CenterNameEn: string;
		CenterNameAr: string;
		LocationId: string;
	}>;
}

interface CommunityResponseResponse {
	data: CommunityResponseData[];
}

// Custom tab content type that includes all the properties used
interface TabContentItem {
	label: string;
	isDisabled: boolean;
	state: "default" | "active";
	align?: string;
	onClick: () => void;
	assetProps?: any;
	rdsBadge?: boolean;
	css?: any;
}

const styles = {
	root: (theme: AppTheme) =>
		css({
			height: "100vh",
			pointerEvents: "auto",
			[tabletMQ(theme)]: {
				boxShadow: theme.shadows.lg,
				width: "30%",
			},
			position: "fixed",
			width: "100%",
			background: theme.colors.background.default,
		}),
	tabLabel: css({
		whiteSpace: "nowrap",
		overflow: "hidden",
		textOverflow: "clip",
		textAlign: "center",
		width: "100%",
	}),

	tabContainer: css({
		flex: 1,
		minWidth: 0,
		textAlign: "center",
		display: "flex",
		alignItems: "center",
		justifyContent: "center",
		padding: "12px 16px",
		minHeight: "48px",
		fontSize: "14px",
		fontWeight: 500,
		lineHeight: "20px",
		letterSpacing: "0.1px",
		whiteSpace: "nowrap",

		// Ensure equal width distribution for both tabs
		width: "50%",
		maxWidth: "none",

		// Responsive sizing adjustments
		"@media (max-width: 768px)": {
			padding: "10px 12px",
			minHeight: "44px",
			fontSize: "13px",
			lineHeight: "18px",
		},

		"@media (min-width: 769px)": {
			padding: "14px 20px",
			minHeight: "52px",
			fontSize: "15px",
			lineHeight: "22px",
		},
	}),
};

// Update validation schema with conditional requirement for salesCenter
const Schema = Yup.object().shape({
	community: Yup.object().nullable().required("Community is required"),
	salesCenter: Yup.object().nullable().required("Sales Center is required"),
	selectedDate: Yup.date().nullable().required("Date is required"),
	selectedStartTime: Yup.date().nullable().required("Start time is required"),
	appointmentType: Yup.string().required("Appointment type is required"),
});

// Define types
export type SalesCenterOption = ExtendedOption & {
	community?: string;
	isDefault?: boolean;
};

export type VisitSaleCenterFormValue = {
	community: ExtendedOption | null;
	salesCenter: SalesCenterOption | null;
	selectedDate: Date | null;
	selectedPartOfDay: string;
	selectedStartTime: Date | null;
	appointmentType: string;
};

interface VirtualAppointmentProps {
	open: boolean;
	onClose: () => void;
	onSubmit?: (values: any) => void;
	clickType?: string;
	appointmentIdProps?: string;
	onDone?: () => void;
	community?: string;
	salesCenter?: string;
	isEdm?: boolean;
	uuid?: string;
	disableSalesCenterSelection?: boolean;
}

export const VirtualAppointment: React.FC<VirtualAppointmentProps> = ({
	open,
	onClose,
	onSubmit,
	clickType,
	appointmentIdProps,
	community,
	salesCenter,
	onDone,
	isEdm = false,
	uuid,
	disableSalesCenterSelection = false,
}) => {
	const virtualAppointmentService = useInjection<VirtualAppointmentService>(
		VirtualAppointmentService,
	);
	const appointmentEDMService = useInjection<AppointmentEDMService>(
		AppointmentEDMService,
	);
	const { t } = useTranslation();
	const lang = i18n.language;
	const [loading, setLoading] = React.useState(false);
	const [successMessage, setSuccessMessage] = React.useState<string | null>(
		null,
	);
	const [isError, setIsError] = React.useState(false);

	// Component state
	const [listAllCommunities, setListAllCommunities] = React.useState<
		ExtendedOption[]
	>([]);
	const [selectedCommunity, setSelectedCommunity] =
		React.useState<ExtendedOption | null>(null);
	const [currentTab, setCurrentTab] = React.useState<number | undefined>(
		undefined,
	); // No default tab selection
	const [slots, setSlots] = React.useState<TimeSlot[]>([]);
	const [appointmentType, setAppointmentType] = React.useState<string>(""); // No default appointment type
	const [availableDates, setAvailableDates] = React.useState<Date[]>([]);
	const [fetchingCommunities, setFetchingCommunities] = React.useState(false);
	const navigate = useNavigate();
	const homePath = generateAppPath(AppPaths.myAppointments);
	const [appointmentExists, setAppointmentExists] = React.useState(false);
	const [appointmentId, setAppointmentId] = React.useState<string>("");
	const [isRescheduling, setIsRescheduling] = React.useState(false);
	const [noSlotAvailable, setNoSlotAvailable] = React.useState(false);
	const [fetching, setFetching] = React.useState(false);
	const [isTabChanging, setIsTabChanging] = React.useState(false);
	// Formik setup with validateOnMount enabled
	const formikState = useFormik<VisitSaleCenterFormValue>({
		initialValues: {
			community: null,
			salesCenter: null,
			selectedDate: null,
			selectedPartOfDay: "",
			selectedStartTime: null,
			appointmentType: "", // No default appointment type
		},
		validateOnMount: true,
		validationSchema: Schema,
		onSubmit: async (values) => {
			if (!formikState?.isValid) return;
			try {
				const communityOfInterestName = selectedCommunity?.CommunityName || "";
				const locationId = values?.salesCenter?.value || "";
				if (!values?.selectedStartTime) return;
				const startTimeDate = new Date(values?.selectedStartTime);
				const startTime = startTimeDate?.toISOString();
				const endTime = new Date(
					startTimeDate?.getTime() + 30 * 60000,
				).toISOString();

				const payload = {
					communityOfInterestName,
					endTime,
					lang: i18n.language.toUpperCase(),
					locationId,
					salesCenterCommunityName: "",
					startTime,
					appointmentType: values?.appointmentType,
				};

				let response;

				if (isRescheduling) {
					if (!isEdm) {
						response = await virtualAppointmentService?.rescheduleAppointment({
							appointmentId: appointmentId,
							...payload,
						});
						formikState?.resetForm();
						setSuccessMessage("Appointment rescheduled successfully!");
					} else {
						response =
							await appointmentEDMService?.rescheduleAppointmentWithEDM({
								appointmentId: appointmentId,
								uuid: uuid || "",
								...payload,
							});
						formikState.resetForm();
						setSuccessMessage("Appointment rescheduled successfully!");
					}
				} else {
					response = await virtualAppointmentService?.bookAppointment(payload);
					setSuccessMessage("Appointment booked successfully!");
					formikState.resetForm();
				}

				if (response) {
					// Reset all state variables
					setSelectedCommunity(null);
					setCurrentTab(undefined); // Reset to no selection
					setSlots([]);
					setAppointmentType(""); // Reset to no appointment type
					setAvailableDates([]);
					setIsRescheduling(false);
					setAppointmentId("");
					formikState.resetForm();
					onDone?.();
				}
				await onSubmit?.(payload);
			} catch (error) {

					console.error("Error booking appointment:", error);
	setIsError(true);
				Toast.showToast({
					message: t(
						"features.bookings.virtualAppointment.toastMessage.slotNotAvailable",
					),
					icon: <WarningCircleIcon />,
				});
			}
		},
	});

	React.useEffect(() => {
		if (open) {
			checkExistingAppointment();
		}
	}, [open]);

	const checkExistingAppointment = async () => {
		try {
			if (!appointmentIdProps) {
				setFetching(true);
				const existingAppointments =
					await virtualAppointmentService?.getAppointments({
						locationId: "",
						appointmentType: "",
					});
				if (
					existingAppointments?.data &&
					Array.isArray(existingAppointments?.data) &&
					existingAppointments?.data?.length > 0
				) {
					navigate(generatePath(AppPaths.myAppointments, { locale: lang }));
					setAppointmentExists(true);
					setAppointmentId(existingAppointments?.data?.[0]?.id);
					setFetching(false);
				} else {
					fetchCommunity();
					setFetching(false);
				}
			} else {
				setAppointmentExists(false);
				setIsRescheduling(true);
				setAppointmentId(appointmentIdProps);
				fetchCommunity();
				setFetching(false);
			}
		} catch (error) {
			setFetching(false);
			Toast.showToast({
				message: t(
					"features.bookings.virtualAppointment.toastMessage.slotNotAvailable",
				),
				icon: <WarningCircleIcon />,
			});
		}
	};

	const fetchCommunity = useFreshCallback(async () => {
		try {
			setFetchingCommunities(true);
			let output: ExtendedOption[] = [];
			if (!isEdm) {
				const saleCenterCommunitiesQuery =
					await virtualAppointmentService?.getAllCommunities();
				if (
					saleCenterCommunitiesQuery?.data &&
					Array.isArray(saleCenterCommunitiesQuery?.data)
				) {
					output = createOptions(saleCenterCommunitiesQuery?.data);
				}
				setListAllCommunities(output);
				setFetchingCommunities(false);
			} else {
				const saleCenterCommunitiesQueryEDM =
					await appointmentEDMService?.getAllEDMCommunities();
				if (
					saleCenterCommunitiesQueryEDM?.data &&
					Array.isArray(saleCenterCommunitiesQueryEDM?.data)
				) {
					output = createOptions(saleCenterCommunitiesQueryEDM?.data);
				}
				setListAllCommunities(output);
				setFetchingCommunities(false);
			}
			if (community) {
				const selectedCommunity = output.find(
					(data) => data.CommunityName === community,
				);
				if (selectedCommunity) {
					setSelectedCommunity(selectedCommunity);
					formikState?.setFieldValue("community", selectedCommunity);
					formikState?.setFieldValue("salesCenter", null);
				}
			} else {
				setSelectedCommunity(null);
				formikState?.setFieldValue("community", null);
				formikState?.setFieldValue("salesCenter", null);
			}
		} catch (error) {
			console.error("Error fetching communities:", error);
			setFetchingCommunities(false);
			Toast.showToast({
				message: t(
					"features.bookings.virtualAppointment.toastMessage.slotNotAvailable",
				),
				icon: <WarningCircleIcon />,
				onClose: () => {},
				duration: 3000,
			});
		}
	});
	// Create options for dropdowns
	const createOptions = (data: CommunityResponseData[]): ExtendedOption[] => {
		return data.map((item) => ({
			label: lang === "en" ? item.CommunityEN : item.CommunityAR,
			CommunityName: item.CommunityName,
			value: item.CommunityId,
			salesCenters: item.salesCenters ?? [],
		}));
	};

	// Handler for "Visit sales center" tab click (set appointmentType to PHYSICAL)
	const handleClickVisitSalesCenter = useFreshCallback(async () => {
		try {
			// Prevent double-click by checking if already processing
			if (isTabChanging || (currentTab === 0 && appointmentType === appointmentTypeProps.PHYSICAL)) {
				return;
			}
			
			setIsTabChanging(true);
			setCurrentTab(0);
			setAppointmentType(appointmentTypeProps.PHYSICAL);
			formikState?.setFieldValue("appointmentType", appointmentTypeProps.PHYSICAL);
			formikState?.setFieldValue("salesCenter", null);
			
			// Reset the tab changing state after a short delay
			setTimeout(() => setIsTabChanging(false), 300);
		} catch (error) {
			console.error("Error fetching sales center communities:", error);
			setIsTabChanging(false);
		}
	});

	// Handler for "Video call" tab click
	const handleClickVirtualAppointment = useFreshCallback(async () => {
		try {
			// Prevent double-click by checking if already processing
			if (isTabChanging || (currentTab === 1 && appointmentType === appointmentTypeProps.VIRTUAL)) {
				return;
			}
			
			setIsTabChanging(true);
			setCurrentTab(1);
			setAppointmentType(appointmentTypeProps.VIRTUAL);
			formikState?.setFieldValue("appointmentType", appointmentTypeProps.VIRTUAL);
			formikState?.setFieldValue("salesCenter", null);
			
			// Reset the tab changing state after a short delay
			setTimeout(() => setIsTabChanging(false), 300);
		} catch (error) {
			console.error("Error fetching virtual appointment communities:", error);
			setIsTabChanging(false);
		}
	});

	// Sales Center Dropdown Options
	const salesCentersOptions = React.useMemo(() => {
		if (!selectedCommunity) return [];
		const selectedComm = listAllCommunities.find(
			(c) => c.value === selectedCommunity.value,
		);
		const salesCenterOption: SalesCenterOption[] =
			selectedComm?.salesCenters?.map((saleCenter) => ({
				community: saleCenter.Community,
				isDefault: saleCenter.DefaultLocation === "Y",
				label:
					lang === "en" ? saleCenter.CenterNameEn : saleCenter.CenterNameAr,
				value: saleCenter.LocationId,
			})) ?? [];

		// If there's only one option or it's a virtual appointment, auto select it
		if (salesCenterOption?.length === 1 || appointmentType === appointmentTypeProps.VIRTUAL) {
			formikState?.setFieldValue("salesCenter", salesCenterOption?.[0]);
			return salesCenterOption;
		} else if (salesCenterOption?.length > 1) {
			// If salesCenter prop exists, set it as default value
			if (salesCenter && salesCenterOption?.length > 0) {
				const defaultSalesCenter = salesCenterOption?.find(
					(option) => option?.value === salesCenter,
				);
				if (defaultSalesCenter) {
					formikState?.setFieldValue("salesCenter", defaultSalesCenter);
				}
			} else {
				// Auto-select the default sales center when multiple options are available
				const defaultSalesCenter = salesCenterOption?.find(
					(option) => option?.isDefault,
				);
				if (defaultSalesCenter) {
					formikState?.setFieldValue("salesCenter", defaultSalesCenter);
				}
			}
			return salesCenterOption;
		} else {
			return salesCenterOption;
		}
	}, [
		listAllCommunities,
		selectedCommunity,
		lang,
		appointmentType,
		salesCenter,
	]);

	const fetchSlots = useFreshCallback(async () => {
		if (!formikState?.values?.salesCenter) return;
		setLoading(true);
		const locationId = formikState?.values?.salesCenter?.value;
		// Using appointmentType from formik if available
		const appointmentTypeValue =
			formikState?.values?.appointmentType || appointmentTypeProps.VIRTUAL;
		let resp;
		if (!isEdm) {
			resp = await virtualAppointmentService?.getBookingCapacity({
				locationId,
				appointmentType: appointmentTypeValue,
			});
		} else {
			resp = await appointmentEDMService?.getEDMBookingCapacity({
				locationId,
				uuid: uuid || "",
				appointmentType: appointmentTypeValue,
			});
		}
		if (resp?.data) {
			setSlots(resp?.data as unknown as TimeSlot[]);
			const includeDates = (resp?.data as unknown as TimeSlot[])?.map(
				(slot) => {
					// to first tick of local date
					return luxon.DateTime.fromISO(slot?.startTime)
						.startOf("day")
						.toJSDate();
				},
			);

			const text = (resp?.data as unknown as TimeSlot[])?.map((slot) => {
				return luxon.DateTime.fromISO(slot?.startTime).toFormat("hh:mm a"); // 12-hour format with AM/PM
			});

			setAvailableDates(includeDates);
		} else {
			setNoSlotAvailable(true);
			Toast.showToast({
				message: t(
					"features.bookings.virtualAppointment.toastMessage.slotNotAvailable",
				),
				icon: <WarningCircleIcon />,
				onClose: () => {},
				duration: 3000,
			});
		}

		setLoading(false);
	});

	React.useEffect(() => {
		if (formikState?.values?.salesCenter?.value) {
			fetchSlots();
		}
	}, [formikState?.values?.salesCenter]);

	React.useEffect(() => {
		if (clickType === appointmentTypeProps.VIRTUAL) {
			handleClickVirtualAppointment();
		}
		if (clickType === appointmentTypeProps.PHYSICAL) {
			handleClickVisitSalesCenter();
		}
	}, [clickType, open]);
	return createPortal(
		<form onSubmit={formikState?.handleSubmit}>
			<RDSSideDrawer
				content={
					fetching ? (
						<div css={{ minWidth: "25rem", maxWidth: "500rem" }}>
							<TimeSlotSkeleton />
						</div>
					) : (
						<>
							{noSlotAvailable ? (
								<div>
									<RDSEmptyState
										heading={t(
											"features.bookings.virtualAppointment.form.noAppointment.title",
										)}
										body={t(
											"features.bookings.virtualAppointment.form.noAppointment.desc",
										)}
										size="sm"
										buttons={[
											{
												text: t("pages.error.contactUs"),
												onClick: () => {
													onClose();
													navigate(generateAppPath(AppPaths.contactUs));
												},
											},
										]}
									/>
								</div>
							) : isError ? (
								<div>
									<RDSEmptyState
										heading={t("features.somethingWentWrong.title")}
										body={t("features.somethingWentWrong.description")}
										size="sm"
										buttons={[
											{
												text: t("pages.error.contactUs"),
												onClick: () => {
													onClose();
													navigate(generateAppPath(AppPaths.contactUs));
												},
											},
										]}
									/>
								</div>
							) : (
								<>
									{formikState?.values?.community?.CommunityName?.toLowerCase() ===
										COMMUNITY_NAMES.ALMANAR && (
										<FeedbackMessage
											message={t(
												"features.bookings.scheduleSelectSlot.disclaimer",
											)}
											variant="none"
										/>
									)}
									<Typography css={{ marginBottom: "-20px" }}>
										{t(
											"features.bookings.virtualAppointment.form.appointmentType.label",
										)}
									</Typography>
									<RDSTabGroup
										size={"lg"}
										tabContent={[
											{
												label: t(
													"features.bookings.virtualAppointment.form.options.physical",
												),
												isDisabled: !!(
													community &&
													appointmentType !== appointmentTypeProps.PHYSICAL &&
													!!isRescheduling
												),
												state: currentTab === 0 ? "active" : "default",
												align: "left",
												onClick: () => {
													!isRescheduling
														? handleClickVisitSalesCenter()
														: null;
												},
												assetProps: { heading: "something" },
												rdsBadge: false,
												css: styles.tabContainer,
											} as TabContentItem,
											{
												label: t(
													"features.bookings.virtualAppointment.form.options.virtual",
												),
												isDisabled: !!(
													community &&
													appointmentType !== appointmentTypeProps.VIRTUAL &&
													!!isRescheduling
												),
												state: currentTab === 1 ? "active" : "default",
												onClick: () => {
													!isRescheduling
														? handleClickVirtualAppointment()
														: null;
												},
												assetProps: { heading: "something" },
												rdsBadge: false,
												css: styles.tabContainer,
											} as TabContentItem,
										]}
										activeTabIndex={currentTab ?? -1} // -1 means no tab selected
									/>
									{appointmentType !== "" && listAllCommunities.length > 0 ? (
										<>
											{fetchingCommunities ? (
												<div css={{ padding: "1rem 0" }}>
													<TimeSlotSkeleton />
												</div>
											) : (
												<RDSSelect
													css={{
														maxWidth: "100%",
													}}
													isDisabled={!!community}
													placeholder={t(
														"features.bookings.virtualAppointment.form.community.placeHolder",
													)}
													label={t(
														"features.bookings.virtualAppointment.form.community.label",
													)}
													value={selectedCommunity}
													onChange={(option) => {
														setSelectedCommunity(option as ExtendedOption);
														formikState?.setFieldValue("community", option);
														formikState?.setFieldValue("salesCenter", null);
													}}
													options={listAllCommunities}
													helperText=""
												/>
											)}

											{appointmentType === appointmentTypeProps.PHYSICAL &&
												salesCentersOptions.length > 1 && (
													<>
														<RDSSelect
															isDisabled={
																disableSalesCenterSelection ||
																(!!community &&
																	appointmentType === "VIRTUAL" &&
																	!isRescheduling) ||
																!!isRescheduling
															}
															css={{
																maxWidth: "100%",
															}}
															label={
																appointmentType === appointmentTypeProps.VIRTUAL
																	? t(
																			"features.bookings.virtualAppointment.form.salesCenter.label",
																		)
																	: t(
																			"features.bookings.visitSaleCenter.whichSaleCenter",
																		)
															}
															placeholder={t(
																"features.bookings.virtualAppointment.form.community.placeHolder",
															)}
															value={formikState?.values?.salesCenter || null}
															onChange={(option) =>
																formikState?.setFieldValue(
																	"salesCenter",
																	option,
																)
															}
															options={salesCentersOptions}
															helperText=""
														/>
													</>
												)}
										</>
									) : fetchingCommunities ? (
										<div css={{ padding: "1rem 0" }}>
											<TimeSlotSkeleton />
										</div>
									) : (
										fetching && <TimeSlotSkeleton />
									)}

									{formikState?.values?.salesCenter && (
										<SelectStartTimePanelV2
											appointmentType={appointmentType}
											loading={loading}
											slots={slots}
											selectedDate={
												formikState?.values?.selectedDate || undefined
											}
											onDateChange={(date) =>
												formikState?.setFieldValue("selectedDate", date)
											}
											selectedStartTime={
												formikState?.values?.selectedStartTime || undefined
											}
											onStartTimeChange={(startTime) =>
												formikState?.setFieldValue(
													"selectedStartTime",
													startTime,
												)
											}
										/>
									)}
								</>
							)}
						</>
					)
				}
				header={true}
				footer={true}
				isOpen={open}
				rdsHeader={{
					leadIcon: false,
					trailIcon: true,
					leadIconProps: undefined,
					label: t(
						`features.bookings.virtualAppointment.form.${isRescheduling ? "rescheduleTitle" : "title"}`,
					),
					type: "left",
					hasAsset: false,
					assetProps: undefined,
					trailIconProps: {
						onClick: () => {
							onClose();
						},
					},
				}}
				buttonsGroup={{
					buttons: [
						isError ? (
							<></>
						) : (
							<RDSButton
								variant="primary"
								onClick={formikState?.submitForm}
								disabled={!formikState?.isValid}
								loading={formikState?.isSubmitting}
								text={t("features.bookings.virtualAppointment.form.button")}
							/>
						),
					],
					direction: "vertical",
				}}
				showDescription={false}
			/>
		</form>,
		document.body,
	);
};
