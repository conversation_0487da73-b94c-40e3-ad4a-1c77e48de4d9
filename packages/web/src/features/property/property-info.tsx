import * as React from "react";
import { Typography } from "@/components/typography";
import { Property } from "@/types/property-type";
import { useTranslation } from "react-i18next";
import { PropertyAttribute } from "./property-attributes";
import { PropertyStatusCard } from "./property-status-tag";
import { usePropertyDetail } from "@roshn/shared";
import { showPrice } from "@/pages/property-group/property-mapper-utils";
import Skeleton from "react-loading-skeleton";
import { AppTheme } from "@/theme";
import { css, Theme, useTheme } from "@emotion/react";
import { PropertyPrice } from "./property-price";
import { useParams } from "@remix-run/react";
import { propertyTypeKeyMap } from "@/utils/property-type-utils";

const styles = {
	detailWrapper: (theme: AppTheme, locale: string | undefined) =>
		css({
			color: theme.colors.text.primary,
			marginTop: theme.spaces.md,
			display: "flex",
			flexDirection: "column",
			alignItems: locale === "en" ? "flex-start" : "flex-end",
		}),
	itemPropertyWrap: (theme: AppTheme) =>
		css({
			display: "flex",
			margin: `${theme.spaces.md} 0`,
		}),
	title: (theme: AppTheme) =>
		css({
			letterSpacing: theme.letterSpacings.xxs,
			paddingBottom: theme.spaces.xs3,
		}),
	titleSection: (theme: Theme, locale: string | undefined) => ({
		alignItems: "center",
		display: "flex",
		justifyContent: "space-between",
		flexDirection: (locale === "en" ? "row" : "row-reverse") as
			| "row"
			| "row-reverse",
		marginBottom: theme.spaces.xs,
		gap: theme.spaces.md,
	}),
	cardContainer: (theme: AppTheme) => ({
		paddingTop: theme.spaces.md,
	}),
};

export const PropertyInfo = ({
	data,
	unit,
	className,
	latestHandoverDateFlag,
}: {
	className?: string;
	data: Property;
	unit?: ReturnType<typeof usePropertyDetail>["data"];
	latestHandoverDateFlag?: boolean;
}) => {
	const { t } = useTranslation();
	const { locale } = useParams();
	const theme = useTheme();
	const typeKey = propertyTypeKeyMap[data.type.toLowerCase()] || "villa";
	const propertyTypeKey = `features.masterPlan.name.propertyType.${typeKey}`;
	return (
		<div css={styles.detailWrapper(theme, locale)} className={className}>
			<div css={styles.titleSection(theme, locale)}>
				<div css={{ display: "inline-flex" }}>
					<Typography
						css={styles.title}
						itemProp="name"
						variant="subtitleS"
						isBold
					>
						{t(propertyTypeKey)} {data.typologyGroup}
					</Typography>
					{unit && (
						<Typography
							css={styles.title}
							variant="subtitleS"
							itemProp="unitNumber"
						>
							&nbsp; | &nbsp;{t("features.propertyDiscovery.unit")}&nbsp;
							{unit.unitNumber}
						</Typography>
					)}
				</div>

				<PropertyStatusCard status={unit ? unit.unitStatus : data.status} />
			</div>
			{showPrice(data, unit) && data?.price && (
				<PropertyPrice price={unit?.price ?? data.price} exactPrice={!!unit} />
			)}
			<div css={styles.cardContainer}>
				<PropertyAttribute
					attributes={{
						bathrooms: data.information.bathroom ?? 0,
						bedrooms: data.information.bedroom ?? 0,
						note: data.information.note ?? "",
						plotArea: data.information.plotArea ?? "0",
					}}
					latestHandoverDateFlag={latestHandoverDateFlag}
					unit={unit}
				/>
			</div>
		</div>
	);
};

export const PropertyInfoSkeleton = () => {
	const theme = useTheme();
	const { locale } = useParams();
	return (
		<div css={styles.detailWrapper(theme, locale)}>
			<div css={styles.titleSection(theme, locale)}>
				<Skeleton css={styles.title(theme)} />
			</div>
			<Skeleton css={styles.itemPropertyWrap(theme)} />
			<Skeleton css={styles.itemPropertyWrap(theme)} />
			<Skeleton css={styles.itemPropertyWrap(theme)} />
		</div>
	);
};
