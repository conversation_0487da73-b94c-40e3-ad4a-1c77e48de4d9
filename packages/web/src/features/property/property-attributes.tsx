import * as React from "react";
import {
	BathThubIconRB,
	DoubleBedIconRB,
	FullScreenIcon,
	LocationIcon,
} from "@/components/icons";
import { Typography } from "@/components/typography";
import { AppTheme } from "@/theme";
import { css } from "@emotion/react";
import { useTranslation } from "react-i18next";
import { Link as RouterLink, useParams } from "@remix-run/react";
import { DateTime } from "luxon";
import { AppPaths, useAppPath } from "@/routes";
import { usePropertyDetail } from "@roshn/shared";
import { RDSButton } from "@roshn/ui-kit";

const styles = {
	container: (locale: string | undefined) => (theme: AppTheme) =>
		css({
			display: "flex",
			flexDirection: "column",
			alignItems: locale === "en" ? "flex-start" : "flex-end",
			gap: theme.spaces.md,
		}),
	description: (theme: AppTheme) =>
		css({
			...theme.typography.subtitleS,
			alignItems: "center",
			display: "flex",
			fontWeight: theme.typography.fontWeightRegular,
			letterSpacing: theme.letterSpacings.sm,
			margin: 0,
		}),
	itemProp: (theme: AppTheme) =>
		css({
			lineHeight: theme.spaces.lg,
		}),
	itemProperty: (theme: AppTheme) =>
		css({
			alignItems: "center",
			display: "flex",
			gap: theme.spaces.xs2,
			lineHeight: theme.spaces.lg,
			marginInlineEnd: theme.spaces.lg,
		}),
	itemPropertyIcon: (theme: AppTheme) =>
		css({
			marginBottom: theme.spaces.xs2,
		}),
	itemPropertyWrap: (locale: string | undefined) =>
		css({
			display: "flex",
			flexDirection: locale === "en" ? "row" : "row-reverse",
		}),
	link: css({
		textDecoration: "none",
	}),
	locationButton: (theme: AppTheme) =>
		css({
			[`& span`]: {
				color: theme.colors.common.black,
			},
			letterSpacing: "inherit",
			lineHeight: theme.spaces.lg,
			textDecorationColor: theme.colors.common.black,
			textTransform: "capitalize",
			textUnderlineOffset: theme.spaces.xs3,
		}),
};

export type PropertyAttributeProps = React.ComponentProps<"div"> & {
	attributes: {
		bathrooms: number;
		bedrooms: number;
		note: string;
		plotArea: string;
	};
	latestHandoverDateFlag?: boolean;
	unit?: ReturnType<typeof usePropertyDetail>["data"];
};

export function PropertyAttribute({
	attributes,
	unit,
	latestHandoverDateFlag = false,
	...props
}: PropertyAttributeProps) {
	const { t } = useTranslation();
	const { communityName = "", locale } = useParams();
	const location = [
		t(`features.masterPlan.breadcrumb.city.${communityName}`),
		"-",
		t(`features.masterPlan.breadcrumb.community.${communityName}`),
	].join(" ");

	const mapNavigation = useAppPath(AppPaths.masterPlanCity, {
		communityName,
	});

	return (
		<div {...props} css={styles.container(locale)}>
			{/* For property specific view page */}
			{unit && (
				<>
					{unit.estDeliveryDate && latestHandoverDateFlag && (
						<div css={styles.itemPropertyWrap(locale)}>
							<Typography css={styles.itemProp}>{`${t(
								"features.propertyDiscovery.estDelivery",
							)} ${DateTime.fromJSDate(new Date(unit.estDeliveryDate)).toFormat(
								"dd/MM/yyyy",
							)}`}</Typography>
						</div>
					)}
					<div css={styles.itemPropertyWrap(locale)}>
						<RouterLink to={mapNavigation} css={styles.link}>
							<RDSButton
								leadIcon={<LocationIcon css={styles.itemPropertyIcon} />}
								text={location}
								variant="tertiary"
								css={styles.locationButton}
							/>
						</RouterLink>
					</div>
				</>
			)}

			<div css={styles.itemPropertyWrap(locale)}>
				<div css={styles.itemProperty}>
					<DoubleBedIconRB css={styles.itemPropertyIcon} />
					<Typography css={styles.itemProp} itemProp="numberOfBedrooms">
						{attributes.bedrooms}
					</Typography>
				</div>
				<div css={styles.itemProperty} itemProp="bad">
					<BathThubIconRB css={styles.itemPropertyIcon} />
					<Typography css={styles.itemProp} itemProp="numberOfBathroomsTotal">
						{attributes.bathrooms}
					</Typography>
				</div>
				<div css={styles.itemProperty}>
					<FullScreenIcon css={styles.itemPropertyIcon} />
					<Typography css={styles.itemProp} itemProp="floorSize">
						{attributes.plotArea}
					</Typography>
					{t("features.communities.propertyCard.squareUnit")}
				</div>
			</div>
			<Typography css={styles.description}>{attributes.note}</Typography>
		</div>
	);
}
