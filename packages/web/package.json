{"name": "@roshn/web", "packageManager": "yarn@3.5.0", "type": "module", "scripts": {"dev": "remix vite:dev", "dev:msw": "cross-env VITE_ENABLE_MSW=1 remix vite:dev", "prebuild": "bash scripts/prebuild.sh", "build": "yarn prebuild && cross-env NODE_OPTIONS=--max-old-space-size=8192 NODE_ENV=production remix vite:build", "build-ssr": "yarn build", "start": "remix-serve ./build/server/index.js", "preview": "vite preview", "test": "cross-env TZ=UTC vitest", "storybook": "storybook dev -p 6006 --no-open", "build-storybook": "cross-env NODE_OPTIONS=--max-old-space-size=8192 storybook build"}, "dependencies": {"@amplitude/experiment-js-client": "^1.11.0", "@emotion/cache": "^11.10.7", "@emotion/react": "^11.10.6", "@emotion/server": "^11.11.0", "@emotion/styled": "^11.10.6", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-form": "^0.0.3", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-portal": "^1.0.4", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@radix-ui/themes": "^2.0.3", "@react-google-maps/api": "^2.20.5", "@reduxjs/toolkit": "^1.9.7", "@remix-run/node": "^2.8.1", "@remix-run/react": "^2.8.1", "@remix-run/serve": "^2.8.1", "@roshn/shared": "workspace:*", "@roshn/ui-kit": "0.0.0-SNAPSHOT-20250917124819", "@sentry/react": "^8.42.0", "@sentry/remix": "^8.42.0", "@tanstack/react-query": "^4.36.1", "@xstate/react": "^3.2.2", "axios": "^1.4.0", "axios-cache-interceptor": "^1", "bowser": "^2.11.0", "calendar-link": "^2.6.0", "change-case": "^4.1.2", "clsx": "^1.2.1", "delay": "^6.0.0", "firebase": "^10.12.4", "fitty": "^2.3.7", "formik": "^2.2.9", "framer-motion": "^10.12.12", "humanize-duration": "^3.28.0", "i18next": "^22.5.1", "i18next-chained-backend": "^4.6.2", "i18next-fs-backend": "^2.3.1", "ibantools": "^4.5.1", "inversify": "^6.0.1", "is-ua-webview": "^1.1.2", "libphonenumber-js": "^1.10.58", "lodash-es": "^4.17.21", "lottie-react": "^2.4.1", "lucide-react": "^0.475.0", "luxon": "^3.3.0", "mitt": "^3.0.0", "nanoid": "^5.0.4", "p-defer": "^4.0.0", "ramda": "^0.29.0", "react": "^18.2.0", "react-datepicker": "^4.11.0", "react-dom": "^18.2.0", "react-error-boundary": "^4.0.10", "react-google-recaptcha-v3": "^1.10.1", "react-helmet": "^6.1.0", "react-i18next": "^12.2.2", "react-idle-timer": "^5.7.2", "react-laag": "^2.0.5", "react-lazyload": "^3.2.0", "react-loading-skeleton": "^3.3.1", "react-modal-sheet": "^1.10.2", "react-otp-input": "^3.0.0", "react-pdf": "^10.0.1", "react-phone-number-input": "^3.2.19", "react-qr-code": "^2.0.15", "react-redux": "^8.1.1", "react-remark": "^2.1.0", "react-select": "^5.7.3", "react-transition-group": "^4.4.5", "react-virtuoso": "^4.2.2", "react-zoom-pan-pinch": "^3.0.7", "redux": "^4.2.1", "redux-persist": "^6.0.0", "reflect-metadata": "^0.2.1", "rehype-slug": "^6.0.0", "remix-utils": "^7.5.0", "rooks": "^7.14.1", "sitemap": "^7.1.1", "supercluster": "^8.0.1", "swiper": "^11.0.5", "ua-parser-js": "^2.0.3", "use-dehydrated-state": "^0.1.0", "use-detect-keyboard-open": "^0.4.0", "xstate": "^4.38.3", "yup": "^1.1.1"}, "devDependencies": {"@elastic/elasticsearch": "^8.13.0", "@microsoft/fetch-event-source": "^2.0.1", "@remix-run/dev": "^2.8.1", "@remix-run/testing": "^2.8.1", "@storybook/addon-essentials": "^8.0.0", "@storybook/addon-interactions": "^8.0.0", "@storybook/addon-links": "^8.0.0", "@storybook/blocks": "^8.0.0", "@storybook/react": "^8.0.0", "@storybook/react-vite": "^8.0.0", "@storybook/testing-library": "^0.2.2", "@testing-library/dom": "^9.3.4", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "@types/humanize-duration": "^3.27.4", "@types/lodash-es": "^4.17.12", "@types/luxon": "^3.4.2", "@types/ramda": "^0.29.11", "@types/react": "^18.2.65", "@types/react-datepicker": "^4.19.6", "@types/react-dom": "^18.2.22", "@types/react-helmet": "^6.1.11", "@types/react-lazyload": "^3.2.3", "@types/react-test-renderer": "^18.0.7", "@types/react-transition-group": "^4.4.10", "@types/supercluster": "^7.1.3", "@vitejs/plugin-legacy": "^5.3.2", "@vitejs/plugin-react": "^4.2.1", "@vitejs/plugin-react-swc": "^3.6.0", "@vitest/coverage-v8": "^1.3.1", "core-js": "^3.36.0", "cross-env": "^7.0.3", "cypress": "^13.2.0", "javascript-obfuscator": "^4.1.1", "jsdom": "^24.0.0", "msw": "^2.4.6", "prop-types": "^15.8.1", "react-papaparse": "^4.4.0", "react-test-renderer": "^18.2.0", "resize-observer-polyfill": "^1.5.1", "rollup-plugin-obfuscator": "^1.1.0", "rollup-plugin-visualizer": "^5.12.0", "storybook": "^8.0.0", "storybook-addon-remix-react-router": "^3.0.0", "type-fest": "^3.13.1", "typescript": "~5.4.2", "vite": "^5.1.6", "vite-imagetools": "^6.2.9", "vite-plugin-cjs-interop": "^2.1.0", "vite-plugin-lqip": "^0.0.5", "vite-plugin-mkcert": "^1.17.4", "vite-plugin-svgr": "^4.2.0", "vite-tsconfig-paths": "^4.3.2", "vitest": "^1.3.1", "zustand": "^4.5.2"}, "msw": {"workerDirectory": "public"}}