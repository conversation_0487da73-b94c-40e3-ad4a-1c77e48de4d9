{"name": "@roshn/shared", "packageManager": "yarn@3.5.0", "type": "module", "main": "dist/index.cjs", "module": "es/index.js", "typings": "es/index.d.ts", "types": "es/index.d.ts", "version": "1.3.51", "exports": {".": {"import": "./es/index.js", "require": "./dist/index.cjs"}, "./es/*": "./es/*/index.js", "./es/features/*": "./es/features/*/index.js", "./es/services/*": "./es/services/*/index.js"}, "scripts": {"build:es": "rimraf ./es && tsc -p tsconfig.json && tsc-alias -p tsconfig.json", "build:cjs": "rimraf ./dist && rollup -c", "test": "yarn pre-test && vitest", "pre-test": "rm -rf ../../node_modules/canvas && rm -rf node_modules/canvas", "build": "concurrently -n build: \"npm:build:*\"", "dev:es": "nodemon --exec \"npm run build:es\"", "dev:cjs": "rollup -c -w", "dev": "concurrently -n dev: \"npm:dev:*\""}, "nodemonConfig": {"delay": "1.5s", "ext": "ts,tsx", "ignore": ["**/dist/**", "**/es/**"]}, "peerDependencies": {"@tanstack/react-query": "^4.28.0", "axios": "^1.4.0", "inversify": "^6.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "reflect-metadata": "^0.2.1"}, "devDependencies": {"@rollup/plugin-json": "^6.1.0", "@rollup/plugin-typescript": "^11.1.6", "@tanstack/react-query": "^4.36.1", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@types/react": "^18.2.65", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react": "^4.2.1", "concurrently": "^8.2.2", "jsdom": "^24.0.0", "msw": "^1.3.2", "nodemon": "^3.1.0", "reflect-metadata": "^0.2.1", "rimraf": "^5.0.5", "rollup": "^4.13.0", "tsc-alias": "^1.8.8", "type-fest": "^3.13.1", "typescript": "~5.4.2", "vite": "^5.1.6", "vite-tsconfig-paths": "^4.3.2", "vitest": "^1.3.1"}, "dependencies": {"@forgerock/javascript-sdk": "^4.3.0", "@xstate/react": "^3.2.2", "change-case": "^4.1.2", "delay": "^6.0.0", "inversify": "^6.0.1", "jwt-decode": "^3.1.2", "mitt": "^3.0.1", "react-markdown": "^9.0.3", "reflect-metadata": "^0.2.1", "rooks": "^7.14.1", "xstate": "^4.38.3", "zustand": "^4.4.1"}, "files": ["dist", "es"]}