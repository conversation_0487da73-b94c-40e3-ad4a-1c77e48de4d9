import { AppFeatures, createAppFeatureFlags } from "./app-features";
import { inject, injectable } from "inversify";
import { EnvService, Store, StoreFactory, StrapiService } from "../../services";
import {
	AppFeatureFlagsState,
	FeatureFlags,
	FlagFeaturesOnly,
	OptionFeatureNames,
	FeatureFlagState,
	SAFeatureFlagsResponse,
	FlagsType,
} from "./feature-flags";
import { getValueHelper, setValueHelper } from "./helpers";
import { StrapiEntity } from "@/types";

@injectable()
export class FeatureFlagsImpl implements FeatureFlags {
	private readonly appFeatures: AppFeatures;

	public readonly useStore: Store<AppFeatureFlagsState>;
	constructor(
		@inject(EnvService) private envSvc: EnvService,
		@inject(StrapiService) private readonly strapi: StrapiService,
		@inject(StoreFactory)
		private readonly storeFactory: StoreFactory,
	) {
		this.appFeatures = createAppFeatureFlags(envSvc);

		const origin = Object.fromEntries(
			Object.entries(this.appFeatures).map(([key, feature]) => [
				key,
				feature.defaultOption,
			]),
		) as FeatureFlagState;

		this.useStore = this.storeFactory.createStore<
			AppFeatureFlagsState,
			AppFeatureFlagsState["overrides"]
		>(
			(set) => ({
				allowOverride:
					this.envSvc.DEV ||
					this.envSvc.MODE === "dev" ||
					this.envSvc.MODE === "uat",
				origin,
				overrides: {},
			}),
			{
				// TODO: add middlewares to initialize from api
				persist: {
					merge: (persistedState, currentState) => ({
						...currentState,
						overrides: Object.fromEntries(
							Object.entries(persistedState as Partial<FeatureFlagState>).map(
								([key, value]) => [
									key,
									setValueHelper(
										currentState,
										key as keyof FeatureFlagState,
										value,
									),
								],
							),
						) as Partial<FeatureFlagState>,
					}),
					name: "feature",
					partialize: (state) => state.overrides,
					version: 0,
				},
			},
		);
	}

	private setOverrides = (key: keyof FeatureFlagState, value: any) => {
		const currentState = this.useStore.getState();
		this.useStore.setState({
			overrides: {
				...currentState.overrides,
				[key]: setValueHelper(currentState, key, value),
			},
		});
	};

	private getValue = (name: keyof FeatureFlagState) => {
		const state = this.useStore.getState();

		return getValueHelper(state, name);
	};

	getFlag(name: keyof FlagFeaturesOnly): boolean {
		return this.getValue(name) as boolean;
	}

	setFlag(name: keyof FlagFeaturesOnly, value: boolean): this {
		this.setOverrides(name, value);

		return this;
	}

	getOption(name: OptionFeatureNames): FeatureFlagState[typeof name] {
		return this.getValue(name) as FeatureFlagState[typeof name];
	}

	setOption(
		name: OptionFeatureNames,
		value: FeatureFlagState[typeof name],
	): this {
		this.setOverrides(name, value);

		return this;
	}

	async getFeatureFlag(): Promise<string | undefined> {
		const data = await this.strapi.strapiAxios.get("/ha-web-feature-flag");
		const featureFlag = data?.data;
		if (featureFlag) {
			return featureFlag;
		}
		throw Error("Feature Flag not found");
	}
	async getSAFeatureFlag(): Promise<FlagsType | undefined> {
		try {
			const data = await this.strapi.strapiAxios.get<
				StrapiEntity<SAFeatureFlagsResponse>
			>("/sa-frontend-web-feature-flag");
			const featureFlags = data?.data.attributes.flags;
			if (featureFlags) {
				return featureFlags;
			}
			throw Error("Feature Flag not found from SA");
		} catch (error) {
			throw new Error("Feature Flag not found from SA");
		}
	}
}
