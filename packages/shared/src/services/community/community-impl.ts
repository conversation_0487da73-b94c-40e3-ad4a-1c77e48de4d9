import { inject, injectable } from "inversify";
import { CommunityService } from "./community";
import { StrapiService } from "../strapi";
import { GetProjectsResponse } from "./community.types";

@injectable()
class CommunityServiceImpl implements CommunityService {
	constructor(
		@inject(StrapiService)
		private readonly strapiService: StrapiService,
	) {}

	getCommunityDetailQueryKey(communityName: string, locale: string): string[] {
		return [
			`/slugify/slugs/community/${communityName}?populate[backgroundVideo][populate]=media&populate[amenities][populate]=media&populate[masterPlan][populate]=media&populate[dayInLife][populate]=media&populate[philosophy][populate]=media&populate[brochure][*]&populate[communityLogo][*]&populate[dividerPattern][*]&populate[aboutCommunity][populate]=communityInfo.iconImage&populate[heroSection]=*&populate[communityMap]=*&populate[propertyOverview]=*&populate[footer]=*`,
			locale,
		];
	}

	getCommunityDetail(communityName: string, locale?: string): Promise<any> {
		communityName = String(communityName.toLowerCase());
		return this.strapiService.strapiAxios.get(
			`/slugify/slugs/community/${communityName}`,
			{
				params: {
					locale,
					populate: {
						amenities: {
							populate: "media",
						},
						backgroundVideo: {
							populate: "media",
						},
						brochure: "*",
						communityLogo: "*",
						dayInLife: {
							populate: "media",
						},
						dividerPattern: "*",
						masterPlan: {
							populate: "media",
						},
						philosophy: {
							populate: "media",
						},
						seo: {
							populate: ["metaImage", "metaSocial", "metaSocial.image"],
						},
						heroSection: { populate: "*" },
						aboutCommunity: {
							populate: {
								communityInfo: {
									populate: {
										iconImage: {
											populate: "*",
										},
									},
								},
							},
						},
						communityMap: {
							populate: {
								mapImage: {
									populate: "*",
								},
							},
						},
						propertyOverview: {
							populate: {
								phase: {
									populate: "*",
								},
							},
						},
						footer: "*",
					},
				},
			},
		);
	}

	getPropertyGroups(communityName: string): Promise<any> {
		communityName = String(communityName.toLowerCase());
		return this.strapiService.strapiAxios.get(
			`/slugify/slugs/community/${communityName}`,
			{
				params: {
					fields: ["name"],
					populate: {
						propertyGroups: {
							fields: [
								"typology",
								"type",
								"typologyGroup",
								"elevationType",
								"basePrice",
								"status",
								"information",
								"description",
								"project",
							],
							filters: {
								isDisplayInRoshnApp: {
									$eqi: true,
								},
							},
							populate: {
								floorPlanLink: { fields: ["url"] },
								communityPhases: {
									fields: ["*"],
								},
							},
							// Add sorting for displayPosition in ascending order
							sort: ["displayPosition:asc"],
						},
					},
				},
			},
		);
	}

	getCommunitiesMasterPlan(): Promise<any> {
		return this.strapiService.strapiAxios.get(`/media-wrappers`, {
			params: {
				filters: {
					tag: {
						$eq: "masterplan",
					},
				},
				populate: "*",
				sort: ["id"],
			},
		});
	}

	getCommunities(locale?: string): Promise<any> {
		return this.strapiService.strapiAxios.get("/communities", {
			params: {
				fields: ["name", "city", "metadata"],
				locale,
			},
		});
	}

	getProjects(): Promise<GetProjectsResponse> {
		return Promise.resolve({
			alarous: ["alarous_1a"],
			aldanah: ["aldanah_1"],
			almanar: ["almanar_1"],
			sedra: ["sedra_3", "sedra_4a", "sedra_2a", "sedra_5"],
			warefa: ["warefa_1"],
		});
	}
}

export { CommunityServiceImpl };
