import {
	StrapiMedia,
	StrapiEntity,
	StrapiResponseData,
} from "../../types/strapi-response";
import { CommunityEntity } from "../community/community.types";
import { Merge } from "type-fest";

export type CommunityPhaseEntity = {
	phaseName: string;
	createdAt: string;
	updatedAt: string;
	publishedAt: string;
};

export type Property = StrapiEntity<
	Merge<
		PropertyGroupInfo,
		Merge<
			PropertyGroupImages,
			{
				[ViewSetKey: `exterior3dImages${number}`]: {
					data: StrapiMedia[];
				};
				community?: {
					data: StrapiEntity<CommunityEntity>;
				};
				description: string;

				numBathrooms: number;
				numBedrooms: number;
				paymentPlan?: any;
			}
		>
	>,
	{
		// mapping for facade type with image property
		facadeMap: {
			"3dSetKey": `exterior3dImages${number}`;
			crmElevationType: string;
			displayName: string;
		}[];
		virtualTourUrl?: string;
	} | null
>;

export type PropertyGroupInfo = {
	area: number;
	basePrice: number;
	project: string;
	communityPhases?: {
		data: StrapiEntity<CommunityPhaseEntity>[];
	};
	description: string;
	elevationType: string;
	floorPlanLink?: { data: StrapiMedia | null };
	information?: {
		additionalRooms?: number;
		bathRoom?: number;
		bedroom?: number;
		builtUpArea?: number;
		driverRoom?: string;
		grossFloorArea?: number;
		landArea?: number;
		laundryRoom?: number;
		maidRoom?: string;
		note?: string;
		parking?: number;
		balcony?: string;
		plotArea?: number;
		terrace?: string;
	};
	status: "LOW_AVAILABILITY" | "AVAILABLE" | "UNAVAILABLE";
	type: string;
	typology: string;
	typologyGroup: string;
};

export type PropertyGroupImages = {
	// array of image for exteriorPreview
	// each image is for separate facade
	exteriorPreview?: {
		data: StrapiMedia[] | null;
	};
	// array of images for generic purpose
	images?: {
		data: StrapiMedia[] | null;
	};
	// array of image for top views (floor plan)
	topViewImages?: {
		data: StrapiMedia[] | null;
	};
};

export type GetPropertyGroupResponse = StrapiResponseData<Property>;
export type GetPropertyGroupImagesResponse = StrapiResponseData<
	StrapiEntity<PropertyGroupImages>
>;
export type PropertyGroupInfoResponse = StrapiEntity<PropertyGroupInfo>;
